package uiagentreporterlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyUIAgentComponentRecordLogic struct {
	*BaseLogic
}

func NewModifyUIAgentComponentRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyUIAgentComponentRecordLogic {
	return &ModifyUIAgentComponentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyUIAgentComponentRecord 修改`UI Agent`组件执行记录
func (l *ModifyUIAgentComponentRecordLogic) ModifyUIAgentComponentRecord(in *pb.ModifyUIAgentComponentRecordReq) (
	out *pb.ModifyUIAgentComponentRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockUIAgentComponentRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID,
		projectID,
	)
	fn := func() error {
		origin, err := model.CheckUIAgentComponentExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.UiAgentComponentExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			return err
		}

		var (
			successes = int64(in.GetSuccesses())

			status   = origin.Status
			costTime = origin.CostTime
			endedAt  = origin.EndedAt
			errMsg   = origin.ErrMsg

			now = time.Now()
		)

		if (!origin.ParentExecuteId.Valid || origin.ParentExecuteId.String == "") && origin.Times > 1 {
			subRecords, _ := l.svcCtx.UiAgentComponentExecutionRecordModel.FindAllByParent(
				l.ctx, model.SearchUIAgentComponentExecutionRecordByParentReq{
					BaseSearchReq: model.BaseSearchReq{
						ProjectID: projectID,
					},
					TaskID:          taskID,
					ParentExecuteID: executeID,
				},
			)

			var count int64
			for _, subRecord := range subRecords {
				count += subRecord.Successes
			}

			if count != successes {
				l.Warnf(
					"the successes of ui agent component execution record is not equal to the sum of sub records, task_id: %s, execute_id: %s, project_id: %s, successes: %d, sub_records: %d",
					taskID, executeID, projectID, successes, count,
				)
			}
			successes = count

			if count == origin.Times {
				in.Status = dispatcherpb.ComponentState_Success.String()
			}
		}

		if in.GetStatus() != "" {
			status.String = in.GetStatus()
			status.Valid = true
		}

		if in.GetEndedAt() != nil && in.GetEndedAt().IsValid() && in.GetEndedAt().GetSeconds() > 0 {
			if endTime := in.GetEndedAt().AsTime(); !endTime.IsZero() {
				costTime = endTime.Sub(origin.StartedAt.Time).Milliseconds()
				endedAt.Time = endTime
				endedAt.Valid = true
			}
		}

		if in.GetErrMsg() != nil {
			s, err := protobuf.MarshalJSONToString(in.GetErrMsg())
			if err != nil {
				l.Errorf(
					"failed to unmarshal the error message of ui agent component execution record, task_id: %s, execute_id: %s, project_id: %s, error_message: %+v, error: %+v",
					taskID, executeID, projectID, in.GetErrMsg(), err,
				)
			} else {
				errMsg.String = s
				errMsg.Valid = s != ""
			}
		}

		updatedBy := l.currentUser.Account
		if l.currentUser.Account == systemUser.Account {
			updatedBy = origin.UpdatedBy
		}

		record := &model.UiAgentComponentExecutionRecord{
			Id:                origin.Id,
			TaskId:            origin.TaskId,
			ExecuteId:         origin.ExecuteId,
			ParentExecuteId:   origin.ParentExecuteId,
			ProjectId:         origin.ProjectId,
			ComponentId:       origin.ComponentId,
			ComponentName:     origin.ComponentName,
			TriggerMode:       origin.TriggerMode,
			ExecuteType:       origin.ExecuteType,
			ApplicationConfig: origin.ApplicationConfig,
			Steps:             origin.Steps,
			Expectation:       origin.Expectation,
			Variables:         origin.Variables,
			Device:            origin.Device,
			Reinstall:         origin.Reinstall,
			Restart:           origin.Restart,
			ReferenceId:       origin.ReferenceId,
			Times:             origin.Times,
			Successes:         successes,
			Status:            status,
			CostTime:          costTime,
			ExecutedBy:        origin.ExecutedBy,
			StartedAt:         origin.StartedAt,
			EndedAt:           endedAt,
			ErrMsg:            errMsg,
			Cleaned:           origin.Cleaned,
			Deleted:           origin.Deleted,
			CreatedBy:         origin.CreatedBy,
			UpdatedBy:         updatedBy,
			CreatedAt:         origin.CreatedAt,
			UpdatedAt:         now,
		}

		return l.svcCtx.UiAgentComponentExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.UiAgentComponentExecutionRecordModel.Update(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify ui agent component execution record, task_id: %s, execute_id: %s, project_id: %s, component_id: %s, error: %+v",
						taskID, executeID, projectID, in.GetComponentId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.ModifyUIAgentComponentRecordResp{}, nil
}
