// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiAgentComponentExecutionRecordTableName           = "`ui_agent_component_execution_record`"
	uiAgentComponentExecutionRecordFieldNames          = builder.RawFieldNames(&UiAgentComponentExecutionRecord{})
	uiAgentComponentExecutionRecordRows                = strings.Join(uiAgentComponentExecutionRecordFieldNames, ",")
	uiAgentComponentExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(uiAgentComponentExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiAgentComponentExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(uiAgentComponentExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	uiAgentComponentExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiAgentComponentExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiAgentComponentExecutionRecord, error)
		FindOneByTaskIdExecuteIdProjectId(ctx context.Context, taskId string, executeId string, projectId string) (*UiAgentComponentExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *UiAgentComponentExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiAgentComponentExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UiAgentComponentExecutionRecord struct {
		Id                int64          `db:"id"`                 // 自增ID
		TaskId            string         `db:"task_id"`            // 任务ID
		ExecuteId         string         `db:"execute_id"`         // 执行ID
		ParentExecuteId   sql.NullString `db:"parent_execute_id"`  // 父执行ID
		ProjectId         string         `db:"project_id"`         // 项目ID
		ComponentId       string         `db:"component_id"`       // 组件ID
		ComponentName     string         `db:"component_name"`     // 组件名称
		TriggerMode       string         `db:"trigger_mode"`       // 触发模式（手动、定时、接口）
		ExecuteType       int64          `db:"execute_type"`       // 执行类型（执行、调试）
		ApplicationConfig string         `db:"application_config"` // 应用配置
		Mode              int64          `db:"mode"`               // 模式（Agent模式、Step模式）
		Steps             string         `db:"steps"`              // 步骤
		Expectation       string         `db:"expectation"`        // 期望结果
		Variables         string         `db:"variables"`          // 变量列表
		ForegroundCheck   int64          `db:"foreground_check"`   // 是否检查App在前台
		Device            string         `db:"device"`             // 设备信息
		Reinstall         int64          `db:"reinstall"`          // 是否重新安装
		Restart           int64          `db:"restart"`            // 是否重启应用
		ReferenceId       sql.NullString `db:"reference_id"`       // 参考配置ID
		Times             int64          `db:"times"`              // 执行次数
		Successes         int64          `db:"successes"`          // 执行成功次数
		Status            sql.NullString `db:"status"`             // 执行状态（结果）
		ExecutedBy        string         `db:"executed_by"`        // 执行者的用户ID
		StartedAt         sql.NullTime   `db:"started_at"`         // 开始时间
		EndedAt           sql.NullTime   `db:"ended_at"`           // 结束时间
		CostTime          int64          `db:"cost_time"`          // 执行耗时（单位为毫秒）
		ErrMsg            sql.NullString `db:"err_msg"`            // 计划执行错误信息
		Cleaned           int64          `db:"cleaned"`            // 清理标识（未清理、已清理）
		Deleted           int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		CreatedBy         string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newUiAgentComponentExecutionRecordModel(conn sqlx.SqlConn) *defaultUiAgentComponentExecutionRecordModel {
	return &defaultUiAgentComponentExecutionRecordModel{
		conn:  conn,
		table: "`ui_agent_component_execution_record`",
	}
}

func (m *defaultUiAgentComponentExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUiAgentComponentExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultUiAgentComponentExecutionRecordModel) FindOne(ctx context.Context, id int64) (*UiAgentComponentExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiAgentComponentExecutionRecordRows, m.table)
	var resp UiAgentComponentExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentComponentExecutionRecordModel) FindOneByTaskIdExecuteIdProjectId(ctx context.Context, taskId string, executeId string, projectId string) (*UiAgentComponentExecutionRecord, error) {
	var resp UiAgentComponentExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `execute_id` = ? and `project_id` = ? and `deleted` = ? limit 1", uiAgentComponentExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, executeId, projectId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentComponentExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *UiAgentComponentExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiAgentComponentExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ParentExecuteId, data.ProjectId, data.ComponentId, data.ComponentName, data.TriggerMode, data.ExecuteType, data.ApplicationConfig, data.Mode, data.Steps, data.Expectation, data.Variables, data.ForegroundCheck, data.Device, data.Reinstall, data.Restart, data.ReferenceId, data.Times, data.Successes, data.Status, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ParentExecuteId, data.ProjectId, data.ComponentId, data.ComponentName, data.TriggerMode, data.ExecuteType, data.ApplicationConfig, data.Mode, data.Steps, data.Expectation, data.Variables, data.ForegroundCheck, data.Device, data.Reinstall, data.Restart, data.ReferenceId, data.Times, data.Successes, data.Status, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultUiAgentComponentExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *UiAgentComponentExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiAgentComponentExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ParentExecuteId, newData.ProjectId, newData.ComponentId, newData.ComponentName, newData.TriggerMode, newData.ExecuteType, newData.ApplicationConfig, newData.Mode, newData.Steps, newData.Expectation, newData.Variables, newData.ForegroundCheck, newData.Device, newData.Reinstall, newData.Restart, newData.ReferenceId, newData.Times, newData.Successes, newData.Status, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ParentExecuteId, newData.ProjectId, newData.ComponentId, newData.ComponentName, newData.TriggerMode, newData.ExecuteType, newData.ApplicationConfig, newData.Mode, newData.Steps, newData.Expectation, newData.Variables, newData.ForegroundCheck, newData.Device, newData.Reinstall, newData.Restart, newData.ReferenceId, newData.Times, newData.Successes, newData.Status, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultUiAgentComponentExecutionRecordModel) tableName() string {
	return m.table
}
