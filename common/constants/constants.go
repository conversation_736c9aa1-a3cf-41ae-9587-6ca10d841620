package constants

import "time"

const (
	ConstQualityPlatformName = "Probe"

	ConstDefaultGitUsername = "probe"
	ConstDefaultBranchName  = "default"

	ConstDefaultMakeMapSize     = 64
	ConstDefaultMakeSliceSize   = 64
	ConstDefaultMakeChannelSize = 1

	ConstMaxPerfTestSerialSteps             = 20               // 最大的压测串行步骤数
	ConstMaxPerfTestParallelSteps           = 10               // 最大的压测并行步骤数
	ConstMaxPerfTestStepSleepTime           = 10 * time.Second // 最大的压测步骤休眠时间
	ConstDividePerfTestStepRPS              = 500              // 压测步骤的分水岭RPS
	ConstMaxPerfTestStepWithDivRPSSleepTime = 2 * time.Second  // 最大的压测步骤分水岭休眠时间

	ConstWorkingTimeRangeOfLeft  = "9:00"
	ConstWorkingTimeRangeOfRight = "18:00"

	ConstDefaultCSVDelimiter rune = '|'
	ConstAddressSeparator         = ","

	ConstSuffixOfApk = ".apk"
	ConstSuffixOfIpa = ".ipa"
	ConstSuffixOfPng = ".png"

	ConstStoragePathOfStabilityTest = "stability_test"
	ConstStoragePathOfUIAgentTest   = "ui_agent_test"
	ConstStoragePathOfFiles         = "files"
	ConstStoragePathOfApps          = "apps"
	ConstStoragePathOfReports       = "reports"

	ConstPort80     = 80
	ConstPort8080   = 8080
	ConstUSBMuxPort = 27015
)

// TaskResult 任务结果
type TaskResult = string

const (
	SUCCESS TaskResult = "success"
	FAILURE TaskResult = "failure"
	SKIPPED TaskResult = "skipped"
)

// FunctionType 函数类型
type FunctionType = string

const (
	BUILTIN FunctionType = "BUILTIN" // 内置
	CUSTOM  FunctionType = "CUSTOM"  // 自定义
)

// ProgrammingLanguage 编程语言
type ProgrammingLanguage = string

const (
	PYTHON ProgrammingLanguage = "PYTHON" // Python
	GOLANG ProgrammingLanguage = "GOLANG" // Golang
)

// FunctionCategory 函数分类
type FunctionCategory = string

const (
	String         FunctionCategory = "String"
	Number         FunctionCategory = "Number"
	Date           FunctionCategory = "Date"
	Encoding       FunctionCategory = "Encoding"
	List           FunctionCategory = "List"
	Dictionary     FunctionCategory = "Dictionary"
	TypeConversion FunctionCategory = "Type Conversion"
	Path           FunctionCategory = "Path"
	Other          FunctionCategory = "Other"
)

// ParameterType 参数类型
type ParameterType = string

const (
	STRING  ParameterType = "STRING"
	NUMBER  ParameterType = "NUMBER"
	ARRAY   ParameterType = "ARRAY"
	OBJECT  ParameterType = "OBJECT"
	BOOLEAN ParameterType = "BOOLEAN"
	NULL    ParameterType = "NULL"
	ANY     ParameterType = "ANY"
)

// ElementType 元素类型
type ElementType = string

const (
	NODE  ElementType = "NODE"  // 点
	EDGE  ElementType = "EDGE"  // 线
	COMBO ElementType = "COMBO" // 框
)

// ComponentType 组件类型
type ComponentType = string

const (
	START                ComponentType = "START"                  // 开始
	END                  ComponentType = "END"                    // 结束
	SETUP                ComponentType = "SETUP"                  // 前置
	TEARDOWN             ComponentType = "TEARDOWN"               // 后置
	SINGLE               ComponentType = "BUSINESS_SINGLE"        // 业务单请求
	GROUP                ComponentType = "BUSINESS_GROUP"         // 业务行为组
	HTTP                 ComponentType = "HTTP"                   // HTTP请求
	PRECISIONTESTINGHTTP ComponentType = "PRECISION_TESTING_HTTP" // 精准测试HTTP请求
	REFERENCE            ComponentType = "COMPONENT_GROUP"        // 组件组
	CONDITION            ComponentType = "CONDITION"              // 条件
	LOOP                 ComponentType = "LOOP"                   // 循环
	WAIT                 ComponentType = "WAIT"                   // 等待
	ASSERT               ComponentType = "ASSERT"                 // 断言
	ACCOUNT              ComponentType = "POOL_ACCOUNT"           // 池账号
	PARALLEL             ComponentType = "PARALLEL"               // 并行
	PROCESSING           ComponentType = "DATA_PROCESSING"        // 数据处理
	DRIVEN               ComponentType = "DATA_DRIVEN"            // 数据驱动
	SQL                  ComponentType = "SQL_EXECUTION"          // SQL执行
)

// ComponentParentType 组件的父对象的类型
type ComponentParentType = string

const (
	ComponentGroup ComponentParentType = "COMPONENT_GROUP" // 组件组
	ApiCase        ComponentParentType = "API_CASE"        // API用例
	ApiSuite       ComponentParentType = "API_SUITE"       // API集合
	ApiPlan        ComponentParentType = "API_PLAN"        // API计划
	InterfaceCase  ComponentParentType = "INTERFACE_CASE"  // 接口用例
)

// ExecutionMode 执行方式
type ExecutionMode = int8

const (
	NullExecutionMode ExecutionMode = iota // 0: Null（正常情况下不使用）
	Parallel                               // 1: 并行
	Serial                                 // 2: 串行
)

// CleanMode 清理模式
type CleanMode = string

// 定时清理类型，目前为按时间清理，按数量保留
const (
	CleanModeTime   CleanMode = "time"
	CleanModeNumber CleanMode = "number"
)

// CleanType 清理类型
type CleanType = string

// 能清理的类型，与执行类型一致
const (
	CleanTypeApiPlan           CleanType = "API_PLAN"
	CleanTypeApiSuite          CleanType = "API_SUITE"
	CleanTypeInterfaceDocument CleanType = "INTERFACE_DOCUMENT"
	CleanTypeApiCase           CleanType = "API_CASE"
	CleanTypeInterfaceCase     CleanType = "INTERFACE_CASE"
	CleanTypeApiComponentGroup CleanType = "API_COMPONENT_GROUP"
	CleanTypeUiPlan            CleanType = "UI_PLAN" // UI测试目前只有计划级别
	CleanTypePerfPlan          CleanType = "PERF_PLAN"
	CleanTypeStabilityPlan     CleanType = "STABILITY_PLAN"
	CleanTypeUIAgentComponent  CleanType = "UI_AGENT_COMPONENT"
)

// TestArgType 测试附加参数类型
type TestArgType = string

// UI测试附加参数类型，目前列举了大部分，包括一些不会用到的
const (
	TestArgProjectId            TestArgType = "$projectId"
	TestArgTaskId               TestArgType = "$taskId"
	TestArgPlanId               TestArgType = "$planId"
	TestArgName                 TestArgType = "$name"
	TestArgDescription          TestArgType = "$description"
	TestArgExecuteType          TestArgType = "$executeType"
	TestArgCronExpression       TestArgType = "$cronExpression"
	TestArgGitConfigId          TestArgType = "$gitConfigId"
	TestArgExecutionMode        TestArgType = "$executionMode"
	TestArgPlatformType         TestArgType = "$platformType"
	TestArgPackageName          TestArgType = "$packageName"
	TestArgCallbackUrl          TestArgType = "$callbackUrl"
	TestArgAppDownloadLink      TestArgType = "$appDownloadLink"
	TestArgAppVersion           TestArgType = "$appVersion"
	TestArgAppName              TestArgType = "$appName"
	TestArgTestLanguage         TestArgType = "$testLanguage"
	TestArgTestLanguageVersion  TestArgType = "$testLanguageVersion"
	TestArgTestFramework        TestArgType = "$testFramework"
	TestArgExecutionEnvironment TestArgType = "$executionEnvironment"
	TestArgFailRetry            TestArgType = "$failRetry"
	TestArgState                TestArgType = "$state"
	TestArgTestDataSavePath     TestArgType = "$testDataSavePath"
	TestArgDeviceRemoteAddress  TestArgType = "$deviceRemoteAddress"
	TestArgUdid                 TestArgType = "$udid"
)

// CaseType 用例类型
type CaseType = string

const (
	CaseTypeApiCase       CaseType = "API_CASE"
	CaseTypeInterfaceCase CaseType = "INTERFACE_CASE"
)

type PerfExecuteState string

const (
	// PerfExecuteStatePreview 预执行
	PerfExecuteStatePreview PerfExecuteState = "Preview"
	// PerfExecuteStateRunning 运行中
	PerfExecuteStateRunning PerfExecuteState = "Running"
)

func (x PerfExecuteState) String() string {
	return string(x)
}

// LarkCardCallbackType 飞书卡片回调类型
type LarkCardCallbackType string

const (
	LarkCardCallbackTypeReleaseDevice LarkCardCallbackType = "ReleaseDevice" // 释放设备
)

type MethodType string

const (
	MethodTypeOfGRPC MethodType = "gRPC"
	MethodTypeOfHTTP MethodType = "HTTP"
)

type SlaThresholdName string

const (
	SLA_Threshold_Finish_Launched SlaThresholdName = "finish_launched_line"
	SLA_Threshold_Auto_Login      SlaThresholdName = "auto_login_line"
	SLA_Threshold_New_Home_Page   SlaThresholdName = "new_home_page_line"
)
