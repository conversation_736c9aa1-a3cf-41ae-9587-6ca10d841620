package clickPilot

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

var (
	_ IClient = (*Client)(nil)

	headerOfApplicationJSONContentType = http.Header{"Content-Type": {"application/json"}}

	nullTaskStatusData = TaskStatusData{
		Status:  TaskStatusOfNull,
		Message: "",
	}
)

// Client ClickPilot客户端
type Client struct {
	c    *fasthttp.Client
	conf Config
}

// NewClient 创建ClickPilot客户端
func NewClient(conf Config) IClient {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) send(apiName string, req *fasthttp.Request, resp *fasthttp.Response) (
	body []byte, err error,
) {
	if err = c.c.Send(req, resp, requestTimeout); err != nil {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of ClickPilot, api: %s, error: %+v",
			apiName, err,
		)
	}

	body = resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of ClickPilot, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	return body, err
}

// OptimizeTaskSteps 优化任务步骤
func (c *Client) OptimizeTaskSteps(steps string) (string, error) {
	apiName := optimizeTaskStepsAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(&optimizeTaskStepsReq{Steps: steps})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return steps, err
	}

	var out optimizeTaskStepsResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return steps, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return steps, NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to optimize task steps, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return out.Data.Steps, nil
}

// CreateUITask 创建UI任务
func (c *Client) CreateUITask(task *UITask) error {
	if task == nil {
		return errorx.Err(errorx.ValidateParamError, "the task is null")
	}
	if task.AgentType == "" {
		task.AgentType = AgentTypeOfAndroid
	}
	if task.AgentConfigID == "" {
		task.AgentConfigID = defaultAgentConfigID
	}

	apiName := createUITaskAPIName
	if task.ReferenceTaskID != "" {
		apiName = executeRefTaskAPIName
	}
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(task)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out createUITaskResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to create ui task, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return nil
}

// GetTaskStatus 查询任务状态
func (c *Client) GetTaskStatus(taskID string) (TaskStatusData, error) {
	apiName := getTaskStatusAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodGet),
		fasthttp.AddQueryArgs(queryOfTaskID, taskID),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nullTaskStatusData, err
	}

	var out getTaskStatusResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nullTaskStatusData, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return nullTaskStatusData, NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to get task status, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return TaskStatusData{
		Status:  out.Data.Status,
		Message: out.Data.Message,
	}, nil
}

// GetTaskRecord 查询任务执行步骤记录
func (c *Client) GetTaskRecord(taskID string) ([]*StepRecord, error) {
	apiName := getTaskRecordAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodGet),
		fasthttp.AddQueryArgs(queryOfTaskID, taskID),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out getTaskRecordResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return nil, NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to get task record, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	steps := make([]*StepRecord, 0, len(out.Data))
	for _, item := range out.Data {
		steps = append(
			steps, &StepRecord{
				StepID:    item.ID,
				Index:     item.Number,
				Name:      item.StepName,
				Thought:   item.Thought,
				Action:    item.Action,
				Status:    item.Status,
				Image:     item.ImagePath,
				StartedAt: item.StartTime,
				EndedAt:   item.EndTime,
				CostTime:  item.Cost,
			},
		)
	}

	return steps, nil
}

// GetTaskLog 查询任务日志
func (c *Client) GetTaskLog(taskID string) (string, error) {
	apiName := getTaskLogAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodGet),
		fasthttp.AddQueryArgs(queryOfTaskID, taskID),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return "", err
	}

	var out getTaskLogResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return "", errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return "", NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to get task log, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return out.Data, nil
}

// StopTask 停止任务
func (c *Client) StopTask(taskID string) error {
	apiName := stopTaskAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(&stopTaskReq{TaskID: taskID})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out stopTaskResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to stop task, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	} else if !out.Data.Success {
		return NewError(
			out.Code, out.Data.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to stop task, api: %s, message: %s",
				apiName, out.Data.Message,
			),
		)
	}

	return nil
}

// DeleteTask 删除任务
func (c *Client) DeleteTask(taskID string) error {
	apiName := deleteTaskAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodDelete),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.AddQueryArgs(queryOfTaskID, taskID),
		// fasthttp.SetBody(jsonx.MarshalIgnoreError(&deleteTaskReq{TaskID: taskID})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out deleteTaskResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to delete task, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	} else if !out.Data.Success {
		return NewError(
			out.Code, out.Data.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to delete task, api: %s, message: %s",
				apiName, out.Data.Message,
			),
		)
	}

	return nil
}

// CreateRefCase 创建参考用例
func (c *Client) CreateRefCase(ref *RefCase) error {
	if ref == nil {
		return errorx.Err(errorx.ValidateParamError, "the ref case is null")
	}

	apiName := createRefCaseAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(ref)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out createRefCaseResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to create ref case, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return nil
}

// DeleteRefCase 删除参考用例
func (c *Client) DeleteRefCase(taskID string) error {
	apiName := deleteRefCaseAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodDelete),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.AddQueryArgs(queryOfTaskID, taskID),
		// fasthttp.SetBody(jsonx.MarshalIgnoreError(&deleteRefCaseReq{TaskID: taskID})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out deleteRefCaseResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to delete ref case, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return nil
}

// DeleteRefCaseStep 删除参考用例步骤
func (c *Client) DeleteRefCaseStep(stepIDs ...int64) error {
	apiName := deleteRefCaseStepAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodDelete),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(&deleteRefCaseStepReq{ActionIDs: stepIDs})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out deleteRefCaseStepResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return NewError(
			out.Code, out.Message, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to delete ref case step, api: %s, code: %d, message: %s",
				apiName, out.Code, out.Message,
			),
		)
	}

	return nil
}
