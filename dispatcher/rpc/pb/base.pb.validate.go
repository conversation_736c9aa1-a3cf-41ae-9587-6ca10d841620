// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/base.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.ApiExecutionDataType(0)

	_ = pb1.TriggerMode(0)
)

// Validate checks the field values on ComponentKey with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ComponentKey) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComponentKey with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ComponentKeyMultiError, or
// nil if none found.
func (m *ComponentKey) ValidateAll() error {
	return m.validate(true)
}

func (m *ComponentKey) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentId

	// no validation rules for ComponentType

	// no validation rules for Version

	if len(errors) > 0 {
		return ComponentKeyMultiError(errors)
	}

	return nil
}

// ComponentKeyMultiError is an error wrapping multiple validation errors
// returned by ComponentKey.ValidateAll() if the designated constraints aren't met.
type ComponentKeyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentKeyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentKeyMultiError) AllErrors() []error { return m }

// ComponentKeyValidationError is the validation error returned by
// ComponentKey.Validate if the designated constraints aren't met.
type ComponentKeyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentKeyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentKeyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentKeyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentKeyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentKeyValidationError) ErrorName() string { return "ComponentKeyValidationError" }

// Error satisfies the builtin error interface
func (e ComponentKeyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponentKey.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentKeyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentKeyValidationError{}

// Validate checks the field values on ComponentExecuteKey with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComponentExecuteKey) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComponentExecuteKey with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComponentExecuteKeyMultiError, or nil if none found.
func (m *ComponentExecuteKey) ValidateAll() error {
	return m.validate(true)
}

func (m *ComponentExecuteKey) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetKey()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComponentExecuteKeyValidationError{
					field:  "Key",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComponentExecuteKeyValidationError{
					field:  "Key",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKey()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComponentExecuteKeyValidationError{
				field:  "Key",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComponentExecuteId

	// no validation rules for State

	if len(errors) > 0 {
		return ComponentExecuteKeyMultiError(errors)
	}

	return nil
}

// ComponentExecuteKeyMultiError is an error wrapping multiple validation
// errors returned by ComponentExecuteKey.ValidateAll() if the designated
// constraints aren't met.
type ComponentExecuteKeyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentExecuteKeyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentExecuteKeyMultiError) AllErrors() []error { return m }

// ComponentExecuteKeyValidationError is the validation error returned by
// ComponentExecuteKey.Validate if the designated constraints aren't met.
type ComponentExecuteKeyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentExecuteKeyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentExecuteKeyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentExecuteKeyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentExecuteKeyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentExecuteKeyValidationError) ErrorName() string {
	return "ComponentExecuteKeyValidationError"
}

// Error satisfies the builtin error interface
func (e ComponentExecuteKeyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponentExecuteKey.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentExecuteKeyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentExecuteKeyValidationError{}

// Validate checks the field values on TaskParams with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskParams with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskParamsMultiError, or
// nil if none found.
func (m *TaskParams) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ExecuteType

	if all {
		switch v := interface{}(m.GetParentComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskParamsValidationError{
					field:  "ParentComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskParamsValidationError{
					field:  "ParentComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParentComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskParamsValidationError{
				field:  "ParentComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskParamsValidationError{
					field:  "CurrentComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskParamsValidationError{
					field:  "CurrentComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskParamsValidationError{
				field:  "CurrentComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskParamsValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskParamsValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskParamsValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskParamsValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskParamsValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskParamsValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for User

	// no validation rules for UserId

	// no validation rules for State

	for idx, item := range m.GetInterfaceCaseIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskParamsValidationError{
						field:  fmt.Sprintf("InterfaceCaseIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskParamsValidationError{
						field:  fmt.Sprintf("InterfaceCaseIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskParamsValidationError{
					field:  fmt.Sprintf("InterfaceCaseIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SuiteExecutionMode

	// no validation rules for CaseExecutionMode

	// no validation rules for Debug

	if len(errors) > 0 {
		return TaskParamsMultiError(errors)
	}

	return nil
}

// TaskParamsMultiError is an error wrapping multiple validation errors
// returned by TaskParams.ValidateAll() if the designated constraints aren't met.
type TaskParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskParamsMultiError) AllErrors() []error { return m }

// TaskParamsValidationError is the validation error returned by
// TaskParams.Validate if the designated constraints aren't met.
type TaskParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskParamsValidationError) ErrorName() string { return "TaskParamsValidationError" }

// Error satisfies the builtin error interface
func (e TaskParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskParamsValidationError{}

// Validate checks the field values on ApiPlanInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApiPlanInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApiPlanInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ApiPlanInfoMultiError, or
// nil if none found.
func (m *ApiPlanInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ApiPlanInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ApiPlanInfoMultiError(errors)
	}

	return nil
}

// ApiPlanInfoMultiError is an error wrapping multiple validation errors
// returned by ApiPlanInfo.ValidateAll() if the designated constraints aren't met.
type ApiPlanInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApiPlanInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApiPlanInfoMultiError) AllErrors() []error { return m }

// ApiPlanInfoValidationError is the validation error returned by
// ApiPlanInfo.Validate if the designated constraints aren't met.
type ApiPlanInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApiPlanInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApiPlanInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApiPlanInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApiPlanInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApiPlanInfoValidationError) ErrorName() string { return "ApiPlanInfoValidationError" }

// Error satisfies the builtin error interface
func (e ApiPlanInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApiPlanInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApiPlanInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApiPlanInfoValidationError{}

// Validate checks the field values on UiPlanInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UiPlanInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UiPlanInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UiPlanInfoMultiError, or
// nil if none found.
func (m *UiPlanInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UiPlanInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppDownloadUrl

	// no validation rules for AppVersion

	// no validation rules for Together

	if len(errors) > 0 {
		return UiPlanInfoMultiError(errors)
	}

	return nil
}

// UiPlanInfoMultiError is an error wrapping multiple validation errors
// returned by UiPlanInfo.ValidateAll() if the designated constraints aren't met.
type UiPlanInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UiPlanInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UiPlanInfoMultiError) AllErrors() []error { return m }

// UiPlanInfoValidationError is the validation error returned by
// UiPlanInfo.Validate if the designated constraints aren't met.
type UiPlanInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UiPlanInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UiPlanInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UiPlanInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UiPlanInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UiPlanInfoValidationError) ErrorName() string { return "UiPlanInfoValidationError" }

// Error satisfies the builtin error interface
func (e UiPlanInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUiPlanInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UiPlanInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UiPlanInfoValidationError{}

// Validate checks the field values on PerfPlanInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfPlanInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfPlanInfoMultiError, or
// nil if none found.
func (m *PerfPlanInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExecuteType

	// no validation rules for EstimatedTime

	// no validation rules for SendPreviewNotification

	if len(errors) > 0 {
		return PerfPlanInfoMultiError(errors)
	}

	return nil
}

// PerfPlanInfoMultiError is an error wrapping multiple validation errors
// returned by PerfPlanInfo.ValidateAll() if the designated constraints aren't met.
type PerfPlanInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanInfoMultiError) AllErrors() []error { return m }

// PerfPlanInfoValidationError is the validation error returned by
// PerfPlanInfo.Validate if the designated constraints aren't met.
type PerfPlanInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanInfoValidationError) ErrorName() string { return "PerfPlanInfoValidationError" }

// Error satisfies the builtin error interface
func (e PerfPlanInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanInfoValidationError{}

// Validate checks the field values on StopDetailOfPerfStopRule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopDetailOfPerfStopRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopDetailOfPerfStopRule with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopDetailOfPerfStopRuleMultiError, or nil if none found.
func (m *StopDetailOfPerfStopRule) ValidateAll() error {
	return m.validate(true)
}

func (m *StopDetailOfPerfStopRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MetricType

	// no validation rules for Service

	// no validation rules for Namespace

	// no validation rules for Method

	// no validation rules for Threshold

	// no validation rules for ReachedAt

	// no validation rules for LatestAt

	for idx, item := range m.GetPoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StopDetailOfPerfStopRuleValidationError{
						field:  fmt.Sprintf("Points[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StopDetailOfPerfStopRuleValidationError{
						field:  fmt.Sprintf("Points[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StopDetailOfPerfStopRuleValidationError{
					field:  fmt.Sprintf("Points[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StopDetailOfPerfStopRuleMultiError(errors)
	}

	return nil
}

// StopDetailOfPerfStopRuleMultiError is an error wrapping multiple validation
// errors returned by StopDetailOfPerfStopRule.ValidateAll() if the designated
// constraints aren't met.
type StopDetailOfPerfStopRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopDetailOfPerfStopRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopDetailOfPerfStopRuleMultiError) AllErrors() []error { return m }

// StopDetailOfPerfStopRuleValidationError is the validation error returned by
// StopDetailOfPerfStopRule.Validate if the designated constraints aren't met.
type StopDetailOfPerfStopRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopDetailOfPerfStopRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopDetailOfPerfStopRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopDetailOfPerfStopRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopDetailOfPerfStopRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopDetailOfPerfStopRuleValidationError) ErrorName() string {
	return "StopDetailOfPerfStopRuleValidationError"
}

// Error satisfies the builtin error interface
func (e StopDetailOfPerfStopRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopDetailOfPerfStopRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopDetailOfPerfStopRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopDetailOfPerfStopRuleValidationError{}

// Validate checks the field values on UIAgentComponentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentInfoMultiError, or nil if none found.
func (m *UIAgentComponentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExecuteType

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentInfoValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentInfoValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentInfoValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reinstall

	// no validation rules for Restart

	// no validation rules for ReferenceId

	// no validation rules for Times

	if len(errors) > 0 {
		return UIAgentComponentInfoMultiError(errors)
	}

	return nil
}

// UIAgentComponentInfoMultiError is an error wrapping multiple validation
// errors returned by UIAgentComponentInfo.ValidateAll() if the designated
// constraints aren't met.
type UIAgentComponentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentInfoMultiError) AllErrors() []error { return m }

// UIAgentComponentInfoValidationError is the validation error returned by
// UIAgentComponentInfo.Validate if the designated constraints aren't met.
type UIAgentComponentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentInfoValidationError) ErrorName() string {
	return "UIAgentComponentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentInfoValidationError{}

// Validate checks the field values on UIAgentDeviceInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UIAgentDeviceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentDeviceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentDeviceInfoMultiError, or nil if none found.
func (m *UIAgentDeviceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentDeviceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Device.(type) {
	case *UIAgentDeviceInfo_ProjectDevice:
		if v == nil {
			err := UIAgentDeviceInfoValidationError{
				field:  "Device",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetProjectDevice() == nil {
			err := UIAgentDeviceInfoValidationError{
				field:  "ProjectDevice",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProjectDevice()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentDeviceInfoValidationError{
						field:  "ProjectDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentDeviceInfoValidationError{
						field:  "ProjectDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProjectDevice()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentDeviceInfoValidationError{
					field:  "ProjectDevice",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UIAgentDeviceInfo_UserDevice:
		if v == nil {
			err := UIAgentDeviceInfoValidationError{
				field:  "Device",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetUserDevice() == nil {
			err := UIAgentDeviceInfoValidationError{
				field:  "UserDevice",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserDevice()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentDeviceInfoValidationError{
						field:  "UserDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentDeviceInfoValidationError{
						field:  "UserDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserDevice()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentDeviceInfoValidationError{
					field:  "UserDevice",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UIAgentDeviceInfoMultiError(errors)
	}

	return nil
}

// UIAgentDeviceInfoMultiError is an error wrapping multiple validation errors
// returned by UIAgentDeviceInfo.ValidateAll() if the designated constraints
// aren't met.
type UIAgentDeviceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentDeviceInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentDeviceInfoMultiError) AllErrors() []error { return m }

// UIAgentDeviceInfoValidationError is the validation error returned by
// UIAgentDeviceInfo.Validate if the designated constraints aren't met.
type UIAgentDeviceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentDeviceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentDeviceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentDeviceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentDeviceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentDeviceInfoValidationError) ErrorName() string {
	return "UIAgentDeviceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentDeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentDeviceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentDeviceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentDeviceInfoValidationError{}

// Validate checks the field values on StopDetailOfPerfStopRule_MetricPoint
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StopDetailOfPerfStopRule_MetricPoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopDetailOfPerfStopRule_MetricPoint
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StopDetailOfPerfStopRule_MetricPointMultiError, or nil if none found.
func (m *StopDetailOfPerfStopRule_MetricPoint) ValidateAll() error {
	return m.validate(true)
}

func (m *StopDetailOfPerfStopRule_MetricPoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	// no validation rules for Value

	if len(errors) > 0 {
		return StopDetailOfPerfStopRule_MetricPointMultiError(errors)
	}

	return nil
}

// StopDetailOfPerfStopRule_MetricPointMultiError is an error wrapping multiple
// validation errors returned by
// StopDetailOfPerfStopRule_MetricPoint.ValidateAll() if the designated
// constraints aren't met.
type StopDetailOfPerfStopRule_MetricPointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopDetailOfPerfStopRule_MetricPointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopDetailOfPerfStopRule_MetricPointMultiError) AllErrors() []error { return m }

// StopDetailOfPerfStopRule_MetricPointValidationError is the validation error
// returned by StopDetailOfPerfStopRule_MetricPoint.Validate if the designated
// constraints aren't met.
type StopDetailOfPerfStopRule_MetricPointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopDetailOfPerfStopRule_MetricPointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopDetailOfPerfStopRule_MetricPointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopDetailOfPerfStopRule_MetricPointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopDetailOfPerfStopRule_MetricPointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopDetailOfPerfStopRule_MetricPointValidationError) ErrorName() string {
	return "StopDetailOfPerfStopRule_MetricPointValidationError"
}

// Error satisfies the builtin error interface
func (e StopDetailOfPerfStopRule_MetricPointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopDetailOfPerfStopRule_MetricPoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopDetailOfPerfStopRule_MetricPointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopDetailOfPerfStopRule_MetricPointValidationError{}

// Validate checks the field values on UIAgentDeviceInfo_ProjectDeviceInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UIAgentDeviceInfo_ProjectDeviceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentDeviceInfo_ProjectDeviceInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UIAgentDeviceInfo_ProjectDeviceInfoMultiError, or nil if none found.
func (m *UIAgentDeviceInfo_ProjectDeviceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentDeviceInfo_ProjectDeviceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetUdid()); l < 1 || l > 64 {
		err := UIAgentDeviceInfo_ProjectDeviceInfoValidationError{
			field:  "Udid",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetToken()); l < 1 || l > 64 {
		err := UIAgentDeviceInfo_ProjectDeviceInfoValidationError{
			field:  "Token",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UIAgentDeviceInfo_ProjectDeviceInfoMultiError(errors)
	}

	return nil
}

// UIAgentDeviceInfo_ProjectDeviceInfoMultiError is an error wrapping multiple
// validation errors returned by
// UIAgentDeviceInfo_ProjectDeviceInfo.ValidateAll() if the designated
// constraints aren't met.
type UIAgentDeviceInfo_ProjectDeviceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentDeviceInfo_ProjectDeviceInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentDeviceInfo_ProjectDeviceInfoMultiError) AllErrors() []error { return m }

// UIAgentDeviceInfo_ProjectDeviceInfoValidationError is the validation error
// returned by UIAgentDeviceInfo_ProjectDeviceInfo.Validate if the designated
// constraints aren't met.
type UIAgentDeviceInfo_ProjectDeviceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentDeviceInfo_ProjectDeviceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentDeviceInfo_ProjectDeviceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentDeviceInfo_ProjectDeviceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentDeviceInfo_ProjectDeviceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentDeviceInfo_ProjectDeviceInfoValidationError) ErrorName() string {
	return "UIAgentDeviceInfo_ProjectDeviceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentDeviceInfo_ProjectDeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentDeviceInfo_ProjectDeviceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentDeviceInfo_ProjectDeviceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentDeviceInfo_ProjectDeviceInfoValidationError{}

// Validate checks the field values on UIAgentDeviceInfo_UserDeviceInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UIAgentDeviceInfo_UserDeviceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentDeviceInfo_UserDeviceInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UIAgentDeviceInfo_UserDeviceInfoMultiError, or nil if none found.
func (m *UIAgentDeviceInfo_UserDeviceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentDeviceInfo_UserDeviceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _UIAgentDeviceInfo_UserDeviceInfo_DeviceType_NotInLookup[m.GetDeviceType()]; ok {
		err := UIAgentDeviceInfo_UserDeviceInfoValidationError{
			field:  "DeviceType",
			reason: "value must not be in list [DT_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UIAgentDeviceInfo_UserDeviceInfo_PlatformType_NotInLookup[m.GetPlatformType()]; ok {
		err := UIAgentDeviceInfo_UserDeviceInfoValidationError{
			field:  "PlatformType",
			reason: "value must not be in list [PT_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUdid() != "" {

		if utf8.RuneCountInString(m.GetUdid()) > 64 {
			err := UIAgentDeviceInfo_UserDeviceInfoValidationError{
				field:  "Udid",
				reason: "value length must be at most 64 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if utf8.RuneCountInString(m.GetRemoteAddress()) < 1 {
		err := UIAgentDeviceInfo_UserDeviceInfoValidationError{
			field:  "RemoteAddress",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UIAgentDeviceInfo_UserDeviceInfoMultiError(errors)
	}

	return nil
}

// UIAgentDeviceInfo_UserDeviceInfoMultiError is an error wrapping multiple
// validation errors returned by
// UIAgentDeviceInfo_UserDeviceInfo.ValidateAll() if the designated
// constraints aren't met.
type UIAgentDeviceInfo_UserDeviceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentDeviceInfo_UserDeviceInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentDeviceInfo_UserDeviceInfoMultiError) AllErrors() []error { return m }

// UIAgentDeviceInfo_UserDeviceInfoValidationError is the validation error
// returned by UIAgentDeviceInfo_UserDeviceInfo.Validate if the designated
// constraints aren't met.
type UIAgentDeviceInfo_UserDeviceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentDeviceInfo_UserDeviceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentDeviceInfo_UserDeviceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentDeviceInfo_UserDeviceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentDeviceInfo_UserDeviceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentDeviceInfo_UserDeviceInfoValidationError) ErrorName() string {
	return "UIAgentDeviceInfo_UserDeviceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentDeviceInfo_UserDeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentDeviceInfo_UserDeviceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentDeviceInfo_UserDeviceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentDeviceInfo_UserDeviceInfoValidationError{}

var _UIAgentDeviceInfo_UserDeviceInfo_DeviceType_NotInLookup = map[pb1.DeviceType]struct{}{
	0: {},
}

var _UIAgentDeviceInfo_UserDeviceInfo_PlatformType_NotInLookup = map[pb1.PlatformType]struct{}{
	0: {},
}
