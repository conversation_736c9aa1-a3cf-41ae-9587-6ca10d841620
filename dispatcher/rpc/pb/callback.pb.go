// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/callback.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	pb2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CallbackType int32

const (
	CallbackType_CallbackType_UNKNOWN            CallbackType = 0
	CallbackType_CallbackType_API_CASE           CallbackType = 1  // API测试用例
	CallbackType_CallbackType_API_SUITE          CallbackType = 2  // API测试集合
	CallbackType_CallbackType_INTERFACE_CASE     CallbackType = 3  // 接口用例
	CallbackType_CallbackType_INTERFACE_DOCUMENT CallbackType = 4  // 接口文档（即接口集合）
	CallbackType_CallbackType_UI_CASE            CallbackType = 5  // UI测试用例
	CallbackType_CallbackType_UI_SUITE           CallbackType = 6  // UI测试集合
	CallbackType_CallbackType_API_SERVICE        CallbackType = 7  // API精准测试服务
	CallbackType_CallbackType_PERF_CASE          CallbackType = 8  // 压力测试用例
	CallbackType_CallbackType_PERF_SUITE         CallbackType = 9  // 压力测试集合
	CallbackType_CallbackType_UI_AGENT_COMPONENT CallbackType = 10 // `UI Agent`组件
	CallbackType_CallbackType_UI_AGENT_CASE      CallbackType = 11 // `UI Agent`用例
	CallbackType_CallbackType_UI_AGENT_SUITE     CallbackType = 12 // `UI Agent`集合
)

// Enum value maps for CallbackType.
var (
	CallbackType_name = map[int32]string{
		0:  "CallbackType_UNKNOWN",
		1:  "CallbackType_API_CASE",
		2:  "CallbackType_API_SUITE",
		3:  "CallbackType_INTERFACE_CASE",
		4:  "CallbackType_INTERFACE_DOCUMENT",
		5:  "CallbackType_UI_CASE",
		6:  "CallbackType_UI_SUITE",
		7:  "CallbackType_API_SERVICE",
		8:  "CallbackType_PERF_CASE",
		9:  "CallbackType_PERF_SUITE",
		10: "CallbackType_UI_AGENT_COMPONENT",
		11: "CallbackType_UI_AGENT_CASE",
		12: "CallbackType_UI_AGENT_SUITE",
	}
	CallbackType_value = map[string]int32{
		"CallbackType_UNKNOWN":            0,
		"CallbackType_API_CASE":           1,
		"CallbackType_API_SUITE":          2,
		"CallbackType_INTERFACE_CASE":     3,
		"CallbackType_INTERFACE_DOCUMENT": 4,
		"CallbackType_UI_CASE":            5,
		"CallbackType_UI_SUITE":           6,
		"CallbackType_API_SERVICE":        7,
		"CallbackType_PERF_CASE":          8,
		"CallbackType_PERF_SUITE":         9,
		"CallbackType_UI_AGENT_COMPONENT": 10,
		"CallbackType_UI_AGENT_CASE":      11,
		"CallbackType_UI_AGENT_SUITE":     12,
	}
)

func (x CallbackType) Enum() *CallbackType {
	p := new(CallbackType)
	*p = x
	return p
}

func (x CallbackType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallbackType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_callback_proto_enumTypes[0].Descriptor()
}

func (CallbackType) Type() protoreflect.EnumType {
	return &file_dispatcher_callback_proto_enumTypes[0]
}

func (x CallbackType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallbackType.Descriptor instead.
func (CallbackType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{0}
}

type CallbackReq struct {
	state        protoimpl.MessageState   `protogen:"open.v1"`
	TriggerMode  pb.TriggerMode           `protobuf:"varint,1,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`           // 触发模式
	TriggerRule  string                   `protobuf:"bytes,2,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`                                    // 触发规则
	ProjectId    string                   `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目ID
	TaskId       string                   `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                   // 任务ID
	ExecuteType  pb1.ApiExecutionDataType `protobuf:"varint,5,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"` // 执行类型
	CallbackType CallbackType             `protobuf:"varint,6,opt,name=callback_type,json=callbackType,proto3,enum=dispatcher.CallbackType" json:"callback_type,omitempty"`   // 回调类型
	PurposeType  pb.PurposeType           `protobuf:"varint,7,opt,name=purpose_type,json=purposeType,proto3,enum=common.PurposeType" json:"purpose_type,omitempty"`           // 计划用途
	// Types that are valid to be assigned to Data:
	//
	//	*CallbackReq_Case
	//	*CallbackReq_Suite
	//	*CallbackReq_InterfaceCase
	//	*CallbackReq_InterfaceDocument
	//	*CallbackReq_UiCase
	//	*CallbackReq_UiSuite
	//	*CallbackReq_Service
	//	*CallbackReq_PerfCase
	//	*CallbackReq_PerfSuite
	//	*CallbackReq_UiAgentComponent
	Data          isCallbackReq_Data `protobuf_oneof:"data"`
	Debug         bool               `protobuf:"varint,99,opt,name=debug,proto3" json:"debug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallbackReq) Reset() {
	*x = CallbackReq{}
	mi := &file_dispatcher_callback_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackReq) ProtoMessage() {}

func (x *CallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackReq.ProtoReflect.Descriptor instead.
func (*CallbackReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{0}
}

func (x *CallbackReq) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *CallbackReq) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *CallbackReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CallbackReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *CallbackReq) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *CallbackReq) GetCallbackType() CallbackType {
	if x != nil {
		return x.CallbackType
	}
	return CallbackType_CallbackType_UNKNOWN
}

func (x *CallbackReq) GetPurposeType() pb.PurposeType {
	if x != nil {
		return x.PurposeType
	}
	return pb.PurposeType(0)
}

func (x *CallbackReq) GetData() isCallbackReq_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CallbackReq) GetCase() *CaseCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_Case); ok {
			return x.Case
		}
	}
	return nil
}

func (x *CallbackReq) GetSuite() *SuiteCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_Suite); ok {
			return x.Suite
		}
	}
	return nil
}

func (x *CallbackReq) GetInterfaceCase() *InterfaceCaseCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_InterfaceCase); ok {
			return x.InterfaceCase
		}
	}
	return nil
}

func (x *CallbackReq) GetInterfaceDocument() *InterfaceDocumentCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_InterfaceDocument); ok {
			return x.InterfaceDocument
		}
	}
	return nil
}

func (x *CallbackReq) GetUiCase() *UICaseCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_UiCase); ok {
			return x.UiCase
		}
	}
	return nil
}

func (x *CallbackReq) GetUiSuite() *UISuiteCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_UiSuite); ok {
			return x.UiSuite
		}
	}
	return nil
}

func (x *CallbackReq) GetService() *ServiceCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_Service); ok {
			return x.Service
		}
	}
	return nil
}

func (x *CallbackReq) GetPerfCase() *PerfCaseCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_PerfCase); ok {
			return x.PerfCase
		}
	}
	return nil
}

func (x *CallbackReq) GetPerfSuite() *PerfSuiteCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_PerfSuite); ok {
			return x.PerfSuite
		}
	}
	return nil
}

func (x *CallbackReq) GetUiAgentComponent() *UIAgentComponentCallbackData {
	if x != nil {
		if x, ok := x.Data.(*CallbackReq_UiAgentComponent); ok {
			return x.UiAgentComponent
		}
	}
	return nil
}

func (x *CallbackReq) GetDebug() bool {
	if x != nil {
		return x.Debug
	}
	return false
}

type isCallbackReq_Data interface {
	isCallbackReq_Data()
}

type CallbackReq_Case struct {
	Case *CaseCallbackData `protobuf:"bytes,31,opt,name=case,proto3,oneof"`
}

type CallbackReq_Suite struct {
	Suite *SuiteCallbackData `protobuf:"bytes,32,opt,name=suite,proto3,oneof"`
}

type CallbackReq_InterfaceCase struct {
	InterfaceCase *InterfaceCaseCallbackData `protobuf:"bytes,33,opt,name=interface_case,json=interfaceCase,proto3,oneof"`
}

type CallbackReq_InterfaceDocument struct {
	InterfaceDocument *InterfaceDocumentCallbackData `protobuf:"bytes,34,opt,name=interface_document,json=interfaceDocument,proto3,oneof"`
}

type CallbackReq_UiCase struct {
	UiCase *UICaseCallbackData `protobuf:"bytes,35,opt,name=ui_case,json=uiCase,proto3,oneof"`
}

type CallbackReq_UiSuite struct {
	UiSuite *UISuiteCallbackData `protobuf:"bytes,36,opt,name=ui_suite,json=uiSuite,proto3,oneof"`
}

type CallbackReq_Service struct {
	Service *ServiceCallbackData `protobuf:"bytes,37,opt,name=service,proto3,oneof"`
}

type CallbackReq_PerfCase struct {
	PerfCase *PerfCaseCallbackData `protobuf:"bytes,38,opt,name=perf_case,json=perfCase,proto3,oneof"`
}

type CallbackReq_PerfSuite struct {
	PerfSuite *PerfSuiteCallbackData `protobuf:"bytes,39,opt,name=perf_suite,json=perfSuite,proto3,oneof"`
}

type CallbackReq_UiAgentComponent struct {
	UiAgentComponent *UIAgentComponentCallbackData `protobuf:"bytes,40,opt,name=ui_agent_component,json=uiAgentComponent,proto3,oneof"`
}

func (*CallbackReq_Case) isCallbackReq_Data() {}

func (*CallbackReq_Suite) isCallbackReq_Data() {}

func (*CallbackReq_InterfaceCase) isCallbackReq_Data() {}

func (*CallbackReq_InterfaceDocument) isCallbackReq_Data() {}

func (*CallbackReq_UiCase) isCallbackReq_Data() {}

func (*CallbackReq_UiSuite) isCallbackReq_Data() {}

func (*CallbackReq_Service) isCallbackReq_Data() {}

func (*CallbackReq_PerfCase) isCallbackReq_Data() {}

func (*CallbackReq_PerfSuite) isCallbackReq_Data() {}

func (*CallbackReq_UiAgentComponent) isCallbackReq_Data() {}

// SuiteCallbackData 集合回调
type SuiteCallbackData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PlanId         string                 `protobuf:"bytes,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                             // 计划ID
	PlanExecuteId  string                 `protobuf:"bytes,2,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                      // 计划执行ID
	SuiteId        string                 `protobuf:"bytes,3,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                          // 回调的集合ID
	SuiteExecuteId string                 `protobuf:"bytes,4,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"`                   // 回调的集合执行ID
	SuiteState     ComponentState         `protobuf:"varint,5,opt,name=suite_state,json=suiteState,proto3,enum=dispatcher.ComponentState" json:"suite_state,omitempty"` // 回调的集合执行状态
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SuiteCallbackData) Reset() {
	*x = SuiteCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuiteCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuiteCallbackData) ProtoMessage() {}

func (x *SuiteCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuiteCallbackData.ProtoReflect.Descriptor instead.
func (*SuiteCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{1}
}

func (x *SuiteCallbackData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SuiteCallbackData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *SuiteCallbackData) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SuiteCallbackData) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *SuiteCallbackData) GetSuiteState() ComponentState {
	if x != nil {
		return x.SuiteState
	}
	return ComponentState_Pending
}

// InterfaceDocumentCallbackData 接口文档回调
type InterfaceDocumentCallbackData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PlanId             string                 `protobuf:"bytes,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                         // 计划ID
	PlanExecuteId      string                 `protobuf:"bytes,2,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                                  // 计划执行ID
	InterfaceId        string                 `protobuf:"bytes,3,opt,name=interface_id,json=interfaceId,proto3" json:"interface_id,omitempty"`                                          // 回调的接口文档ID
	InterfaceExecuteId string                 `protobuf:"bytes,4,opt,name=interface_execute_id,json=interfaceExecuteId,proto3" json:"interface_execute_id,omitempty"`                   // 回调的集合文档执行ID
	InterfaceState     ComponentState         `protobuf:"varint,5,opt,name=interface_state,json=interfaceState,proto3,enum=dispatcher.ComponentState" json:"interface_state,omitempty"` // 回调的接口文档执行状态
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *InterfaceDocumentCallbackData) Reset() {
	*x = InterfaceDocumentCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceDocumentCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceDocumentCallbackData) ProtoMessage() {}

func (x *InterfaceDocumentCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceDocumentCallbackData.ProtoReflect.Descriptor instead.
func (*InterfaceDocumentCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{2}
}

func (x *InterfaceDocumentCallbackData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *InterfaceDocumentCallbackData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *InterfaceDocumentCallbackData) GetInterfaceId() string {
	if x != nil {
		return x.InterfaceId
	}
	return ""
}

func (x *InterfaceDocumentCallbackData) GetInterfaceExecuteId() string {
	if x != nil {
		return x.InterfaceExecuteId
	}
	return ""
}

func (x *InterfaceDocumentCallbackData) GetInterfaceState() ComponentState {
	if x != nil {
		return x.InterfaceState
	}
	return ComponentState_Pending
}

// InterfaceCaseCallbackData 接口用例回调
type InterfaceCaseCallbackData struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	InterfaceId            string                 `protobuf:"bytes,1,opt,name=interface_id,json=interfaceId,proto3" json:"interface_id,omitempty"`                                                        // 接口ID
	InterfaceExecuteId     string                 `protobuf:"bytes,2,opt,name=interface_execute_id,json=interfaceExecuteId,proto3" json:"interface_execute_id,omitempty"`                                 // 接口执行ID
	InterfaceCaseId        string                 `protobuf:"bytes,3,opt,name=interface_case_id,json=interfaceCaseId,proto3" json:"interface_case_id,omitempty"`                                          // 回调的接口用例ID
	InterfaceCaseExecuteId string                 `protobuf:"bytes,4,opt,name=interface_case_execute_id,json=interfaceCaseExecuteId,proto3" json:"interface_case_execute_id,omitempty"`                   // 回调的接口用例执行ID
	InterfaceCaseState     ComponentState         `protobuf:"varint,5,opt,name=interface_case_state,json=interfaceCaseState,proto3,enum=dispatcher.ComponentState" json:"interface_case_state,omitempty"` // 回调的接口用例执行状态
	Version                string                 `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`                                                                                   // 版本
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *InterfaceCaseCallbackData) Reset() {
	*x = InterfaceCaseCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceCaseCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceCaseCallbackData) ProtoMessage() {}

func (x *InterfaceCaseCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceCaseCallbackData.ProtoReflect.Descriptor instead.
func (*InterfaceCaseCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{3}
}

func (x *InterfaceCaseCallbackData) GetInterfaceId() string {
	if x != nil {
		return x.InterfaceId
	}
	return ""
}

func (x *InterfaceCaseCallbackData) GetInterfaceExecuteId() string {
	if x != nil {
		return x.InterfaceExecuteId
	}
	return ""
}

func (x *InterfaceCaseCallbackData) GetInterfaceCaseId() string {
	if x != nil {
		return x.InterfaceCaseId
	}
	return ""
}

func (x *InterfaceCaseCallbackData) GetInterfaceCaseExecuteId() string {
	if x != nil {
		return x.InterfaceCaseExecuteId
	}
	return ""
}

func (x *InterfaceCaseCallbackData) GetInterfaceCaseState() ComponentState {
	if x != nil {
		return x.InterfaceCaseState
	}
	return ComponentState_Pending
}

func (x *InterfaceCaseCallbackData) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// CaseCallbackData 用例回调
type CaseCallbackData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SuiteId        string                 `protobuf:"bytes,1,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                       // 集合ID
	SuiteExecuteId string                 `protobuf:"bytes,2,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"`                // 集合执行ID
	CaseId         string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                                          // 用例ID
	CaseExecuteId  string                 `protobuf:"bytes,4,opt,name=case_execute_id,json=caseExecuteId,proto3" json:"case_execute_id,omitempty"`                   // 用例执行ID
	CaseState      ComponentState         `protobuf:"varint,5,opt,name=case_state,json=caseState,proto3,enum=dispatcher.ComponentState" json:"case_state,omitempty"` // 用例执行状态
	Version        string                 `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`                                                      // 版本
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CaseCallbackData) Reset() {
	*x = CaseCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseCallbackData) ProtoMessage() {}

func (x *CaseCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseCallbackData.ProtoReflect.Descriptor instead.
func (*CaseCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{4}
}

func (x *CaseCallbackData) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *CaseCallbackData) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *CaseCallbackData) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseCallbackData) GetCaseExecuteId() string {
	if x != nil {
		return x.CaseExecuteId
	}
	return ""
}

func (x *CaseCallbackData) GetCaseState() ComponentState {
	if x != nil {
		return x.CaseState
	}
	return ComponentState_Pending
}

func (x *CaseCallbackData) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// UICaseCallbackData UI用例回调
type UICaseCallbackData struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	UiPlanId         string                 `protobuf:"bytes,1,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`                                  // UI计划ID
	UiPlanExecuteId  string                 `protobuf:"bytes,2,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"`           // UI计划执行ID
	UiSuiteId        string                 `protobuf:"bytes,3,opt,name=ui_suite_id,json=uiSuiteId,proto3" json:"ui_suite_id,omitempty"`                               // UI集合ID
	UiSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=ui_suite_execute_id,json=uiSuiteExecuteId,proto3" json:"ui_suite_execute_id,omitempty"`        // UI集合执行ID
	UiCaseId         string                 `protobuf:"bytes,5,opt,name=ui_case_id,json=uiCaseId,proto3" json:"ui_case_id,omitempty"`                                  // 回调的UI用例ID
	UiCaseExecuteId  string                 `protobuf:"bytes,6,opt,name=ui_case_execute_id,json=uiCaseExecuteId,proto3" json:"ui_case_execute_id,omitempty"`           // 回调的UI用例执行ID
	CaseState        ComponentState         `protobuf:"varint,7,opt,name=case_state,json=caseState,proto3,enum=dispatcher.ComponentState" json:"case_state,omitempty"` // 回调的UI用例执行状态
	MetaData         *pb1.UIPlanMetaData    `protobuf:"bytes,8,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                    // UI计划元数据
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UICaseCallbackData) Reset() {
	*x = UICaseCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICaseCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICaseCallbackData) ProtoMessage() {}

func (x *UICaseCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICaseCallbackData.ProtoReflect.Descriptor instead.
func (*UICaseCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{5}
}

func (x *UICaseCallbackData) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UICaseCallbackData) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UICaseCallbackData) GetUiSuiteId() string {
	if x != nil {
		return x.UiSuiteId
	}
	return ""
}

func (x *UICaseCallbackData) GetUiSuiteExecuteId() string {
	if x != nil {
		return x.UiSuiteExecuteId
	}
	return ""
}

func (x *UICaseCallbackData) GetUiCaseId() string {
	if x != nil {
		return x.UiCaseId
	}
	return ""
}

func (x *UICaseCallbackData) GetUiCaseExecuteId() string {
	if x != nil {
		return x.UiCaseExecuteId
	}
	return ""
}

func (x *UICaseCallbackData) GetCaseState() ComponentState {
	if x != nil {
		return x.CaseState
	}
	return ComponentState_Pending
}

func (x *UICaseCallbackData) GetMetaData() *pb1.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

// UISuiteCallbackData UI集合回调
type UISuiteCallbackData struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	UiPlanId         string                 `protobuf:"bytes,1,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`                                     // UI计划ID
	UiPlanExecuteId  string                 `protobuf:"bytes,2,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"`              // UI计划执行ID
	UiSuiteId        string                 `protobuf:"bytes,3,opt,name=ui_suite_id,json=uiSuiteId,proto3" json:"ui_suite_id,omitempty"`                                  // 回调的UI集合ID
	UiSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=ui_suite_execute_id,json=uiSuiteExecuteId,proto3" json:"ui_suite_execute_id,omitempty"`           // 回调的UI集合执行ID
	SuiteState       ComponentState         `protobuf:"varint,5,opt,name=suite_state,json=suiteState,proto3,enum=dispatcher.ComponentState" json:"suite_state,omitempty"` // 回调的UI集合执行状态
	MetaData         *pb1.UIPlanMetaData    `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                       // UI计划元数据
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UISuiteCallbackData) Reset() {
	*x = UISuiteCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UISuiteCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UISuiteCallbackData) ProtoMessage() {}

func (x *UISuiteCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UISuiteCallbackData.ProtoReflect.Descriptor instead.
func (*UISuiteCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{6}
}

func (x *UISuiteCallbackData) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UISuiteCallbackData) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UISuiteCallbackData) GetUiSuiteId() string {
	if x != nil {
		return x.UiSuiteId
	}
	return ""
}

func (x *UISuiteCallbackData) GetUiSuiteExecuteId() string {
	if x != nil {
		return x.UiSuiteExecuteId
	}
	return ""
}

func (x *UISuiteCallbackData) GetSuiteState() ComponentState {
	if x != nil {
		return x.SuiteState
	}
	return ComponentState_Pending
}

func (x *UISuiteCallbackData) GetMetaData() *pb1.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

type ServiceCallbackData struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PlanId           string                 `protobuf:"bytes,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                   // 计划ID
	PlanExecuteId    string                 `protobuf:"bytes,2,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                            // 计划执行ID
	ServiceId        string                 `protobuf:"bytes,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`                                          // 回调的服务ID
	ServiceExecuteId string                 `protobuf:"bytes,4,opt,name=service_execute_id,json=serviceExecuteId,proto3" json:"service_execute_id,omitempty"`                   // 回调的服务执行ID
	ServiceState     ComponentState         `protobuf:"varint,5,opt,name=service_state,json=serviceState,proto3,enum=dispatcher.ComponentState" json:"service_state,omitempty"` // 集合执行状态
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ServiceCallbackData) Reset() {
	*x = ServiceCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCallbackData) ProtoMessage() {}

func (x *ServiceCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCallbackData.ProtoReflect.Descriptor instead.
func (*ServiceCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{7}
}

func (x *ServiceCallbackData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *ServiceCallbackData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *ServiceCallbackData) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceCallbackData) GetServiceExecuteId() string {
	if x != nil {
		return x.ServiceExecuteId
	}
	return ""
}

func (x *ServiceCallbackData) GetServiceState() ComponentState {
	if x != nil {
		return x.ServiceState
	}
	return ComponentState_Pending
}

// PerfCaseCallbackData 压测用例回调
type PerfCaseCallbackData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PerfPlanId         string                 `protobuf:"bytes,1,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                            // 压力计划ID
	PerfPlanExecuteId  string                 `protobuf:"bytes,2,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"`     // 压力计划执行ID
	PerfSuiteId        string                 `protobuf:"bytes,3,opt,name=perf_suite_id,json=perfSuiteId,proto3" json:"perf_suite_id,omitempty"`                         // 压力集合ID
	PerfSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=perf_suite_execute_id,json=perfSuiteExecuteId,proto3" json:"perf_suite_execute_id,omitempty"`  //压力集合执行ID
	PerfCaseId         string                 `protobuf:"bytes,5,opt,name=perf_case_id,json=perfCaseId,proto3" json:"perf_case_id,omitempty"`                            // 回调的压力用例ID
	PerfCaseExecuteId  string                 `protobuf:"bytes,6,opt,name=perf_case_execute_id,json=perfCaseExecuteId,proto3" json:"perf_case_execute_id,omitempty"`     // 回调的压力用例执行ID
	CaseState          ComponentState         `protobuf:"varint,7,opt,name=case_state,json=caseState,proto3,enum=dispatcher.ComponentState" json:"case_state,omitempty"` // 回调的压力用例执行状态
	MetaData           *pb1.PerfPlanMetaData  `protobuf:"bytes,8,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                    // 压力计划元数据
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PerfCaseCallbackData) Reset() {
	*x = PerfCaseCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseCallbackData) ProtoMessage() {}

func (x *PerfCaseCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseCallbackData.ProtoReflect.Descriptor instead.
func (*PerfCaseCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{8}
}

func (x *PerfCaseCallbackData) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfCaseCallbackData) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfCaseCallbackData) GetPerfSuiteId() string {
	if x != nil {
		return x.PerfSuiteId
	}
	return ""
}

func (x *PerfCaseCallbackData) GetPerfSuiteExecuteId() string {
	if x != nil {
		return x.PerfSuiteExecuteId
	}
	return ""
}

func (x *PerfCaseCallbackData) GetPerfCaseId() string {
	if x != nil {
		return x.PerfCaseId
	}
	return ""
}

func (x *PerfCaseCallbackData) GetPerfCaseExecuteId() string {
	if x != nil {
		return x.PerfCaseExecuteId
	}
	return ""
}

func (x *PerfCaseCallbackData) GetCaseState() ComponentState {
	if x != nil {
		return x.CaseState
	}
	return ComponentState_Pending
}

func (x *PerfCaseCallbackData) GetMetaData() *pb1.PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

// PerfSuiteCallbackData 集合回调
type PerfSuiteCallbackData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PerfPlanId         string                 `protobuf:"bytes,1,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                               // 压力计划ID
	PerfPlanExecuteId  string                 `protobuf:"bytes,2,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"`        // 压力计划执行ID
	PerfSuiteId        string                 `protobuf:"bytes,3,opt,name=perf_suite_id,json=perfSuiteId,proto3" json:"perf_suite_id,omitempty"`                            // 回调的压力集合ID
	PerfSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=perf_suite_execute_id,json=perfSuiteExecuteId,proto3" json:"perf_suite_execute_id,omitempty"`     // 回调的压力集合执行ID
	SuiteState         ComponentState         `protobuf:"varint,5,opt,name=suite_state,json=suiteState,proto3,enum=dispatcher.ComponentState" json:"suite_state,omitempty"` // 回调的压力集合执行状态
	MetaData           *pb1.PerfPlanMetaData  `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                       // 压力计划元数据
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PerfSuiteCallbackData) Reset() {
	*x = PerfSuiteCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfSuiteCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfSuiteCallbackData) ProtoMessage() {}

func (x *PerfSuiteCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfSuiteCallbackData.ProtoReflect.Descriptor instead.
func (*PerfSuiteCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{9}
}

func (x *PerfSuiteCallbackData) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfSuiteCallbackData) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfSuiteCallbackData) GetPerfSuiteId() string {
	if x != nil {
		return x.PerfSuiteId
	}
	return ""
}

func (x *PerfSuiteCallbackData) GetPerfSuiteExecuteId() string {
	if x != nil {
		return x.PerfSuiteExecuteId
	}
	return ""
}

func (x *PerfSuiteCallbackData) GetSuiteState() ComponentState {
	if x != nil {
		return x.SuiteState
	}
	return ComponentState_Pending
}

func (x *PerfSuiteCallbackData) GetMetaData() *pb1.PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

// UIAgentComponentCallbackData `UI Agent`组件回调
type UIAgentComponentCallbackData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ComponentId        string                 `protobuf:"bytes,1,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"`                                           // 组件ID
	ComponentExecuteId string                 `protobuf:"bytes,2,opt,name=component_execute_id,json=componentExecuteId,proto3" json:"component_execute_id,omitempty"`                    // 组件执行ID
	ParentExecuteId    string                 `protobuf:"bytes,3,opt,name=parent_execute_id,json=parentExecuteId,proto3" json:"parent_execute_id,omitempty"`                             // 父执行ID（多次执行场景）
	ComponentState     ComponentState         `protobuf:"varint,21,opt,name=component_state,json=componentState,proto3,enum=dispatcher.ComponentState" json:"component_state,omitempty"` // 组件执行状态
	ErrMsg             *pb2.ErrorMessage      `protobuf:"bytes,22,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                                                         // 错误信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UIAgentComponentCallbackData) Reset() {
	*x = UIAgentComponentCallbackData{}
	mi := &file_dispatcher_callback_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentCallbackData) ProtoMessage() {}

func (x *UIAgentComponentCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_callback_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentCallbackData.ProtoReflect.Descriptor instead.
func (*UIAgentComponentCallbackData) Descriptor() ([]byte, []int) {
	return file_dispatcher_callback_proto_rawDescGZIP(), []int{10}
}

func (x *UIAgentComponentCallbackData) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *UIAgentComponentCallbackData) GetComponentExecuteId() string {
	if x != nil {
		return x.ComponentExecuteId
	}
	return ""
}

func (x *UIAgentComponentCallbackData) GetParentExecuteId() string {
	if x != nil {
		return x.ParentExecuteId
	}
	return ""
}

func (x *UIAgentComponentCallbackData) GetComponentState() ComponentState {
	if x != nil {
		return x.ComponentState
	}
	return ComponentState_Pending
}

func (x *UIAgentComponentCallbackData) GetErrMsg() *pb2.ErrorMessage {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

var File_dispatcher_callback_proto protoreflect.FileDescriptor

var file_dispatcher_callback_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x08, 0x0a, 0x0b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x32, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x04, 0x63, 0x61, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x2e, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x05, 0x73, 0x75, 0x69, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x0e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x12,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x63,
	0x61, 0x73, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69, 0x43,
	0x61, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x55, 0x49, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x07, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74,
	0x65, 0x12, 0x3b, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f,
	0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50,
	0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x12,
	0x42, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x27, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x10, 0x75, 0x69, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x63, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x64, 0x65,
	0x62, 0x75, 0x67, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd6, 0x01, 0x0a, 0x11,
	0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x22, 0xfa, 0x01, 0x0a, 0x1d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x22, 0xbf, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43,
	0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x39, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61,
	0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x14, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0xed, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09,
	0x63, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0xea, 0x02, 0x0a, 0x12, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69,
	0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x69, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x75, 0x69, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x09, 0x63, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x6d, 0x65,
	0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61,
	0x22, 0xa2, 0x02, 0x0a, 0x13, 0x55, 0x49, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69,
	0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x34, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50,
	0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0xe4, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x86, 0x03, 0x0a,
	0x14, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15,
	0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x65, 0x72,
	0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a,
	0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50,
	0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0xb6, 0x02, 0x0a, 0x15, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x53,
	0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73,
	0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0xeb,
	0x02, 0x0a, 0x1c, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x43, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x0a, 0x52, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x52, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x52, 0x0f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x2a, 0x97, 0x03, 0x0a,
	0x0c, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x14, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x02, 0x12, 0x1f,
	0x0a, 0x1b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x03, 0x12,
	0x23, 0x0a, 0x1f, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x05, 0x12, 0x19,
	0x0a, 0x15, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55,
	0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x43, 0x41, 0x53,
	0x45, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x09,
	0x12, 0x23, 0x0a, 0x1f, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x55, 0x49, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e,
	0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x41, 0x53, 0x45, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x55, 0x49, 0x54, 0x45, 0x10, 0x0c, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73,
	0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f,
	0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_callback_proto_rawDescOnce sync.Once
	file_dispatcher_callback_proto_rawDescData = file_dispatcher_callback_proto_rawDesc
)

func file_dispatcher_callback_proto_rawDescGZIP() []byte {
	file_dispatcher_callback_proto_rawDescOnce.Do(func() {
		file_dispatcher_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_callback_proto_rawDescData)
	})
	return file_dispatcher_callback_proto_rawDescData
}

var file_dispatcher_callback_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dispatcher_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_dispatcher_callback_proto_goTypes = []any{
	(CallbackType)(0),                     // 0: dispatcher.CallbackType
	(*CallbackReq)(nil),                   // 1: dispatcher.CallbackReq
	(*SuiteCallbackData)(nil),             // 2: dispatcher.SuiteCallbackData
	(*InterfaceDocumentCallbackData)(nil), // 3: dispatcher.InterfaceDocumentCallbackData
	(*InterfaceCaseCallbackData)(nil),     // 4: dispatcher.InterfaceCaseCallbackData
	(*CaseCallbackData)(nil),              // 5: dispatcher.CaseCallbackData
	(*UICaseCallbackData)(nil),            // 6: dispatcher.UICaseCallbackData
	(*UISuiteCallbackData)(nil),           // 7: dispatcher.UISuiteCallbackData
	(*ServiceCallbackData)(nil),           // 8: dispatcher.ServiceCallbackData
	(*PerfCaseCallbackData)(nil),          // 9: dispatcher.PerfCaseCallbackData
	(*PerfSuiteCallbackData)(nil),         // 10: dispatcher.PerfSuiteCallbackData
	(*UIAgentComponentCallbackData)(nil),  // 11: dispatcher.UIAgentComponentCallbackData
	(pb.TriggerMode)(0),                   // 12: common.TriggerMode
	(pb1.ApiExecutionDataType)(0),         // 13: manager.ApiExecutionDataType
	(pb.PurposeType)(0),                   // 14: common.PurposeType
	(ComponentState)(0),                   // 15: dispatcher.ComponentState
	(*pb1.UIPlanMetaData)(nil),            // 16: manager.UIPlanMetaData
	(*pb1.PerfPlanMetaData)(nil),          // 17: manager.PerfPlanMetaData
	(*pb2.ErrorMessage)(nil),              // 18: reporter.ErrorMessage
}
var file_dispatcher_callback_proto_depIdxs = []int32{
	12, // 0: dispatcher.CallbackReq.trigger_mode:type_name -> common.TriggerMode
	13, // 1: dispatcher.CallbackReq.execute_type:type_name -> manager.ApiExecutionDataType
	0,  // 2: dispatcher.CallbackReq.callback_type:type_name -> dispatcher.CallbackType
	14, // 3: dispatcher.CallbackReq.purpose_type:type_name -> common.PurposeType
	5,  // 4: dispatcher.CallbackReq.case:type_name -> dispatcher.CaseCallbackData
	2,  // 5: dispatcher.CallbackReq.suite:type_name -> dispatcher.SuiteCallbackData
	4,  // 6: dispatcher.CallbackReq.interface_case:type_name -> dispatcher.InterfaceCaseCallbackData
	3,  // 7: dispatcher.CallbackReq.interface_document:type_name -> dispatcher.InterfaceDocumentCallbackData
	6,  // 8: dispatcher.CallbackReq.ui_case:type_name -> dispatcher.UICaseCallbackData
	7,  // 9: dispatcher.CallbackReq.ui_suite:type_name -> dispatcher.UISuiteCallbackData
	8,  // 10: dispatcher.CallbackReq.service:type_name -> dispatcher.ServiceCallbackData
	9,  // 11: dispatcher.CallbackReq.perf_case:type_name -> dispatcher.PerfCaseCallbackData
	10, // 12: dispatcher.CallbackReq.perf_suite:type_name -> dispatcher.PerfSuiteCallbackData
	11, // 13: dispatcher.CallbackReq.ui_agent_component:type_name -> dispatcher.UIAgentComponentCallbackData
	15, // 14: dispatcher.SuiteCallbackData.suite_state:type_name -> dispatcher.ComponentState
	15, // 15: dispatcher.InterfaceDocumentCallbackData.interface_state:type_name -> dispatcher.ComponentState
	15, // 16: dispatcher.InterfaceCaseCallbackData.interface_case_state:type_name -> dispatcher.ComponentState
	15, // 17: dispatcher.CaseCallbackData.case_state:type_name -> dispatcher.ComponentState
	15, // 18: dispatcher.UICaseCallbackData.case_state:type_name -> dispatcher.ComponentState
	16, // 19: dispatcher.UICaseCallbackData.meta_data:type_name -> manager.UIPlanMetaData
	15, // 20: dispatcher.UISuiteCallbackData.suite_state:type_name -> dispatcher.ComponentState
	16, // 21: dispatcher.UISuiteCallbackData.meta_data:type_name -> manager.UIPlanMetaData
	15, // 22: dispatcher.ServiceCallbackData.service_state:type_name -> dispatcher.ComponentState
	15, // 23: dispatcher.PerfCaseCallbackData.case_state:type_name -> dispatcher.ComponentState
	17, // 24: dispatcher.PerfCaseCallbackData.meta_data:type_name -> manager.PerfPlanMetaData
	15, // 25: dispatcher.PerfSuiteCallbackData.suite_state:type_name -> dispatcher.ComponentState
	17, // 26: dispatcher.PerfSuiteCallbackData.meta_data:type_name -> manager.PerfPlanMetaData
	15, // 27: dispatcher.UIAgentComponentCallbackData.component_state:type_name -> dispatcher.ComponentState
	18, // 28: dispatcher.UIAgentComponentCallbackData.err_msg:type_name -> reporter.ErrorMessage
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_dispatcher_callback_proto_init() }
func file_dispatcher_callback_proto_init() {
	if File_dispatcher_callback_proto != nil {
		return
	}
	file_dispatcher_base_proto_init()
	file_dispatcher_callback_proto_msgTypes[0].OneofWrappers = []any{
		(*CallbackReq_Case)(nil),
		(*CallbackReq_Suite)(nil),
		(*CallbackReq_InterfaceCase)(nil),
		(*CallbackReq_InterfaceDocument)(nil),
		(*CallbackReq_UiCase)(nil),
		(*CallbackReq_UiSuite)(nil),
		(*CallbackReq_Service)(nil),
		(*CallbackReq_PerfCase)(nil),
		(*CallbackReq_PerfSuite)(nil),
		(*CallbackReq_UiAgentComponent)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_callback_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_callback_proto_goTypes,
		DependencyIndexes: file_dispatcher_callback_proto_depIdxs,
		EnumInfos:         file_dispatcher_callback_proto_enumTypes,
		MessageInfos:      file_dispatcher_callback_proto_msgTypes,
	}.Build()
	File_dispatcher_callback_proto = out.File
	file_dispatcher_callback_proto_rawDesc = nil
	file_dispatcher_callback_proto_goTypes = nil
	file_dispatcher_callback_proto_depIdxs = nil
}
