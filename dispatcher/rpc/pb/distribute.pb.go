// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/distribute.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DistributeType int32

const (
	DistributeType_DistributeType_UNKNOWN            DistributeType = 0
	DistributeType_DistributeType_API_SUITE          DistributeType = 1 // API测试集合
	DistributeType_DistributeType_API_PLAN           DistributeType = 2 // API测试计划
	DistributeType_DistributeType_INTERFACE_DOCUMENT DistributeType = 3 // 接口文档（即接口集合）
	DistributeType_DistributeType_UI_SUITE           DistributeType = 4 // UI测试集合
	DistributeType_DistributeType_UI_PLAN            DistributeType = 5 // UI测试计划
	DistributeType_DistributeType_API_SERVICE        DistributeType = 6 // 精准测试服务
	DistributeType_DistributeType_PERF_PLAN          DistributeType = 7 // 压力测试计划
	DistributeType_DistributeType_PERF_SUITE         DistributeType = 8 // 压力测试集合
	DistributeType_DistributeType_UI_AGENT_COMPONENT DistributeType = 9 // `UI Agent`组件
)

// Enum value maps for DistributeType.
var (
	DistributeType_name = map[int32]string{
		0: "DistributeType_UNKNOWN",
		1: "DistributeType_API_SUITE",
		2: "DistributeType_API_PLAN",
		3: "DistributeType_INTERFACE_DOCUMENT",
		4: "DistributeType_UI_SUITE",
		5: "DistributeType_UI_PLAN",
		6: "DistributeType_API_SERVICE",
		7: "DistributeType_PERF_PLAN",
		8: "DistributeType_PERF_SUITE",
		9: "DistributeType_UI_AGENT_COMPONENT",
	}
	DistributeType_value = map[string]int32{
		"DistributeType_UNKNOWN":            0,
		"DistributeType_API_SUITE":          1,
		"DistributeType_API_PLAN":           2,
		"DistributeType_INTERFACE_DOCUMENT": 3,
		"DistributeType_UI_SUITE":           4,
		"DistributeType_UI_PLAN":            5,
		"DistributeType_API_SERVICE":        6,
		"DistributeType_PERF_PLAN":          7,
		"DistributeType_PERF_SUITE":         8,
		"DistributeType_UI_AGENT_COMPONENT": 9,
	}
)

func (x DistributeType) Enum() *DistributeType {
	p := new(DistributeType)
	*p = x
	return p
}

func (x DistributeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DistributeType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_distribute_proto_enumTypes[0].Descriptor()
}

func (DistributeType) Type() protoreflect.EnumType {
	return &file_dispatcher_distribute_proto_enumTypes[0]
}

func (x DistributeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DistributeType.Descriptor instead.
func (DistributeType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{0}
}

type DistributeReq struct {
	state          protoimpl.MessageState   `protogen:"open.v1"`
	TriggerMode    pb.TriggerMode           `protobuf:"varint,1,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`                 // 触发模式
	TriggerRule    string                   `protobuf:"bytes,2,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`                                          // 触发规则
	ProjectId      string                   `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                // 项目ID
	TaskId         string                   `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                         // 任务ID
	ExecuteType    pb1.ApiExecutionDataType `protobuf:"varint,5,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"`       // 执行类型
	DistributeType DistributeType           `protobuf:"varint,6,opt,name=distribute_type,json=distributeType,proto3,enum=dispatcher.DistributeType" json:"distribute_type,omitempty"` // 分发类型
	GeneralConfig  *pb.GeneralConfig        `protobuf:"bytes,7,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                    // 通用配置
	AccountConfig  []*pb.AccountConfig      `protobuf:"bytes,8,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                    // 池账号配置
	User           string                   `protobuf:"bytes,9,opt,name=user,proto3" json:"user,omitempty"`                                                                           // 用户
	UserId         string                   `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                        // 用户ID
	PurposeType    pb.PurposeType           `protobuf:"varint,11,opt,name=purpose_type,json=purposeType,proto3,enum=common.PurposeType" json:"purpose_type,omitempty"`                // 计划用途
	PriorityType   pb.PriorityType          `protobuf:"varint,12,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"`            // 优先级
	// Types that are valid to be assigned to Data:
	//
	//	*DistributeReq_Plan
	//	*DistributeReq_Suite
	//	*DistributeReq_InterfaceDocument
	//	*DistributeReq_UiPlan
	//	*DistributeReq_UiSuite
	//	*DistributeReq_Service
	//	*DistributeReq_PerfPlan
	//	*DistributeReq_PerfSuite
	//	*DistributeReq_UiAgentComponent
	Data          isDistributeReq_Data `protobuf_oneof:"data"`
	Debug         bool                 `protobuf:"varint,99,opt,name=debug,proto3" json:"debug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeReq) Reset() {
	*x = DistributeReq{}
	mi := &file_dispatcher_distribute_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeReq) ProtoMessage() {}

func (x *DistributeReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeReq.ProtoReflect.Descriptor instead.
func (*DistributeReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{0}
}

func (x *DistributeReq) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *DistributeReq) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *DistributeReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *DistributeReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DistributeReq) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *DistributeReq) GetDistributeType() DistributeType {
	if x != nil {
		return x.DistributeType
	}
	return DistributeType_DistributeType_UNKNOWN
}

func (x *DistributeReq) GetGeneralConfig() *pb.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *DistributeReq) GetAccountConfig() []*pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *DistributeReq) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *DistributeReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DistributeReq) GetPurposeType() pb.PurposeType {
	if x != nil {
		return x.PurposeType
	}
	return pb.PurposeType(0)
}

func (x *DistributeReq) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *DistributeReq) GetData() isDistributeReq_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DistributeReq) GetPlan() *PlanDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_Plan); ok {
			return x.Plan
		}
	}
	return nil
}

func (x *DistributeReq) GetSuite() *SuiteDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_Suite); ok {
			return x.Suite
		}
	}
	return nil
}

func (x *DistributeReq) GetInterfaceDocument() *InterfaceDocumentDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_InterfaceDocument); ok {
			return x.InterfaceDocument
		}
	}
	return nil
}

func (x *DistributeReq) GetUiPlan() *UIPlanDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_UiPlan); ok {
			return x.UiPlan
		}
	}
	return nil
}

func (x *DistributeReq) GetUiSuite() *UISuiteDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_UiSuite); ok {
			return x.UiSuite
		}
	}
	return nil
}

func (x *DistributeReq) GetService() *ServiceDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_Service); ok {
			return x.Service
		}
	}
	return nil
}

func (x *DistributeReq) GetPerfPlan() *PerfPlanDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_PerfPlan); ok {
			return x.PerfPlan
		}
	}
	return nil
}

func (x *DistributeReq) GetPerfSuite() *PerfSuiteDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_PerfSuite); ok {
			return x.PerfSuite
		}
	}
	return nil
}

func (x *DistributeReq) GetUiAgentComponent() *UIAgentComponentDistributeData {
	if x != nil {
		if x, ok := x.Data.(*DistributeReq_UiAgentComponent); ok {
			return x.UiAgentComponent
		}
	}
	return nil
}

func (x *DistributeReq) GetDebug() bool {
	if x != nil {
		return x.Debug
	}
	return false
}

type isDistributeReq_Data interface {
	isDistributeReq_Data()
}

type DistributeReq_Plan struct {
	Plan *PlanDistributeData `protobuf:"bytes,31,opt,name=plan,proto3,oneof"`
}

type DistributeReq_Suite struct {
	Suite *SuiteDistributeData `protobuf:"bytes,32,opt,name=suite,proto3,oneof"`
}

type DistributeReq_InterfaceDocument struct {
	InterfaceDocument *InterfaceDocumentDistributeData `protobuf:"bytes,33,opt,name=interface_document,json=interfaceDocument,proto3,oneof"`
}

type DistributeReq_UiPlan struct {
	UiPlan *UIPlanDistributeData `protobuf:"bytes,34,opt,name=ui_plan,json=uiPlan,proto3,oneof"`
}

type DistributeReq_UiSuite struct {
	UiSuite *UISuiteDistributeData `protobuf:"bytes,35,opt,name=ui_suite,json=uiSuite,proto3,oneof"`
}

type DistributeReq_Service struct {
	Service *ServiceDistributeData `protobuf:"bytes,36,opt,name=service,proto3,oneof"`
}

type DistributeReq_PerfPlan struct {
	PerfPlan *PerfPlanDistributeData `protobuf:"bytes,37,opt,name=perf_plan,json=perfPlan,proto3,oneof"`
}

type DistributeReq_PerfSuite struct {
	PerfSuite *PerfSuiteDistributeData `protobuf:"bytes,38,opt,name=perf_suite,json=perfSuite,proto3,oneof"`
}

type DistributeReq_UiAgentComponent struct {
	UiAgentComponent *UIAgentComponentDistributeData `protobuf:"bytes,39,opt,name=ui_agent_component,json=uiAgentComponent,proto3,oneof"`
}

func (*DistributeReq_Plan) isDistributeReq_Data() {}

func (*DistributeReq_Suite) isDistributeReq_Data() {}

func (*DistributeReq_InterfaceDocument) isDistributeReq_Data() {}

func (*DistributeReq_UiPlan) isDistributeReq_Data() {}

func (*DistributeReq_UiSuite) isDistributeReq_Data() {}

func (*DistributeReq_Service) isDistributeReq_Data() {}

func (*DistributeReq_PerfPlan) isDistributeReq_Data() {}

func (*DistributeReq_PerfSuite) isDistributeReq_Data() {}

func (*DistributeReq_UiAgentComponent) isDistributeReq_Data() {}

type PlanDistributeData struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	PlanId            string                  `protobuf:"bytes,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                  // 计划ID
	PlanExecuteId     string                  `protobuf:"bytes,2,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`           // 计划执行ID
	State             ComponentState          `protobuf:"varint,3,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                  // 当前状态
	Plan              *pb1.PlanComponent      `protobuf:"bytes,4,opt,name=plan,proto3" json:"plan,omitempty"`                                                    // 计划信息
	Suites            []*pb1.ApiExecutionData `protobuf:"bytes,5,rep,name=suites,proto3" json:"suites,omitempty"`                                                // 其下集合
	InterfaceDocument []*pb1.ApiExecutionData `protobuf:"bytes,6,rep,name=interface_document,json=interfaceDocument,proto3" json:"interface_document,omitempty"` // 其下接口文档
	Services          []*pb1.ApiExecutionData `protobuf:"bytes,7,rep,name=services,proto3" json:"services,omitempty"`                                            // 其下精准测试服务
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PlanDistributeData) Reset() {
	*x = PlanDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanDistributeData) ProtoMessage() {}

func (x *PlanDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanDistributeData.ProtoReflect.Descriptor instead.
func (*PlanDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{1}
}

func (x *PlanDistributeData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PlanDistributeData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PlanDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *PlanDistributeData) GetPlan() *pb1.PlanComponent {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *PlanDistributeData) GetSuites() []*pb1.ApiExecutionData {
	if x != nil {
		return x.Suites
	}
	return nil
}

func (x *PlanDistributeData) GetInterfaceDocument() []*pb1.ApiExecutionData {
	if x != nil {
		return x.InterfaceDocument
	}
	return nil
}

func (x *PlanDistributeData) GetServices() []*pb1.ApiExecutionData {
	if x != nil {
		return x.Services
	}
	return nil
}

type SuiteDistributeData struct {
	state          protoimpl.MessageState  `protogen:"open.v1"`
	SuiteId        string                  `protobuf:"bytes,1,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                        // 集合ID
	SuiteExecuteId string                  `protobuf:"bytes,2,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"` // 集合执行ID
	PlanId         string                  `protobuf:"bytes,3,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                           // 计划ID
	PlanExecuteId  string                  `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`    // 计划执行ID
	State          ComponentState          `protobuf:"varint,5,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`           // 集合状态
	Suite          *pb1.SuiteComponent     `protobuf:"bytes,6,opt,name=suite,proto3" json:"suite,omitempty"`                                           // 集合信息
	Cases          []*pb1.ApiExecutionData `protobuf:"bytes,7,rep,name=cases,proto3" json:"cases,omitempty"`                                           // 其下执行用例
	PlanName       string                  `protobuf:"bytes,11,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                    // 计划名称
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SuiteDistributeData) Reset() {
	*x = SuiteDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuiteDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuiteDistributeData) ProtoMessage() {}

func (x *SuiteDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuiteDistributeData.ProtoReflect.Descriptor instead.
func (*SuiteDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{2}
}

func (x *SuiteDistributeData) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SuiteDistributeData) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *SuiteDistributeData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SuiteDistributeData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *SuiteDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *SuiteDistributeData) GetSuite() *pb1.SuiteComponent {
	if x != nil {
		return x.Suite
	}
	return nil
}

func (x *SuiteDistributeData) GetCases() []*pb1.ApiExecutionData {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *SuiteDistributeData) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type InterfaceDocumentDistributeData struct {
	state                      protoimpl.MessageState          `protogen:"open.v1"`
	InterfaceDocumentId        string                          `protobuf:"bytes,1,opt,name=interface_document_id,json=interfaceDocumentId,proto3" json:"interface_document_id,omitempty"`                        // 接口文档ID
	InterfaceDocumentExecuteId string                          `protobuf:"bytes,2,opt,name=interface_document_execute_id,json=interfaceDocumentExecuteId,proto3" json:"interface_document_execute_id,omitempty"` // 接口文档执行ID
	PlanId                     string                          `protobuf:"bytes,3,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                 // 计划ID
	PlanExecuteId              string                          `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                                          // 计划执行ID
	State                      ComponentState                  `protobuf:"varint,5,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                                                 // 当前状态
	InterfaceDocument          *pb1.InterfaceDocumentComponent `protobuf:"bytes,6,opt,name=interface_document,json=interfaceDocument,proto3" json:"interface_document,omitempty"`                                // 接口文档信息
	InterfaceCases             []*pb1.ApiExecutionData         `protobuf:"bytes,7,rep,name=interface_cases,json=interfaceCases,proto3" json:"interface_cases,omitempty"`                                         // 其下执行接口用例
	PlanName                   string                          `protobuf:"bytes,11,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                          // 计划名称
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *InterfaceDocumentDistributeData) Reset() {
	*x = InterfaceDocumentDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceDocumentDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceDocumentDistributeData) ProtoMessage() {}

func (x *InterfaceDocumentDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceDocumentDistributeData.ProtoReflect.Descriptor instead.
func (*InterfaceDocumentDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{3}
}

func (x *InterfaceDocumentDistributeData) GetInterfaceDocumentId() string {
	if x != nil {
		return x.InterfaceDocumentId
	}
	return ""
}

func (x *InterfaceDocumentDistributeData) GetInterfaceDocumentExecuteId() string {
	if x != nil {
		return x.InterfaceDocumentExecuteId
	}
	return ""
}

func (x *InterfaceDocumentDistributeData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *InterfaceDocumentDistributeData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *InterfaceDocumentDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *InterfaceDocumentDistributeData) GetInterfaceDocument() *pb1.InterfaceDocumentComponent {
	if x != nil {
		return x.InterfaceDocument
	}
	return nil
}

func (x *InterfaceDocumentDistributeData) GetInterfaceCases() []*pb1.ApiExecutionData {
	if x != nil {
		return x.InterfaceCases
	}
	return nil
}

func (x *InterfaceDocumentDistributeData) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type UIPlanDistributeData struct {
	state           protoimpl.MessageState  `protogen:"open.v1"`
	UiPlanId        string                  `protobuf:"bytes,1,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`                        // 计划id
	UiPlanExecuteId string                  `protobuf:"bytes,2,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"` // 计划执行id
	State           ComponentState          `protobuf:"varint,3,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                // 当前状态
	UiPlan          *pb1.UIPlanComponent    `protobuf:"bytes,4,opt,name=ui_plan,json=uiPlan,proto3" json:"ui_plan,omitempty"`                                // 计划信息
	UiSuites        []*pb1.ApiExecutionData `protobuf:"bytes,5,rep,name=ui_suites,json=uiSuites,proto3" json:"ui_suites,omitempty"`                          // 其下集合
	MetaData        *pb1.UIPlanMetaData     `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                          // ui用例数据
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UIPlanDistributeData) Reset() {
	*x = UIPlanDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIPlanDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIPlanDistributeData) ProtoMessage() {}

func (x *UIPlanDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIPlanDistributeData.ProtoReflect.Descriptor instead.
func (*UIPlanDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{4}
}

func (x *UIPlanDistributeData) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UIPlanDistributeData) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UIPlanDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *UIPlanDistributeData) GetUiPlan() *pb1.UIPlanComponent {
	if x != nil {
		return x.UiPlan
	}
	return nil
}

func (x *UIPlanDistributeData) GetUiSuites() []*pb1.ApiExecutionData {
	if x != nil {
		return x.UiSuites
	}
	return nil
}

func (x *UIPlanDistributeData) GetMetaData() *pb1.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

type UISuiteDistributeData struct {
	state            protoimpl.MessageState  `protogen:"open.v1"`
	UiSuiteId        string                  `protobuf:"bytes,1,opt,name=ui_suite_id,json=uiSuiteId,proto3" json:"ui_suite_id,omitempty"`                        // 集合id
	UiSuiteExecuteId string                  `protobuf:"bytes,2,opt,name=ui_suite_execute_id,json=uiSuiteExecuteId,proto3" json:"ui_suite_execute_id,omitempty"` // 集合执行id
	UiPlanId         string                  `protobuf:"bytes,3,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`                           // 计划id
	UiPlanExecuteId  string                  `protobuf:"bytes,4,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"`    // 计划执行id
	State            ComponentState          `protobuf:"varint,5,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                   // 集合状态
	UiSuite          *pb1.UISuiteComponent   `protobuf:"bytes,6,opt,name=ui_suite,json=uiSuite,proto3" json:"ui_suite,omitempty"`                                // 集合信息
	UiCases          []*pb1.ApiExecutionData `protobuf:"bytes,7,rep,name=ui_cases,json=uiCases,proto3" json:"ui_cases,omitempty"`                                // 其下执行用例
	MetaData         *pb1.UIPlanMetaData     `protobuf:"bytes,8,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                             // ui用例数据
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UISuiteDistributeData) Reset() {
	*x = UISuiteDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UISuiteDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UISuiteDistributeData) ProtoMessage() {}

func (x *UISuiteDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UISuiteDistributeData.ProtoReflect.Descriptor instead.
func (*UISuiteDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{5}
}

func (x *UISuiteDistributeData) GetUiSuiteId() string {
	if x != nil {
		return x.UiSuiteId
	}
	return ""
}

func (x *UISuiteDistributeData) GetUiSuiteExecuteId() string {
	if x != nil {
		return x.UiSuiteExecuteId
	}
	return ""
}

func (x *UISuiteDistributeData) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UISuiteDistributeData) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UISuiteDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *UISuiteDistributeData) GetUiSuite() *pb1.UISuiteComponent {
	if x != nil {
		return x.UiSuite
	}
	return nil
}

func (x *UISuiteDistributeData) GetUiCases() []*pb1.ApiExecutionData {
	if x != nil {
		return x.UiCases
	}
	return nil
}

func (x *UISuiteDistributeData) GetMetaData() *pb1.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

// ServiceDistributeData 精准测试服务
type ServiceDistributeData struct {
	state            protoimpl.MessageState  `protogen:"open.v1"`
	ServiceId        string                  `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`                        // 服务ID
	ServiceExecuteId string                  `protobuf:"bytes,2,opt,name=service_execute_id,json=serviceExecuteId,proto3" json:"service_execute_id,omitempty"` // 服务执行ID
	ServiceName      string                  `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`                  // 服务名称
	PlanId           string                  `protobuf:"bytes,4,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                 // 计划ID
	PlanExecuteId    string                  `protobuf:"bytes,5,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`          // 计划执行ID
	Service          *pb1.ServiceComponent   `protobuf:"bytes,6,opt,name=service,proto3" json:"service,omitempty"`                                             // 集合信息
	Cases            []*pb1.ApiExecutionData `protobuf:"bytes,7,rep,name=cases,proto3" json:"cases,omitempty"`                                                 // 其下执行用例
	InterfaceCases   []*pb1.ApiExecutionData `protobuf:"bytes,8,rep,name=interface_cases,json=interfaceCases,proto3" json:"interface_cases,omitempty"`         // 其下执行接口用例
	PlanName         string                  `protobuf:"bytes,11,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                          // 计划名称
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ServiceDistributeData) Reset() {
	*x = ServiceDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDistributeData) ProtoMessage() {}

func (x *ServiceDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDistributeData.ProtoReflect.Descriptor instead.
func (*ServiceDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceDistributeData) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceDistributeData) GetServiceExecuteId() string {
	if x != nil {
		return x.ServiceExecuteId
	}
	return ""
}

func (x *ServiceDistributeData) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceDistributeData) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *ServiceDistributeData) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *ServiceDistributeData) GetService() *pb1.ServiceComponent {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ServiceDistributeData) GetCases() []*pb1.ApiExecutionData {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *ServiceDistributeData) GetInterfaceCases() []*pb1.ApiExecutionData {
	if x != nil {
		return x.InterfaceCases
	}
	return nil
}

func (x *ServiceDistributeData) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type PerfPlanDistributeData struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	PerfPlanId        string                  `protobuf:"bytes,1,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                        // 计划ID
	PerfPlanExecuteId string                  `protobuf:"bytes,2,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"` // 计划执行ID
	State             ComponentState          `protobuf:"varint,11,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                     // 计划状态
	PerfPlan          *pb1.PerfPlanComponent  `protobuf:"bytes,12,opt,name=perf_plan,json=perfPlan,proto3" json:"perf_plan,omitempty"`                               // 计划数据
	PerfSuites        []*pb1.ApiExecutionData `protobuf:"bytes,13,rep,name=perf_suites,json=perfSuites,proto3" json:"perf_suites,omitempty"`                         // 集合数据
	PerfPlanInfo      *PerfPlanInfo           `protobuf:"bytes,21,opt,name=perf_plan_info,json=perfPlanInfo,proto3" json:"perf_plan_info,omitempty"`                 // 计划执行信息
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PerfPlanDistributeData) Reset() {
	*x = PerfPlanDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanDistributeData) ProtoMessage() {}

func (x *PerfPlanDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanDistributeData.ProtoReflect.Descriptor instead.
func (*PerfPlanDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{7}
}

func (x *PerfPlanDistributeData) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfPlanDistributeData) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfPlanDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *PerfPlanDistributeData) GetPerfPlan() *pb1.PerfPlanComponent {
	if x != nil {
		return x.PerfPlan
	}
	return nil
}

func (x *PerfPlanDistributeData) GetPerfSuites() []*pb1.ApiExecutionData {
	if x != nil {
		return x.PerfSuites
	}
	return nil
}

func (x *PerfPlanDistributeData) GetPerfPlanInfo() *PerfPlanInfo {
	if x != nil {
		return x.PerfPlanInfo
	}
	return nil
}

type PerfSuiteDistributeData struct {
	state              protoimpl.MessageState  `protogen:"open.v1"`
	PerfSuiteId        string                  `protobuf:"bytes,1,opt,name=perf_suite_id,json=perfSuiteId,proto3" json:"perf_suite_id,omitempty"`                        // 集合ID
	PerfSuiteExecuteId string                  `protobuf:"bytes,2,opt,name=perf_suite_execute_id,json=perfSuiteExecuteId,proto3" json:"perf_suite_execute_id,omitempty"` // 集合执行ID
	PerfPlanId         string                  `protobuf:"bytes,3,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                           // 计划ID
	PerfPlanExecuteId  string                  `protobuf:"bytes,4,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"`    // 计划执行ID
	State              ComponentState          `protobuf:"varint,11,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                        // 集合状态
	PerfSuite          *pb1.PerfSuiteComponent `protobuf:"bytes,12,opt,name=perf_suite,json=perfSuite,proto3" json:"perf_suite,omitempty"`                               // 集合数据
	PerfCases          []*pb1.ApiExecutionData `protobuf:"bytes,13,rep,name=perf_cases,json=perfCases,proto3" json:"perf_cases,omitempty"`                               // 用例数据
	MetaData           *pb1.PerfPlanMetaData   `protobuf:"bytes,14,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                  // 计划元数据
	PerfPlanInfo       *PerfPlanInfo           `protobuf:"bytes,21,opt,name=perf_plan_info,json=perfPlanInfo,proto3" json:"perf_plan_info,omitempty"`                    // 计划执行信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PerfSuiteDistributeData) Reset() {
	*x = PerfSuiteDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfSuiteDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfSuiteDistributeData) ProtoMessage() {}

func (x *PerfSuiteDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfSuiteDistributeData.ProtoReflect.Descriptor instead.
func (*PerfSuiteDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{8}
}

func (x *PerfSuiteDistributeData) GetPerfSuiteId() string {
	if x != nil {
		return x.PerfSuiteId
	}
	return ""
}

func (x *PerfSuiteDistributeData) GetPerfSuiteExecuteId() string {
	if x != nil {
		return x.PerfSuiteExecuteId
	}
	return ""
}

func (x *PerfSuiteDistributeData) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfSuiteDistributeData) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfSuiteDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *PerfSuiteDistributeData) GetPerfSuite() *pb1.PerfSuiteComponent {
	if x != nil {
		return x.PerfSuite
	}
	return nil
}

func (x *PerfSuiteDistributeData) GetPerfCases() []*pb1.ApiExecutionData {
	if x != nil {
		return x.PerfCases
	}
	return nil
}

func (x *PerfSuiteDistributeData) GetMetaData() *pb1.PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *PerfSuiteDistributeData) GetPerfPlanInfo() *PerfPlanInfo {
	if x != nil {
		return x.PerfPlanInfo
	}
	return nil
}

type UIAgentComponentDistributeData struct {
	state              protoimpl.MessageState         `protogen:"open.v1"`
	ComponentId        string                         `protobuf:"bytes,1,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"`                        // 组件ID
	ComponentExecuteId string                         `protobuf:"bytes,2,opt,name=component_execute_id,json=componentExecuteId,proto3" json:"component_execute_id,omitempty"` // 组件执行ID
	State              ComponentState                 `protobuf:"varint,11,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`                      // 组件状态
	Component          *pb1.UIAgentComponentComponent `protobuf:"bytes,12,opt,name=component,proto3" json:"component,omitempty"`                                              // 组件数据
	ComponentInfo      *UIAgentComponentInfo          `protobuf:"bytes,21,opt,name=component_info,json=componentInfo,proto3" json:"component_info,omitempty"`                 // 组件执行信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UIAgentComponentDistributeData) Reset() {
	*x = UIAgentComponentDistributeData{}
	mi := &file_dispatcher_distribute_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentDistributeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentDistributeData) ProtoMessage() {}

func (x *UIAgentComponentDistributeData) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_distribute_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentDistributeData.ProtoReflect.Descriptor instead.
func (*UIAgentComponentDistributeData) Descriptor() ([]byte, []int) {
	return file_dispatcher_distribute_proto_rawDescGZIP(), []int{9}
}

func (x *UIAgentComponentDistributeData) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *UIAgentComponentDistributeData) GetComponentExecuteId() string {
	if x != nil {
		return x.ComponentExecuteId
	}
	return ""
}

func (x *UIAgentComponentDistributeData) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *UIAgentComponentDistributeData) GetComponent() *pb1.UIAgentComponentComponent {
	if x != nil {
		return x.Component
	}
	return nil
}

func (x *UIAgentComponentDistributeData) GetComponentInfo() *UIAgentComponentInfo {
	if x != nil {
		return x.ComponentInfo
	}
	return nil
}

var File_dispatcher_distribute_proto protoreflect.FileDescriptor

var file_dispatcher_distribute_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd1, 0x09, 0x0a,
	0x0d, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x36,
	0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x36, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x50, 0x6c, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x37, 0x0a, 0x05, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x69, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x05, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x12, 0x5c, 0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x11,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x3b, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x22, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x3e,
	0x0a, 0x08, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49,
	0x53, 0x75, 0x69, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x07, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x3d,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x41, 0x0a,
	0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e,
	0x12, 0x44, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x26,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x70, 0x65, 0x72,
	0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x27, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x10, 0x75, 0x69, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x63, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xe7, 0x02, 0x0a, 0x12, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x70, 0x6c,
	0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x75, 0x69, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x06, 0x73, 0x75, 0x69, 0x74, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x12, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0xca, 0x02, 0x0a, 0x13, 0x53,
	0x75, 0x69, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x05, 0x73, 0x75, 0x69, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc0, 0x03, 0x0a, 0x1f, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x15, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x41, 0x0a, 0x1d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb4, 0x02, 0x0a, 0x14, 0x55,
	0x49, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49,
	0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75,
	0x69, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x31, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c,
	0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x75, 0x69, 0x50,
	0x6c, 0x61, 0x6e, 0x12, 0x36, 0x0a, 0x09, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x85, 0x03, 0x0a, 0x15, 0x55, 0x49, 0x53, 0x75, 0x69, 0x74, 0x65, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b, 0x75,
	0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75,
	0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74,
	0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69,
	0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x69, 0x5f, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x34, 0x0a,
	0x08, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x75, 0x69, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0x8f, 0x03, 0x0a, 0x15, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd2, 0x02, 0x0a, 0x16,
	0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65,
	0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x70,
	0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66,
	0x50, 0x6c, 0x61, 0x6e, 0x12, 0x3a, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x73,
	0x12, 0x3e, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0xe3, 0x03, 0x0a, 0x17, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0d,
	0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x50,
	0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x65, 0x72, 0x66, 0x53,
	0x75, 0x69, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x09, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x36,
	0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c,
	0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb2, 0x02, 0x0a, 0x1e, 0x55, 0x49, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x40, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x47, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2a, 0xcb, 0x02, 0x0a, 0x0e,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49,
	0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x50,
	0x4c, 0x41, 0x4e, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43,
	0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55,
	0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x50,
	0x4c, 0x41, 0x4e, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x50, 0x4c, 0x41,
	0x4e, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45,
	0x10, 0x08, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x09, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_distribute_proto_rawDescOnce sync.Once
	file_dispatcher_distribute_proto_rawDescData = file_dispatcher_distribute_proto_rawDesc
)

func file_dispatcher_distribute_proto_rawDescGZIP() []byte {
	file_dispatcher_distribute_proto_rawDescOnce.Do(func() {
		file_dispatcher_distribute_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_distribute_proto_rawDescData)
	})
	return file_dispatcher_distribute_proto_rawDescData
}

var file_dispatcher_distribute_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dispatcher_distribute_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_dispatcher_distribute_proto_goTypes = []any{
	(DistributeType)(0),                     // 0: dispatcher.DistributeType
	(*DistributeReq)(nil),                   // 1: dispatcher.DistributeReq
	(*PlanDistributeData)(nil),              // 2: dispatcher.PlanDistributeData
	(*SuiteDistributeData)(nil),             // 3: dispatcher.SuiteDistributeData
	(*InterfaceDocumentDistributeData)(nil), // 4: dispatcher.InterfaceDocumentDistributeData
	(*UIPlanDistributeData)(nil),            // 5: dispatcher.UIPlanDistributeData
	(*UISuiteDistributeData)(nil),           // 6: dispatcher.UISuiteDistributeData
	(*ServiceDistributeData)(nil),           // 7: dispatcher.ServiceDistributeData
	(*PerfPlanDistributeData)(nil),          // 8: dispatcher.PerfPlanDistributeData
	(*PerfSuiteDistributeData)(nil),         // 9: dispatcher.PerfSuiteDistributeData
	(*UIAgentComponentDistributeData)(nil),  // 10: dispatcher.UIAgentComponentDistributeData
	(pb.TriggerMode)(0),                     // 11: common.TriggerMode
	(pb1.ApiExecutionDataType)(0),           // 12: manager.ApiExecutionDataType
	(*pb.GeneralConfig)(nil),                // 13: common.GeneralConfig
	(*pb.AccountConfig)(nil),                // 14: common.AccountConfig
	(pb.PurposeType)(0),                     // 15: common.PurposeType
	(pb.PriorityType)(0),                    // 16: common.PriorityType
	(ComponentState)(0),                     // 17: dispatcher.ComponentState
	(*pb1.PlanComponent)(nil),               // 18: manager.PlanComponent
	(*pb1.ApiExecutionData)(nil),            // 19: manager.ApiExecutionData
	(*pb1.SuiteComponent)(nil),              // 20: manager.SuiteComponent
	(*pb1.InterfaceDocumentComponent)(nil),  // 21: manager.InterfaceDocumentComponent
	(*pb1.UIPlanComponent)(nil),             // 22: manager.UIPlanComponent
	(*pb1.UIPlanMetaData)(nil),              // 23: manager.UIPlanMetaData
	(*pb1.UISuiteComponent)(nil),            // 24: manager.UISuiteComponent
	(*pb1.ServiceComponent)(nil),            // 25: manager.ServiceComponent
	(*pb1.PerfPlanComponent)(nil),           // 26: manager.PerfPlanComponent
	(*PerfPlanInfo)(nil),                    // 27: dispatcher.PerfPlanInfo
	(*pb1.PerfSuiteComponent)(nil),          // 28: manager.PerfSuiteComponent
	(*pb1.PerfPlanMetaData)(nil),            // 29: manager.PerfPlanMetaData
	(*pb1.UIAgentComponentComponent)(nil),   // 30: manager.UIAgentComponentComponent
	(*UIAgentComponentInfo)(nil),            // 31: dispatcher.UIAgentComponentInfo
}
var file_dispatcher_distribute_proto_depIdxs = []int32{
	11, // 0: dispatcher.DistributeReq.trigger_mode:type_name -> common.TriggerMode
	12, // 1: dispatcher.DistributeReq.execute_type:type_name -> manager.ApiExecutionDataType
	0,  // 2: dispatcher.DistributeReq.distribute_type:type_name -> dispatcher.DistributeType
	13, // 3: dispatcher.DistributeReq.general_config:type_name -> common.GeneralConfig
	14, // 4: dispatcher.DistributeReq.account_config:type_name -> common.AccountConfig
	15, // 5: dispatcher.DistributeReq.purpose_type:type_name -> common.PurposeType
	16, // 6: dispatcher.DistributeReq.priority_type:type_name -> common.PriorityType
	2,  // 7: dispatcher.DistributeReq.plan:type_name -> dispatcher.PlanDistributeData
	3,  // 8: dispatcher.DistributeReq.suite:type_name -> dispatcher.SuiteDistributeData
	4,  // 9: dispatcher.DistributeReq.interface_document:type_name -> dispatcher.InterfaceDocumentDistributeData
	5,  // 10: dispatcher.DistributeReq.ui_plan:type_name -> dispatcher.UIPlanDistributeData
	6,  // 11: dispatcher.DistributeReq.ui_suite:type_name -> dispatcher.UISuiteDistributeData
	7,  // 12: dispatcher.DistributeReq.service:type_name -> dispatcher.ServiceDistributeData
	8,  // 13: dispatcher.DistributeReq.perf_plan:type_name -> dispatcher.PerfPlanDistributeData
	9,  // 14: dispatcher.DistributeReq.perf_suite:type_name -> dispatcher.PerfSuiteDistributeData
	10, // 15: dispatcher.DistributeReq.ui_agent_component:type_name -> dispatcher.UIAgentComponentDistributeData
	17, // 16: dispatcher.PlanDistributeData.state:type_name -> dispatcher.ComponentState
	18, // 17: dispatcher.PlanDistributeData.plan:type_name -> manager.PlanComponent
	19, // 18: dispatcher.PlanDistributeData.suites:type_name -> manager.ApiExecutionData
	19, // 19: dispatcher.PlanDistributeData.interface_document:type_name -> manager.ApiExecutionData
	19, // 20: dispatcher.PlanDistributeData.services:type_name -> manager.ApiExecutionData
	17, // 21: dispatcher.SuiteDistributeData.state:type_name -> dispatcher.ComponentState
	20, // 22: dispatcher.SuiteDistributeData.suite:type_name -> manager.SuiteComponent
	19, // 23: dispatcher.SuiteDistributeData.cases:type_name -> manager.ApiExecutionData
	17, // 24: dispatcher.InterfaceDocumentDistributeData.state:type_name -> dispatcher.ComponentState
	21, // 25: dispatcher.InterfaceDocumentDistributeData.interface_document:type_name -> manager.InterfaceDocumentComponent
	19, // 26: dispatcher.InterfaceDocumentDistributeData.interface_cases:type_name -> manager.ApiExecutionData
	17, // 27: dispatcher.UIPlanDistributeData.state:type_name -> dispatcher.ComponentState
	22, // 28: dispatcher.UIPlanDistributeData.ui_plan:type_name -> manager.UIPlanComponent
	19, // 29: dispatcher.UIPlanDistributeData.ui_suites:type_name -> manager.ApiExecutionData
	23, // 30: dispatcher.UIPlanDistributeData.meta_data:type_name -> manager.UIPlanMetaData
	17, // 31: dispatcher.UISuiteDistributeData.state:type_name -> dispatcher.ComponentState
	24, // 32: dispatcher.UISuiteDistributeData.ui_suite:type_name -> manager.UISuiteComponent
	19, // 33: dispatcher.UISuiteDistributeData.ui_cases:type_name -> manager.ApiExecutionData
	23, // 34: dispatcher.UISuiteDistributeData.meta_data:type_name -> manager.UIPlanMetaData
	25, // 35: dispatcher.ServiceDistributeData.service:type_name -> manager.ServiceComponent
	19, // 36: dispatcher.ServiceDistributeData.cases:type_name -> manager.ApiExecutionData
	19, // 37: dispatcher.ServiceDistributeData.interface_cases:type_name -> manager.ApiExecutionData
	17, // 38: dispatcher.PerfPlanDistributeData.state:type_name -> dispatcher.ComponentState
	26, // 39: dispatcher.PerfPlanDistributeData.perf_plan:type_name -> manager.PerfPlanComponent
	19, // 40: dispatcher.PerfPlanDistributeData.perf_suites:type_name -> manager.ApiExecutionData
	27, // 41: dispatcher.PerfPlanDistributeData.perf_plan_info:type_name -> dispatcher.PerfPlanInfo
	17, // 42: dispatcher.PerfSuiteDistributeData.state:type_name -> dispatcher.ComponentState
	28, // 43: dispatcher.PerfSuiteDistributeData.perf_suite:type_name -> manager.PerfSuiteComponent
	19, // 44: dispatcher.PerfSuiteDistributeData.perf_cases:type_name -> manager.ApiExecutionData
	29, // 45: dispatcher.PerfSuiteDistributeData.meta_data:type_name -> manager.PerfPlanMetaData
	27, // 46: dispatcher.PerfSuiteDistributeData.perf_plan_info:type_name -> dispatcher.PerfPlanInfo
	17, // 47: dispatcher.UIAgentComponentDistributeData.state:type_name -> dispatcher.ComponentState
	30, // 48: dispatcher.UIAgentComponentDistributeData.component:type_name -> manager.UIAgentComponentComponent
	31, // 49: dispatcher.UIAgentComponentDistributeData.component_info:type_name -> dispatcher.UIAgentComponentInfo
	50, // [50:50] is the sub-list for method output_type
	50, // [50:50] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_dispatcher_distribute_proto_init() }
func file_dispatcher_distribute_proto_init() {
	if File_dispatcher_distribute_proto != nil {
		return
	}
	file_dispatcher_base_proto_init()
	file_dispatcher_distribute_proto_msgTypes[0].OneofWrappers = []any{
		(*DistributeReq_Plan)(nil),
		(*DistributeReq_Suite)(nil),
		(*DistributeReq_InterfaceDocument)(nil),
		(*DistributeReq_UiPlan)(nil),
		(*DistributeReq_UiSuite)(nil),
		(*DistributeReq_Service)(nil),
		(*DistributeReq_PerfPlan)(nil),
		(*DistributeReq_PerfSuite)(nil),
		(*DistributeReq_UiAgentComponent)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_distribute_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_distribute_proto_goTypes,
		DependencyIndexes: file_dispatcher_distribute_proto_depIdxs,
		EnumInfos:         file_dispatcher_distribute_proto_enumTypes,
		MessageInfos:      file_dispatcher_distribute_proto_msgTypes,
	}.Build()
	File_dispatcher_distribute_proto = out.File
	file_dispatcher_distribute_proto_rawDesc = nil
	file_dispatcher_distribute_proto_goTypes = nil
	file_dispatcher_distribute_proto_depIdxs = nil
}
