// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/base.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	_ "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 组件状态
type ComponentState int32

const (
	ComponentState_Pending ComponentState = 0
	ComponentState_Init    ComponentState = 1
	ComponentState_Started ComponentState = 2
	ComponentState_Success ComponentState = 3
	ComponentState_Waiting ComponentState = 4
	ComponentState_Warning ComponentState = 11
	ComponentState_Skip    ComponentState = 12
	ComponentState_Failure ComponentState = 21
	ComponentState_Panic   ComponentState = 22
	ComponentState_Stop    ComponentState = 23
	ComponentState_Invalid ComponentState = 99
)

// Enum value maps for ComponentState.
var (
	ComponentState_name = map[int32]string{
		0:  "Pending",
		1:  "Init",
		2:  "Started",
		3:  "Success",
		4:  "Waiting",
		11: "Warning",
		12: "Skip",
		21: "Failure",
		22: "Panic",
		23: "Stop",
		99: "Invalid",
	}
	ComponentState_value = map[string]int32{
		"Pending": 0,
		"Init":    1,
		"Started": 2,
		"Success": 3,
		"Waiting": 4,
		"Warning": 11,
		"Skip":    12,
		"Failure": 21,
		"Panic":   22,
		"Stop":    23,
		"Invalid": 99,
	}
)

func (x ComponentState) Enum() *ComponentState {
	p := new(ComponentState)
	*p = x
	return p
}

func (x ComponentState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComponentState) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_base_proto_enumTypes[0].Descriptor()
}

func (ComponentState) Type() protoreflect.EnumType {
	return &file_dispatcher_base_proto_enumTypes[0]
}

func (x ComponentState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComponentState.Descriptor instead.
func (ComponentState) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{0}
}

type TaskPageType int32

const (
	TaskPageType_PT_ALL     TaskPageType = 0 // 缺省
	TaskPageType_PT_ING     TaskPageType = 1 // 处理中
	TaskPageType_PT_ARCHIVE TaskPageType = 2 // 归档
)

// Enum value maps for TaskPageType.
var (
	TaskPageType_name = map[int32]string{
		0: "PT_ALL",
		1: "PT_ING",
		2: "PT_ARCHIVE",
	}
	TaskPageType_value = map[string]int32{
		"PT_ALL":     0,
		"PT_ING":     1,
		"PT_ARCHIVE": 2,
	}
)

func (x TaskPageType) Enum() *TaskPageType {
	p := new(TaskPageType)
	*p = x
	return p
}

func (x TaskPageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskPageType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_base_proto_enumTypes[1].Descriptor()
}

func (TaskPageType) Type() protoreflect.EnumType {
	return &file_dispatcher_base_proto_enumTypes[1]
}

func (x TaskPageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskPageType.Descriptor instead.
func (TaskPageType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{1}
}

type StageType int32

const (
	StageType_ST_NULL     StageType = 0
	StageType_ST_PREVIEW  StageType = 1 // 预告
	StageType_ST_STARTED  StageType = 2 // 开始
	StageType_ST_FINISHED StageType = 3 // 结束
	StageType_ST_ADVANCED StageType = 4 // 提前
)

// Enum value maps for StageType.
var (
	StageType_name = map[int32]string{
		0: "ST_NULL",
		1: "ST_PREVIEW",
		2: "ST_STARTED",
		3: "ST_FINISHED",
		4: "ST_ADVANCED",
	}
	StageType_value = map[string]int32{
		"ST_NULL":     0,
		"ST_PREVIEW":  1,
		"ST_STARTED":  2,
		"ST_FINISHED": 3,
		"ST_ADVANCED": 4,
	}
)

func (x StageType) Enum() *StageType {
	p := new(StageType)
	*p = x
	return p
}

func (x StageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StageType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_base_proto_enumTypes[2].Descriptor()
}

func (StageType) Type() protoreflect.EnumType {
	return &file_dispatcher_base_proto_enumTypes[2]
}

func (x StageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StageType.Descriptor instead.
func (StageType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{2}
}

type StopType int32

const (
	StopType_StopType_Unknown StopType = 0
	StopType_StopType_Manual  StopType = 1 // 手动
	StopType_StopType_Auto    StopType = 2 // 自动
)

// Enum value maps for StopType.
var (
	StopType_name = map[int32]string{
		0: "StopType_Unknown",
		1: "StopType_Manual",
		2: "StopType_Auto",
	}
	StopType_value = map[string]int32{
		"StopType_Unknown": 0,
		"StopType_Manual":  1,
		"StopType_Auto":    2,
	}
)

func (x StopType) Enum() *StopType {
	p := new(StopType)
	*p = x
	return p
}

func (x StopType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StopType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_base_proto_enumTypes[3].Descriptor()
}

func (StopType) Type() protoreflect.EnumType {
	return &file_dispatcher_base_proto_enumTypes[3]
}

func (x StopType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StopType.Descriptor instead.
func (StopType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{3}
}

type ComponentKey struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	ComponentId   string                  `protobuf:"bytes,1,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"`
	ComponentType pb.ApiExecutionDataType `protobuf:"varint,2,opt,name=component_type,json=componentType,proto3,enum=manager.ApiExecutionDataType" json:"component_type,omitempty"`
	Version       string                  `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ComponentKey) Reset() {
	*x = ComponentKey{}
	mi := &file_dispatcher_base_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentKey) ProtoMessage() {}

func (x *ComponentKey) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentKey.ProtoReflect.Descriptor instead.
func (*ComponentKey) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{0}
}

func (x *ComponentKey) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *ComponentKey) GetComponentType() pb.ApiExecutionDataType {
	if x != nil {
		return x.ComponentType
	}
	return pb.ApiExecutionDataType(0)
}

func (x *ComponentKey) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type ComponentExecuteKey struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Key                *ComponentKey          `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ComponentExecuteId string                 `protobuf:"bytes,2,opt,name=component_execute_id,json=componentExecuteId,proto3" json:"component_execute_id,omitempty"`
	State              ComponentState         `protobuf:"varint,3,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ComponentExecuteKey) Reset() {
	*x = ComponentExecuteKey{}
	mi := &file_dispatcher_base_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentExecuteKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentExecuteKey) ProtoMessage() {}

func (x *ComponentExecuteKey) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentExecuteKey.ProtoReflect.Descriptor instead.
func (*ComponentExecuteKey) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{1}
}

func (x *ComponentExecuteKey) GetKey() *ComponentKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *ComponentExecuteKey) GetComponentExecuteId() string {
	if x != nil {
		return x.ComponentExecuteId
	}
	return ""
}

func (x *ComponentExecuteKey) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

type TaskParams struct {
	state              protoimpl.MessageState  `protogen:"open.v1"`
	TriggerMode        pb1.TriggerMode         `protobuf:"varint,1,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"` // 触发模式
	TriggerRule        string                  `protobuf:"bytes,2,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`                          // 触发规则
	ProjectId          string                  `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TaskId             string                  `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	ExecuteId          string                  `protobuf:"bytes,5,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`
	ExecuteType        pb.ApiExecutionDataType `protobuf:"varint,6,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"`
	ParentComponent    *ComponentExecuteKey    `protobuf:"bytes,7,opt,name=parent_component,json=parentComponent,proto3" json:"parent_component,omitempty"`
	CurrentComponent   *ComponentExecuteKey    `protobuf:"bytes,8,opt,name=current_component,json=currentComponent,proto3" json:"current_component,omitempty"`
	GeneralConfig      *pb1.GeneralConfig      `protobuf:"bytes,9,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`
	AccountConfig      []*pb1.AccountConfig    `protobuf:"bytes,10,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`
	User               string                  `protobuf:"bytes,11,opt,name=user,proto3" json:"user,omitempty"`
	UserId             string                  `protobuf:"bytes,12,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	State              ComponentState          `protobuf:"varint,13,opt,name=state,proto3,enum=dispatcher.ComponentState" json:"state,omitempty"`
	InterfaceCaseIds   []*ComponentExecuteKey  `protobuf:"bytes,14,rep,name=interface_case_ids,json=interfaceCaseIds,proto3" json:"interface_case_ids,omitempty"`
	SuiteExecutionMode pb.ExecutionMode        `protobuf:"varint,15,opt,name=suite_execution_mode,json=suiteExecutionMode,proto3,enum=manager.ExecutionMode" json:"suite_execution_mode,omitempty"` // 集合执行方式
	CaseExecutionMode  pb.ExecutionMode        `protobuf:"varint,16,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"`    // 用例执行方式
	Debug              bool                    `protobuf:"varint,99,opt,name=debug,proto3" json:"debug,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TaskParams) Reset() {
	*x = TaskParams{}
	mi := &file_dispatcher_base_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskParams) ProtoMessage() {}

func (x *TaskParams) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskParams.ProtoReflect.Descriptor instead.
func (*TaskParams) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{2}
}

func (x *TaskParams) GetTriggerMode() pb1.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb1.TriggerMode(0)
}

func (x *TaskParams) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *TaskParams) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *TaskParams) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskParams) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *TaskParams) GetExecuteType() pb.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb.ApiExecutionDataType(0)
}

func (x *TaskParams) GetParentComponent() *ComponentExecuteKey {
	if x != nil {
		return x.ParentComponent
	}
	return nil
}

func (x *TaskParams) GetCurrentComponent() *ComponentExecuteKey {
	if x != nil {
		return x.CurrentComponent
	}
	return nil
}

func (x *TaskParams) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *TaskParams) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *TaskParams) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *TaskParams) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TaskParams) GetState() ComponentState {
	if x != nil {
		return x.State
	}
	return ComponentState_Pending
}

func (x *TaskParams) GetInterfaceCaseIds() []*ComponentExecuteKey {
	if x != nil {
		return x.InterfaceCaseIds
	}
	return nil
}

func (x *TaskParams) GetSuiteExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.SuiteExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *TaskParams) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *TaskParams) GetDebug() bool {
	if x != nil {
		return x.Debug
	}
	return false
}

type ApiPlanInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []string               `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"` // 服务列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiPlanInfo) Reset() {
	*x = ApiPlanInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiPlanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiPlanInfo) ProtoMessage() {}

func (x *ApiPlanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiPlanInfo.ProtoReflect.Descriptor instead.
func (*ApiPlanInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{3}
}

func (x *ApiPlanInfo) GetServices() []string {
	if x != nil {
		return x.Services
	}
	return nil
}

type UiPlanInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AppDownloadUrl string                 `protobuf:"bytes,1,opt,name=app_download_url,json=appDownloadUrl,proto3" json:"app_download_url,omitempty"` // App下载地址
	AppVersion     string                 `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`               // App版本信息
	Devices        []string               `protobuf:"bytes,3,rep,name=devices,proto3" json:"devices,omitempty"`                                       // 设备列表
	Together       bool                   `protobuf:"varint,4,opt,name=together,proto3" json:"together,omitempty"`                                    // 是否一起执行
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UiPlanInfo) Reset() {
	*x = UiPlanInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UiPlanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UiPlanInfo) ProtoMessage() {}

func (x *UiPlanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UiPlanInfo.ProtoReflect.Descriptor instead.
func (*UiPlanInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{4}
}

func (x *UiPlanInfo) GetAppDownloadUrl() string {
	if x != nil {
		return x.AppDownloadUrl
	}
	return ""
}

func (x *UiPlanInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UiPlanInfo) GetDevices() []string {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UiPlanInfo) GetTogether() bool {
	if x != nil {
		return x.Together
	}
	return false
}

type PerfPlanInfo struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	ExecuteType             pb1.PerfTaskType       `protobuf:"varint,1,opt,name=execute_type,json=executeType,proto3,enum=common.PerfTaskType" json:"execute_type,omitempty"`              // 执行类型，如：执行、调试
	EstimatedTime           int64                  `protobuf:"varint,2,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`                                 // 指定开始时间
	SendPreviewNotification bool                   `protobuf:"varint,3,opt,name=send_preview_notification,json=sendPreviewNotification,proto3" json:"send_preview_notification,omitempty"` // 是否发送预告通知
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *PerfPlanInfo) Reset() {
	*x = PerfPlanInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanInfo) ProtoMessage() {}

func (x *PerfPlanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanInfo.ProtoReflect.Descriptor instead.
func (*PerfPlanInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{5}
}

func (x *PerfPlanInfo) GetExecuteType() pb1.PerfTaskType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.PerfTaskType(0)
}

func (x *PerfPlanInfo) GetEstimatedTime() int64 {
	if x != nil {
		return x.EstimatedTime
	}
	return 0
}

func (x *PerfPlanInfo) GetSendPreviewNotification() bool {
	if x != nil {
		return x.SendPreviewNotification
	}
	return false
}

type StopDetailOfPerfStopRule struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	MetricType    string                                  `protobuf:"bytes,1,opt,name=metric_type,json=metricType,proto3" json:"metric_type,omitempty"` // 指标类型
	Service       string                                  `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`                         // 服务名称
	Namespace     string                                  `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`                     // 命名空间
	Method        string                                  `protobuf:"bytes,4,opt,name=method,proto3" json:"method,omitempty"`                           // 接口名称
	Threshold     float64                                 `protobuf:"fixed64,5,opt,name=threshold,proto3" json:"threshold,omitempty"`                   // 规则中的阀值
	ReachedAt     int64                                   `protobuf:"varint,6,opt,name=reached_at,json=reachedAt,proto3" json:"reached_at,omitempty"`   // 达到规则中的阀值的时间
	LatestAt      int64                                   `protobuf:"varint,7,opt,name=latest_at,json=latestAt,proto3" json:"latest_at,omitempty"`      // 达到规则中的持续时长的时间
	Points        []*StopDetailOfPerfStopRule_MetricPoint `protobuf:"bytes,8,rep,name=points,proto3" json:"points,omitempty"`                           // 满足规则后的指标点
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopDetailOfPerfStopRule) Reset() {
	*x = StopDetailOfPerfStopRule{}
	mi := &file_dispatcher_base_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopDetailOfPerfStopRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDetailOfPerfStopRule) ProtoMessage() {}

func (x *StopDetailOfPerfStopRule) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDetailOfPerfStopRule.ProtoReflect.Descriptor instead.
func (*StopDetailOfPerfStopRule) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{6}
}

func (x *StopDetailOfPerfStopRule) GetMetricType() string {
	if x != nil {
		return x.MetricType
	}
	return ""
}

func (x *StopDetailOfPerfStopRule) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *StopDetailOfPerfStopRule) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *StopDetailOfPerfStopRule) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *StopDetailOfPerfStopRule) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *StopDetailOfPerfStopRule) GetReachedAt() int64 {
	if x != nil {
		return x.ReachedAt
	}
	return 0
}

func (x *StopDetailOfPerfStopRule) GetLatestAt() int64 {
	if x != nil {
		return x.LatestAt
	}
	return 0
}

func (x *StopDetailOfPerfStopRule) GetPoints() []*StopDetailOfPerfStopRule_MetricPoint {
	if x != nil {
		return x.Points
	}
	return nil
}

// UIAgentComponentInfo `UI Agent`组件信息
type UIAgentComponentInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExecuteType   pb1.ExecuteType        `protobuf:"varint,1,opt,name=execute_type,json=executeType,proto3,enum=common.ExecuteType" json:"execute_type,omitempty"` // 执行类型
	Device        *pb1.UIAgentDevice     `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`                                                       // 设备信息
	Reinstall     bool                   `protobuf:"varint,3,opt,name=reinstall,proto3" json:"reinstall,omitempty"`                                                // 是否重新安装
	Restart       bool                   `protobuf:"varint,4,opt,name=restart,proto3" json:"restart,omitempty"`                                                    // 是否重启应用
	ReferenceId   string                 `protobuf:"bytes,5,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                          // 参考配置ID
	Times         int32                  `protobuf:"varint,6,opt,name=times,proto3" json:"times,omitempty"`                                                        // 执行次数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentComponentInfo) Reset() {
	*x = UIAgentComponentInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentInfo) ProtoMessage() {}

func (x *UIAgentComponentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentInfo.ProtoReflect.Descriptor instead.
func (*UIAgentComponentInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{7}
}

func (x *UIAgentComponentInfo) GetExecuteType() pb1.ExecuteType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ExecuteType(0)
}

func (x *UIAgentComponentInfo) GetDevice() *pb1.UIAgentDevice {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *UIAgentComponentInfo) GetReinstall() bool {
	if x != nil {
		return x.Reinstall
	}
	return false
}

func (x *UIAgentComponentInfo) GetRestart() bool {
	if x != nil {
		return x.Restart
	}
	return false
}

func (x *UIAgentComponentInfo) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *UIAgentComponentInfo) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

// UIAgentDeviceInfo `UI Agent`组件的设备信息
type UIAgentDeviceInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Device:
	//
	//	*UIAgentDeviceInfo_ProjectDevice
	//	*UIAgentDeviceInfo_UserDevice
	Device        isUIAgentDeviceInfo_Device `protobuf_oneof:"device"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentDeviceInfo) Reset() {
	*x = UIAgentDeviceInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentDeviceInfo) ProtoMessage() {}

func (x *UIAgentDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentDeviceInfo.ProtoReflect.Descriptor instead.
func (*UIAgentDeviceInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{8}
}

func (x *UIAgentDeviceInfo) GetDevice() isUIAgentDeviceInfo_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *UIAgentDeviceInfo) GetProjectDevice() *UIAgentDeviceInfo_ProjectDeviceInfo {
	if x != nil {
		if x, ok := x.Device.(*UIAgentDeviceInfo_ProjectDevice); ok {
			return x.ProjectDevice
		}
	}
	return nil
}

func (x *UIAgentDeviceInfo) GetUserDevice() *UIAgentDeviceInfo_UserDeviceInfo {
	if x != nil {
		if x, ok := x.Device.(*UIAgentDeviceInfo_UserDevice); ok {
			return x.UserDevice
		}
	}
	return nil
}

type isUIAgentDeviceInfo_Device interface {
	isUIAgentDeviceInfo_Device()
}

type UIAgentDeviceInfo_ProjectDevice struct {
	ProjectDevice *UIAgentDeviceInfo_ProjectDeviceInfo `protobuf:"bytes,1,opt,name=project_device,json=projectDevice,proto3,oneof"`
}

type UIAgentDeviceInfo_UserDevice struct {
	UserDevice *UIAgentDeviceInfo_UserDeviceInfo `protobuf:"bytes,2,opt,name=user_device,json=userDevice,proto3,oneof"`
}

func (*UIAgentDeviceInfo_ProjectDevice) isUIAgentDeviceInfo_Device() {}

func (*UIAgentDeviceInfo_UserDevice) isUIAgentDeviceInfo_Device() {}

type StopDetailOfPerfStopRule_MetricPoint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     int64                  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 时间戳
	Value         float64                `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`        // 值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopDetailOfPerfStopRule_MetricPoint) Reset() {
	*x = StopDetailOfPerfStopRule_MetricPoint{}
	mi := &file_dispatcher_base_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopDetailOfPerfStopRule_MetricPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDetailOfPerfStopRule_MetricPoint) ProtoMessage() {}

func (x *StopDetailOfPerfStopRule_MetricPoint) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDetailOfPerfStopRule_MetricPoint.ProtoReflect.Descriptor instead.
func (*StopDetailOfPerfStopRule_MetricPoint) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{6, 0}
}

func (x *StopDetailOfPerfStopRule_MetricPoint) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *StopDetailOfPerfStopRule_MetricPoint) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type UIAgentDeviceInfo_ProjectDeviceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`   // 设备ID
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"` // 占用设备的令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentDeviceInfo_ProjectDeviceInfo) Reset() {
	*x = UIAgentDeviceInfo_ProjectDeviceInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentDeviceInfo_ProjectDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentDeviceInfo_ProjectDeviceInfo) ProtoMessage() {}

func (x *UIAgentDeviceInfo_ProjectDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentDeviceInfo_ProjectDeviceInfo.ProtoReflect.Descriptor instead.
func (*UIAgentDeviceInfo_ProjectDeviceInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{8, 0}
}

func (x *UIAgentDeviceInfo_ProjectDeviceInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UIAgentDeviceInfo_ProjectDeviceInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type UIAgentDeviceInfo_UserDeviceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceType    pb1.DeviceType         `protobuf:"varint,1,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型
	PlatformType  pb1.PlatformType       `protobuf:"varint,2,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型
	Udid          string                 `protobuf:"bytes,3,opt,name=udid,proto3" json:"udid,omitempty"`                                                               // 设备ID
	RemoteAddress string                 `protobuf:"bytes,4,opt,name=remote_address,json=remoteAddress,proto3" json:"remote_address,omitempty"`                        // 设备远程连接地址
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentDeviceInfo_UserDeviceInfo) Reset() {
	*x = UIAgentDeviceInfo_UserDeviceInfo{}
	mi := &file_dispatcher_base_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentDeviceInfo_UserDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentDeviceInfo_UserDeviceInfo) ProtoMessage() {}

func (x *UIAgentDeviceInfo_UserDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_base_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentDeviceInfo_UserDeviceInfo.ProtoReflect.Descriptor instead.
func (*UIAgentDeviceInfo_UserDeviceInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_base_proto_rawDescGZIP(), []int{8, 1}
}

func (x *UIAgentDeviceInfo_UserDeviceInfo) GetDeviceType() pb1.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb1.DeviceType(0)
}

func (x *UIAgentDeviceInfo_UserDeviceInfo) GetPlatformType() pb1.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb1.PlatformType(0)
}

func (x *UIAgentDeviceInfo_UserDeviceInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UIAgentDeviceInfo_UserDeviceInfo) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

var File_dispatcher_base_proto protoreflect.FileDescriptor

var file_dispatcher_base_proto_rawDesc = []byte{
	0x0a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xa5, 0x01, 0x0a, 0x13,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x4b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x22, 0xec, 0x06, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x4b, 0x65,
	0x79, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x10,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4d, 0x0a, 0x12, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x48, 0x0a, 0x14, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x12, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x63, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x64, 0x65, 0x62,
	0x75, 0x67, 0x22, 0x29, 0x0a, 0x0b, 0x41, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x8d, 0x01,
	0x0a, 0x0a, 0x55, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10,
	0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x67, 0x65, 0x74, 0x68, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x6f, 0x67, 0x65, 0x74, 0x68, 0x65, 0x72, 0x22, 0xaa, 0x01,
	0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37,
	0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x19, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x17, 0x73, 0x65, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf2, 0x02, 0x0a, 0x18, 0x53,
	0x74, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x66, 0x50, 0x65, 0x72, 0x66, 0x53,
	0x74, 0x6f, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x41, 0x74, 0x12, 0x48, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x66, 0x50, 0x65, 0x72, 0x66,
	0x53, 0x74, 0x6f, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x1a, 0x41, 0x0a, 0x0b,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xee, 0x01, 0x0a, 0x14, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x22, 0x98, 0x04, 0x0a, 0x11, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x62, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x59, 0x0a, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x53, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0xe4, 0x01, 0x0a, 0x0e, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x0d,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x75, 0x64, 0x69,
	0x64, 0x12, 0x2e, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2a, 0x94, 0x01, 0x0a, 0x0e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x49,
	0x6e, 0x69, 0x74, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07,
	0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x0b, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x6b, 0x69,
	0x70, 0x10, 0x0c, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x10, 0x15,
	0x12, 0x09, 0x0a, 0x05, 0x50, 0x61, 0x6e, 0x69, 0x63, 0x10, 0x16, 0x12, 0x08, 0x0a, 0x04, 0x53,
	0x74, 0x6f, 0x70, 0x10, 0x17, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x10, 0x63, 0x2a, 0x36, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x50, 0x54, 0x5f, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x54,
	0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x10, 0x02, 0x2a, 0x90, 0x01, 0x0a, 0x09, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x5f, 0x4e,
	0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x0a, 0x53, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x10, 0x01, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x50, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x12, 0x1b, 0x0a, 0x0a, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44,
	0x10, 0x02, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x12,
	0x1d, 0x0a, 0x0b, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x03,
	0x1a, 0x0c, 0x82, 0xb5, 0x18, 0x08, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x12, 0x1d,
	0x0a, 0x0b, 0x53, 0x54, 0x5f, 0x41, 0x44, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x44, 0x10, 0x04, 0x1a,
	0x0c, 0x82, 0xb5, 0x18, 0x08, 0x41, 0x44, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x44, 0x2a, 0x48, 0x0a,
	0x08, 0x53, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x74, 0x6f,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x41, 0x75, 0x74, 0x6f, 0x10, 0x02, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_base_proto_rawDescOnce sync.Once
	file_dispatcher_base_proto_rawDescData = file_dispatcher_base_proto_rawDesc
)

func file_dispatcher_base_proto_rawDescGZIP() []byte {
	file_dispatcher_base_proto_rawDescOnce.Do(func() {
		file_dispatcher_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_base_proto_rawDescData)
	})
	return file_dispatcher_base_proto_rawDescData
}

var file_dispatcher_base_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_dispatcher_base_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_dispatcher_base_proto_goTypes = []any{
	(ComponentState)(0),                          // 0: dispatcher.ComponentState
	(TaskPageType)(0),                            // 1: dispatcher.TaskPageType
	(StageType)(0),                               // 2: dispatcher.StageType
	(StopType)(0),                                // 3: dispatcher.StopType
	(*ComponentKey)(nil),                         // 4: dispatcher.ComponentKey
	(*ComponentExecuteKey)(nil),                  // 5: dispatcher.ComponentExecuteKey
	(*TaskParams)(nil),                           // 6: dispatcher.TaskParams
	(*ApiPlanInfo)(nil),                          // 7: dispatcher.ApiPlanInfo
	(*UiPlanInfo)(nil),                           // 8: dispatcher.UiPlanInfo
	(*PerfPlanInfo)(nil),                         // 9: dispatcher.PerfPlanInfo
	(*StopDetailOfPerfStopRule)(nil),             // 10: dispatcher.StopDetailOfPerfStopRule
	(*UIAgentComponentInfo)(nil),                 // 11: dispatcher.UIAgentComponentInfo
	(*UIAgentDeviceInfo)(nil),                    // 12: dispatcher.UIAgentDeviceInfo
	(*StopDetailOfPerfStopRule_MetricPoint)(nil), // 13: dispatcher.StopDetailOfPerfStopRule.MetricPoint
	(*UIAgentDeviceInfo_ProjectDeviceInfo)(nil),  // 14: dispatcher.UIAgentDeviceInfo.ProjectDeviceInfo
	(*UIAgentDeviceInfo_UserDeviceInfo)(nil),     // 15: dispatcher.UIAgentDeviceInfo.UserDeviceInfo
	(pb.ApiExecutionDataType)(0),                 // 16: manager.ApiExecutionDataType
	(pb1.TriggerMode)(0),                         // 17: common.TriggerMode
	(*pb1.GeneralConfig)(nil),                    // 18: common.GeneralConfig
	(*pb1.AccountConfig)(nil),                    // 19: common.AccountConfig
	(pb.ExecutionMode)(0),                        // 20: manager.ExecutionMode
	(pb1.PerfTaskType)(0),                        // 21: common.PerfTaskType
	(pb1.ExecuteType)(0),                         // 22: common.ExecuteType
	(*pb1.UIAgentDevice)(nil),                    // 23: common.UIAgentDevice
	(pb1.DeviceType)(0),                          // 24: common.DeviceType
	(pb1.PlatformType)(0),                        // 25: common.PlatformType
}
var file_dispatcher_base_proto_depIdxs = []int32{
	16, // 0: dispatcher.ComponentKey.component_type:type_name -> manager.ApiExecutionDataType
	4,  // 1: dispatcher.ComponentExecuteKey.key:type_name -> dispatcher.ComponentKey
	0,  // 2: dispatcher.ComponentExecuteKey.state:type_name -> dispatcher.ComponentState
	17, // 3: dispatcher.TaskParams.trigger_mode:type_name -> common.TriggerMode
	16, // 4: dispatcher.TaskParams.execute_type:type_name -> manager.ApiExecutionDataType
	5,  // 5: dispatcher.TaskParams.parent_component:type_name -> dispatcher.ComponentExecuteKey
	5,  // 6: dispatcher.TaskParams.current_component:type_name -> dispatcher.ComponentExecuteKey
	18, // 7: dispatcher.TaskParams.general_config:type_name -> common.GeneralConfig
	19, // 8: dispatcher.TaskParams.account_config:type_name -> common.AccountConfig
	0,  // 9: dispatcher.TaskParams.state:type_name -> dispatcher.ComponentState
	5,  // 10: dispatcher.TaskParams.interface_case_ids:type_name -> dispatcher.ComponentExecuteKey
	20, // 11: dispatcher.TaskParams.suite_execution_mode:type_name -> manager.ExecutionMode
	20, // 12: dispatcher.TaskParams.case_execution_mode:type_name -> manager.ExecutionMode
	21, // 13: dispatcher.PerfPlanInfo.execute_type:type_name -> common.PerfTaskType
	13, // 14: dispatcher.StopDetailOfPerfStopRule.points:type_name -> dispatcher.StopDetailOfPerfStopRule.MetricPoint
	22, // 15: dispatcher.UIAgentComponentInfo.execute_type:type_name -> common.ExecuteType
	23, // 16: dispatcher.UIAgentComponentInfo.device:type_name -> common.UIAgentDevice
	14, // 17: dispatcher.UIAgentDeviceInfo.project_device:type_name -> dispatcher.UIAgentDeviceInfo.ProjectDeviceInfo
	15, // 18: dispatcher.UIAgentDeviceInfo.user_device:type_name -> dispatcher.UIAgentDeviceInfo.UserDeviceInfo
	24, // 19: dispatcher.UIAgentDeviceInfo.UserDeviceInfo.device_type:type_name -> common.DeviceType
	25, // 20: dispatcher.UIAgentDeviceInfo.UserDeviceInfo.platform_type:type_name -> common.PlatformType
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_dispatcher_base_proto_init() }
func file_dispatcher_base_proto_init() {
	if File_dispatcher_base_proto != nil {
		return
	}
	file_dispatcher_base_proto_msgTypes[8].OneofWrappers = []any{
		(*UIAgentDeviceInfo_ProjectDevice)(nil),
		(*UIAgentDeviceInfo_UserDevice)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_base_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_base_proto_goTypes,
		DependencyIndexes: file_dispatcher_base_proto_depIdxs,
		EnumInfos:         file_dispatcher_base_proto_enumTypes,
		MessageInfos:      file_dispatcher_base_proto_msgTypes,
	}.Build()
	File_dispatcher_base_proto = out.File
	file_dispatcher_base_proto_rawDesc = nil
	file_dispatcher_base_proto_goTypes = nil
	file_dispatcher_base_proto_depIdxs = nil
}
