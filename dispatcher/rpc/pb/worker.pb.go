// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/worker.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkerType int32

const (
	WorkerType_WorkerType_UNKNOWN             WorkerType = 0
	WorkerType_WorkerType_API_COMPONENT_GROUP WorkerType = 1  // API组件组
	WorkerType_WorkerType_API_CASE            WorkerType = 2  // API测试用例
	WorkerType_WorkerType_INTERFACE_CASE      WorkerType = 3  // 接口用例
	WorkerType_WorkerType_UI_CASE             WorkerType = 4  // UI测试用例
	WorkerType_WorkerType_PERF_CASE           WorkerType = 5  // 压力测试用例
	WorkerType_WorkerType_STABILITY_CASE      WorkerType = 6  // 稳定性测试用例
	WorkerType_WorkerType_STABILITY_PLAN      WorkerType = 7  // 稳定性测试计划
	WorkerType_WorkerType_UI_AGENT_COMPONENT  WorkerType = 8  // `UI Agent`组件
	WorkerType_WorkerType_UI_AGENT_CASE       WorkerType = 9  // `UI Agent`用例
	WorkerType_WorkerType_UI_AGENT_PLAN       WorkerType = 10 // `UI Agent`计划
)

// Enum value maps for WorkerType.
var (
	WorkerType_name = map[int32]string{
		0:  "WorkerType_UNKNOWN",
		1:  "WorkerType_API_COMPONENT_GROUP",
		2:  "WorkerType_API_CASE",
		3:  "WorkerType_INTERFACE_CASE",
		4:  "WorkerType_UI_CASE",
		5:  "WorkerType_PERF_CASE",
		6:  "WorkerType_STABILITY_CASE",
		7:  "WorkerType_STABILITY_PLAN",
		8:  "WorkerType_UI_AGENT_COMPONENT",
		9:  "WorkerType_UI_AGENT_CASE",
		10: "WorkerType_UI_AGENT_PLAN",
	}
	WorkerType_value = map[string]int32{
		"WorkerType_UNKNOWN":             0,
		"WorkerType_API_COMPONENT_GROUP": 1,
		"WorkerType_API_CASE":            2,
		"WorkerType_INTERFACE_CASE":      3,
		"WorkerType_UI_CASE":             4,
		"WorkerType_PERF_CASE":           5,
		"WorkerType_STABILITY_CASE":      6,
		"WorkerType_STABILITY_PLAN":      7,
		"WorkerType_UI_AGENT_COMPONENT":  8,
		"WorkerType_UI_AGENT_CASE":       9,
		"WorkerType_UI_AGENT_PLAN":       10,
	}
)

func (x WorkerType) Enum() *WorkerType {
	p := new(WorkerType)
	*p = x
	return p
}

func (x WorkerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkerType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_worker_proto_enumTypes[0].Descriptor()
}

func (WorkerType) Type() protoreflect.EnumType {
	return &file_dispatcher_worker_proto_enumTypes[0]
}

func (x WorkerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkerType.Descriptor instead.
func (WorkerType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{0}
}

type WorkerReq struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	TriggerMode   pb.TriggerMode           `protobuf:"varint,1,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`           // 触发模式
	TriggerRule   string                   `protobuf:"bytes,2,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`                                    // 触发规则
	ProjectId     string                   `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目ID
	TaskId        string                   `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                   // 任务ID
	ExecuteId     string                   `protobuf:"bytes,5,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                          // 执行ID
	ExecuteType   pb1.ApiExecutionDataType `protobuf:"varint,6,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"` // 执行类型
	WorkerType    WorkerType               `protobuf:"varint,7,opt,name=worker_type,json=workerType,proto3,enum=dispatcher.WorkerType" json:"worker_type,omitempty"`           // 工作类型
	GeneralConfig *pb.GeneralConfig        `protobuf:"bytes,8,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                              // 通用配置
	AccountConfig []*pb.AccountConfig      `protobuf:"bytes,9,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                              // 池账号配置
	User          string                   `protobuf:"bytes,10,opt,name=user,proto3" json:"user,omitempty"`
	UserId        string                   `protobuf:"bytes,11,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NodeData      *pb1.ApiExecutionData    `protobuf:"bytes,12,opt,name=node_data,json=nodeData,proto3" json:"node_data,omitempty"`
	PurposeType   pb.PurposeType           `protobuf:"varint,13,opt,name=purpose_type,json=purposeType,proto3,enum=common.PurposeType" json:"purpose_type,omitempty"`     // 计划用途
	PriorityType  pb.PriorityType          `protobuf:"varint,14,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"` // 优先级
	// Types that are valid to be assigned to Data:
	//
	//	*WorkerReq_ComponentGroup
	//	*WorkerReq_Case
	//	*WorkerReq_InterfaceCase
	//	*WorkerReq_UiCase
	//	*WorkerReq_PerfCase
	//	*WorkerReq_StabilityCase
	//	*WorkerReq_StabilityPlan
	//	*WorkerReq_UiAgentComponent
	Data          isWorkerReq_Data `protobuf_oneof:"data"`
	Debug         bool             `protobuf:"varint,99,opt,name=debug,proto3" json:"debug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerReq) Reset() {
	*x = WorkerReq{}
	mi := &file_dispatcher_worker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerReq) ProtoMessage() {}

func (x *WorkerReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerReq.ProtoReflect.Descriptor instead.
func (*WorkerReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{0}
}

func (x *WorkerReq) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *WorkerReq) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *WorkerReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *WorkerReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *WorkerReq) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *WorkerReq) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *WorkerReq) GetWorkerType() WorkerType {
	if x != nil {
		return x.WorkerType
	}
	return WorkerType_WorkerType_UNKNOWN
}

func (x *WorkerReq) GetGeneralConfig() *pb.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *WorkerReq) GetAccountConfig() []*pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *WorkerReq) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *WorkerReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *WorkerReq) GetNodeData() *pb1.ApiExecutionData {
	if x != nil {
		return x.NodeData
	}
	return nil
}

func (x *WorkerReq) GetPurposeType() pb.PurposeType {
	if x != nil {
		return x.PurposeType
	}
	return pb.PurposeType(0)
}

func (x *WorkerReq) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *WorkerReq) GetData() isWorkerReq_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *WorkerReq) GetComponentGroup() *ComponentGroupWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_ComponentGroup); ok {
			return x.ComponentGroup
		}
	}
	return nil
}

func (x *WorkerReq) GetCase() *CaseWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_Case); ok {
			return x.Case
		}
	}
	return nil
}

func (x *WorkerReq) GetInterfaceCase() *InterfaceCaseWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_InterfaceCase); ok {
			return x.InterfaceCase
		}
	}
	return nil
}

func (x *WorkerReq) GetUiCase() *UICaseWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_UiCase); ok {
			return x.UiCase
		}
	}
	return nil
}

func (x *WorkerReq) GetPerfCase() *PerfCaseWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_PerfCase); ok {
			return x.PerfCase
		}
	}
	return nil
}

func (x *WorkerReq) GetStabilityCase() *StabilityCaseWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_StabilityCase); ok {
			return x.StabilityCase
		}
	}
	return nil
}

func (x *WorkerReq) GetStabilityPlan() *StabilityPlanWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_StabilityPlan); ok {
			return x.StabilityPlan
		}
	}
	return nil
}

func (x *WorkerReq) GetUiAgentComponent() *UIAgentComponentWorkerInfo {
	if x != nil {
		if x, ok := x.Data.(*WorkerReq_UiAgentComponent); ok {
			return x.UiAgentComponent
		}
	}
	return nil
}

func (x *WorkerReq) GetDebug() bool {
	if x != nil {
		return x.Debug
	}
	return false
}

type isWorkerReq_Data interface {
	isWorkerReq_Data()
}

type WorkerReq_ComponentGroup struct {
	ComponentGroup *ComponentGroupWorkerInfo `protobuf:"bytes,31,opt,name=component_group,json=componentGroup,proto3,oneof"`
}

type WorkerReq_Case struct {
	Case *CaseWorkerInfo `protobuf:"bytes,32,opt,name=case,proto3,oneof"`
}

type WorkerReq_InterfaceCase struct {
	InterfaceCase *InterfaceCaseWorkerInfo `protobuf:"bytes,33,opt,name=interface_case,json=interfaceCase,proto3,oneof"`
}

type WorkerReq_UiCase struct {
	UiCase *UICaseWorkerInfo `protobuf:"bytes,34,opt,name=ui_case,json=uiCase,proto3,oneof"`
}

type WorkerReq_PerfCase struct {
	PerfCase *PerfCaseWorkerInfo `protobuf:"bytes,35,opt,name=perf_case,json=perfCase,proto3,oneof"`
}

type WorkerReq_StabilityCase struct {
	StabilityCase *StabilityCaseWorkerInfo `protobuf:"bytes,36,opt,name=stability_case,json=stabilityCase,proto3,oneof"`
}

type WorkerReq_StabilityPlan struct {
	StabilityPlan *StabilityPlanWorkerInfo `protobuf:"bytes,37,opt,name=stability_plan,json=stabilityPlan,proto3,oneof"`
}

type WorkerReq_UiAgentComponent struct {
	UiAgentComponent *UIAgentComponentWorkerInfo `protobuf:"bytes,38,opt,name=ui_agent_component,json=uiAgentComponent,proto3,oneof"`
}

func (*WorkerReq_ComponentGroup) isWorkerReq_Data() {}

func (*WorkerReq_Case) isWorkerReq_Data() {}

func (*WorkerReq_InterfaceCase) isWorkerReq_Data() {}

func (*WorkerReq_UiCase) isWorkerReq_Data() {}

func (*WorkerReq_PerfCase) isWorkerReq_Data() {}

func (*WorkerReq_StabilityCase) isWorkerReq_Data() {}

func (*WorkerReq_StabilityPlan) isWorkerReq_Data() {}

func (*WorkerReq_UiAgentComponent) isWorkerReq_Data() {}

type ComponentGroupWorkerInfo struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	ComponentGroupId        string                 `protobuf:"bytes,1,opt,name=component_group_id,json=componentGroupId,proto3" json:"component_group_id,omitempty"`
	ComponentGroupExecuteId string                 `protobuf:"bytes,2,opt,name=component_group_execute_id,json=componentGroupExecuteId,proto3" json:"component_group_execute_id,omitempty"`
	Version                 string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ComponentGroupWorkerInfo) Reset() {
	*x = ComponentGroupWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentGroupWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentGroupWorkerInfo) ProtoMessage() {}

func (x *ComponentGroupWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentGroupWorkerInfo.ProtoReflect.Descriptor instead.
func (*ComponentGroupWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{1}
}

func (x *ComponentGroupWorkerInfo) GetComponentGroupId() string {
	if x != nil {
		return x.ComponentGroupId
	}
	return ""
}

func (x *ComponentGroupWorkerInfo) GetComponentGroupExecuteId() string {
	if x != nil {
		return x.ComponentGroupExecuteId
	}
	return ""
}

func (x *ComponentGroupWorkerInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type CaseWorkerInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CaseId         string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	CaseExecuteId  string                 `protobuf:"bytes,2,opt,name=case_execute_id,json=caseExecuteId,proto3" json:"case_execute_id,omitempty"`
	SuiteId        string                 `protobuf:"bytes,3,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"` // 注：精准测试时，这里是服务ID，即虚拟的集合ID
	SuiteExecuteId string                 `protobuf:"bytes,4,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"`
	Version        string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	SuiteName      string                 `protobuf:"bytes,11,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`
	PlanId         string                 `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	PlanExecuteId  string                 `protobuf:"bytes,13,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`
	PlanName       string                 `protobuf:"bytes,14,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CaseWorkerInfo) Reset() {
	*x = CaseWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseWorkerInfo) ProtoMessage() {}

func (x *CaseWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseWorkerInfo.ProtoReflect.Descriptor instead.
func (*CaseWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{2}
}

func (x *CaseWorkerInfo) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseWorkerInfo) GetCaseExecuteId() string {
	if x != nil {
		return x.CaseExecuteId
	}
	return ""
}

func (x *CaseWorkerInfo) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *CaseWorkerInfo) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *CaseWorkerInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CaseWorkerInfo) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *CaseWorkerInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *CaseWorkerInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *CaseWorkerInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type InterfaceCaseWorkerInfo struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	InterfaceCaseId        string                 `protobuf:"bytes,1,opt,name=interface_case_id,json=interfaceCaseId,proto3" json:"interface_case_id,omitempty"`
	InterfaceCaseExecuteId string                 `protobuf:"bytes,2,opt,name=interface_case_execute_id,json=interfaceCaseExecuteId,proto3" json:"interface_case_execute_id,omitempty"`
	InterfaceId            string                 `protobuf:"bytes,3,opt,name=interface_id,json=interfaceId,proto3" json:"interface_id,omitempty"` // 注：精准测试时，这里是服务ID，即虚拟的集合ID
	InterfaceExecuteId     string                 `protobuf:"bytes,4,opt,name=interface_execute_id,json=interfaceExecuteId,proto3" json:"interface_execute_id,omitempty"`
	Version                string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	DocumentId             string                 `protobuf:"bytes,6,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`        // 接口文档ID
	DocumentName           string                 `protobuf:"bytes,11,opt,name=document_name,json=documentName,proto3" json:"document_name,omitempty"` // 接口文档名称
	PlanId                 string                 `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	PlanExecuteId          string                 `protobuf:"bytes,13,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`
	PlanName               string                 `protobuf:"bytes,14,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *InterfaceCaseWorkerInfo) Reset() {
	*x = InterfaceCaseWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceCaseWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceCaseWorkerInfo) ProtoMessage() {}

func (x *InterfaceCaseWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceCaseWorkerInfo.ProtoReflect.Descriptor instead.
func (*InterfaceCaseWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{3}
}

func (x *InterfaceCaseWorkerInfo) GetInterfaceCaseId() string {
	if x != nil {
		return x.InterfaceCaseId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetInterfaceCaseExecuteId() string {
	if x != nil {
		return x.InterfaceCaseExecuteId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetInterfaceId() string {
	if x != nil {
		return x.InterfaceId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetInterfaceExecuteId() string {
	if x != nil {
		return x.InterfaceExecuteId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetDocumentName() string {
	if x != nil {
		return x.DocumentName
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *InterfaceCaseWorkerInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type PlanMonitorReq struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	CallbackUrl   string                   `protobuf:"bytes,1,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	Timeout       int64                    `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	ProjectId     string                   `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TaskId        string                   `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	PlanExecuteId string                   `protobuf:"bytes,5,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`
	TestInfo      *PlanMonitorReq_TestInfo `protobuf:"bytes,6,opt,name=test_info,json=testInfo,proto3" json:"test_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlanMonitorReq) Reset() {
	*x = PlanMonitorReq{}
	mi := &file_dispatcher_worker_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanMonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanMonitorReq) ProtoMessage() {}

func (x *PlanMonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanMonitorReq.ProtoReflect.Descriptor instead.
func (*PlanMonitorReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{4}
}

func (x *PlanMonitorReq) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *PlanMonitorReq) GetTimeout() int64 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *PlanMonitorReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PlanMonitorReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PlanMonitorReq) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PlanMonitorReq) GetTestInfo() *PlanMonitorReq_TestInfo {
	if x != nil {
		return x.TestInfo
	}
	return nil
}

type UICaseWorkerInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	UiCaseId         string                 `protobuf:"bytes,1,opt,name=ui_case_id,json=uiCaseId,proto3" json:"ui_case_id,omitempty"`
	UiCaseExecuteId  string                 `protobuf:"bytes,2,opt,name=ui_case_execute_id,json=uiCaseExecuteId,proto3" json:"ui_case_execute_id,omitempty"`
	UiSuiteId        string                 `protobuf:"bytes,3,opt,name=ui_suite_id,json=uiSuiteId,proto3" json:"ui_suite_id,omitempty"`
	UiSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=ui_suite_execute_id,json=uiSuiteExecuteId,proto3" json:"ui_suite_execute_id,omitempty"`
	UiPlanId         string                 `protobuf:"bytes,5,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`
	UiPlanExecuteId  string                 `protobuf:"bytes,6,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"`
	MetaData         *pb1.UIPlanMetaData    `protobuf:"bytes,7,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UICaseWorkerInfo) Reset() {
	*x = UICaseWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICaseWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICaseWorkerInfo) ProtoMessage() {}

func (x *UICaseWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICaseWorkerInfo.ProtoReflect.Descriptor instead.
func (*UICaseWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{5}
}

func (x *UICaseWorkerInfo) GetUiCaseId() string {
	if x != nil {
		return x.UiCaseId
	}
	return ""
}

func (x *UICaseWorkerInfo) GetUiCaseExecuteId() string {
	if x != nil {
		return x.UiCaseExecuteId
	}
	return ""
}

func (x *UICaseWorkerInfo) GetUiSuiteId() string {
	if x != nil {
		return x.UiSuiteId
	}
	return ""
}

func (x *UICaseWorkerInfo) GetUiSuiteExecuteId() string {
	if x != nil {
		return x.UiSuiteExecuteId
	}
	return ""
}

func (x *UICaseWorkerInfo) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UICaseWorkerInfo) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UICaseWorkerInfo) GetMetaData() *pb1.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

type PerfCaseWorkerInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PerfCaseId         string                 `protobuf:"bytes,1,opt,name=perf_case_id,json=perfCaseId,proto3" json:"perf_case_id,omitempty"`
	PerfCaseExecuteId  string                 `protobuf:"bytes,2,opt,name=perf_case_execute_id,json=perfCaseExecuteId,proto3" json:"perf_case_execute_id,omitempty"`
	PerfSuiteId        string                 `protobuf:"bytes,3,opt,name=perf_suite_id,json=perfSuiteId,proto3" json:"perf_suite_id,omitempty"`
	PerfSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=perf_suite_execute_id,json=perfSuiteExecuteId,proto3" json:"perf_suite_execute_id,omitempty"`
	PerfPlanId         string                 `protobuf:"bytes,5,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`
	PerfPlanExecuteId  string                 `protobuf:"bytes,6,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"`
	MetaData           *pb1.PerfPlanMetaData  `protobuf:"bytes,11,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`               // 计划元数据
	PerfPlanInfo       *PerfPlanInfo          `protobuf:"bytes,12,opt,name=perf_plan_info,json=perfPlanInfo,proto3" json:"perf_plan_info,omitempty"` // 计划执行信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PerfCaseWorkerInfo) Reset() {
	*x = PerfCaseWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseWorkerInfo) ProtoMessage() {}

func (x *PerfCaseWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseWorkerInfo.ProtoReflect.Descriptor instead.
func (*PerfCaseWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{6}
}

func (x *PerfCaseWorkerInfo) GetPerfCaseId() string {
	if x != nil {
		return x.PerfCaseId
	}
	return ""
}

func (x *PerfCaseWorkerInfo) GetPerfCaseExecuteId() string {
	if x != nil {
		return x.PerfCaseExecuteId
	}
	return ""
}

func (x *PerfCaseWorkerInfo) GetPerfSuiteId() string {
	if x != nil {
		return x.PerfSuiteId
	}
	return ""
}

func (x *PerfCaseWorkerInfo) GetPerfSuiteExecuteId() string {
	if x != nil {
		return x.PerfSuiteExecuteId
	}
	return ""
}

func (x *PerfCaseWorkerInfo) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfCaseWorkerInfo) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfCaseWorkerInfo) GetMetaData() *pb1.PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *PerfCaseWorkerInfo) GetPerfPlanInfo() *PerfPlanInfo {
	if x != nil {
		return x.PerfPlanInfo
	}
	return nil
}

type StabilityCaseWorkerInfo struct {
	state                  protoimpl.MessageState    `protogen:"open.v1"`
	StabilityCaseId        string                    `protobuf:"bytes,1,opt,name=stability_case_id,json=stabilityCaseId,proto3" json:"stability_case_id,omitempty"`
	StabilityCaseExecuteId string                    `protobuf:"bytes,2,opt,name=stability_case_execute_id,json=stabilityCaseExecuteId,proto3" json:"stability_case_execute_id,omitempty"`
	StabilityPlanId        string                    `protobuf:"bytes,5,opt,name=stability_plan_id,json=stabilityPlanId,proto3" json:"stability_plan_id,omitempty"`
	StabilityPlanExecuteId string                    `protobuf:"bytes,6,opt,name=stability_plan_execute_id,json=stabilityPlanExecuteId,proto3" json:"stability_plan_execute_id,omitempty"`
	AccountConfig          *pb.AccountConfig         `protobuf:"bytes,11,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                        // 池账号配置
	DeviceType             pb.DeviceType             `protobuf:"varint,21,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型（真机、云手机）
	PlatformType           pb.PlatformType           `protobuf:"varint,22,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Udid                   string                    `protobuf:"bytes,23,opt,name=udid,proto3" json:"udid,omitempty"`                                                               // 设备编号（非空：表示指定设备，空：表示随机选择设备）
	PackageName            string                    `protobuf:"bytes,31,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                              // 包名
	AppDownloadLink        string                    `protobuf:"bytes,32,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // App下载地址
	Activities             []string                  `protobuf:"bytes,33,rep,name=activities,proto3" json:"activities,omitempty"`                                                   // 指定的Activity列表
	CustomScript           *pb.StabilityCustomScript `protobuf:"bytes,34,opt,name=custom_script,json=customScript,proto3" json:"custom_script,omitempty"`                           // 自定义脚本
	Duration               uint32                    `protobuf:"varint,41,opt,name=duration,proto3" json:"duration,omitempty"`                                                      // 执行时长
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *StabilityCaseWorkerInfo) Reset() {
	*x = StabilityCaseWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCaseWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCaseWorkerInfo) ProtoMessage() {}

func (x *StabilityCaseWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCaseWorkerInfo.ProtoReflect.Descriptor instead.
func (*StabilityCaseWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{7}
}

func (x *StabilityCaseWorkerInfo) GetStabilityCaseId() string {
	if x != nil {
		return x.StabilityCaseId
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetStabilityCaseExecuteId() string {
	if x != nil {
		return x.StabilityCaseExecuteId
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetStabilityPlanId() string {
	if x != nil {
		return x.StabilityPlanId
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetStabilityPlanExecuteId() string {
	if x != nil {
		return x.StabilityPlanExecuteId
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetAccountConfig() *pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *StabilityCaseWorkerInfo) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *StabilityCaseWorkerInfo) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *StabilityCaseWorkerInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *StabilityCaseWorkerInfo) GetActivities() []string {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *StabilityCaseWorkerInfo) GetCustomScript() *pb.StabilityCustomScript {
	if x != nil {
		return x.CustomScript
	}
	return nil
}

func (x *StabilityCaseWorkerInfo) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type StabilityPlanWorkerInfo struct {
	state                  protoimpl.MessageState     `protogen:"open.v1"`
	StabilityPlanId        string                     `protobuf:"bytes,1,opt,name=stability_plan_id,json=stabilityPlanId,proto3" json:"stability_plan_id,omitempty"`
	StabilityPlanExecuteId string                     `protobuf:"bytes,2,opt,name=stability_plan_execute_id,json=stabilityPlanExecuteId,proto3" json:"stability_plan_execute_id,omitempty"`
	MetaData               *pb1.StabilityPlanMetaData `protobuf:"bytes,11,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *StabilityPlanWorkerInfo) Reset() {
	*x = StabilityPlanWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanWorkerInfo) ProtoMessage() {}

func (x *StabilityPlanWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanWorkerInfo.ProtoReflect.Descriptor instead.
func (*StabilityPlanWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{8}
}

func (x *StabilityPlanWorkerInfo) GetStabilityPlanId() string {
	if x != nil {
		return x.StabilityPlanId
	}
	return ""
}

func (x *StabilityPlanWorkerInfo) GetStabilityPlanExecuteId() string {
	if x != nil {
		return x.StabilityPlanExecuteId
	}
	return ""
}

func (x *StabilityPlanWorkerInfo) GetMetaData() *pb1.StabilityPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

type UIAgentComponentWorkerInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ComponentId        string                 `protobuf:"bytes,1,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"`
	ComponentExecuteId string                 `protobuf:"bytes,2,opt,name=component_execute_id,json=componentExecuteId,proto3" json:"component_execute_id,omitempty"`
	ParentExecuteId    string                 `protobuf:"bytes,3,opt,name=parent_execute_id,json=parentExecuteId,proto3" json:"parent_execute_id,omitempty"`
	ExecuteType        pb.ExecuteType         `protobuf:"varint,11,opt,name=execute_type,json=executeType,proto3,enum=common.ExecuteType" json:"execute_type,omitempty"` // 执行类型
	Device             *pb.UIAgentDevice      `protobuf:"bytes,12,opt,name=device,proto3" json:"device,omitempty"`                                                       // 设备信息
	Reinstall          bool                   `protobuf:"varint,13,opt,name=reinstall,proto3" json:"reinstall,omitempty"`                                                // 是否重新安装
	Restart            bool                   `protobuf:"varint,14,opt,name=restart,proto3" json:"restart,omitempty"`                                                    // 是否重启应用
	ReferenceId        string                 `protobuf:"bytes,15,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                          // 参考配置ID
	Times              int32                  `protobuf:"varint,16,opt,name=times,proto3" json:"times,omitempty"`                                                        // 执行次数
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UIAgentComponentWorkerInfo) Reset() {
	*x = UIAgentComponentWorkerInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentWorkerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentWorkerInfo) ProtoMessage() {}

func (x *UIAgentComponentWorkerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentWorkerInfo.ProtoReflect.Descriptor instead.
func (*UIAgentComponentWorkerInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{9}
}

func (x *UIAgentComponentWorkerInfo) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *UIAgentComponentWorkerInfo) GetComponentExecuteId() string {
	if x != nil {
		return x.ComponentExecuteId
	}
	return ""
}

func (x *UIAgentComponentWorkerInfo) GetParentExecuteId() string {
	if x != nil {
		return x.ParentExecuteId
	}
	return ""
}

func (x *UIAgentComponentWorkerInfo) GetExecuteType() pb.ExecuteType {
	if x != nil {
		return x.ExecuteType
	}
	return pb.ExecuteType(0)
}

func (x *UIAgentComponentWorkerInfo) GetDevice() *pb.UIAgentDevice {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *UIAgentComponentWorkerInfo) GetReinstall() bool {
	if x != nil {
		return x.Reinstall
	}
	return false
}

func (x *UIAgentComponentWorkerInfo) GetRestart() bool {
	if x != nil {
		return x.Restart
	}
	return false
}

func (x *UIAgentComponentWorkerInfo) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *UIAgentComponentWorkerInfo) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

type UIAgentComponentTimesInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Req           *WorkerReq             `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`          // 任务参数
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`     // 执行次数
	Sent          int32                  `protobuf:"varint,3,opt,name=sent,proto3" json:"sent,omitempty"`       // 已发送次数
	Success       int32                  `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"` // 执行成功次数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentComponentTimesInfo) Reset() {
	*x = UIAgentComponentTimesInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentTimesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentTimesInfo) ProtoMessage() {}

func (x *UIAgentComponentTimesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentTimesInfo.ProtoReflect.Descriptor instead.
func (*UIAgentComponentTimesInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{10}
}

func (x *UIAgentComponentTimesInfo) GetReq() *WorkerReq {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UIAgentComponentTimesInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *UIAgentComponentTimesInfo) GetSent() int32 {
	if x != nil {
		return x.Sent
	}
	return 0
}

func (x *UIAgentComponentTimesInfo) GetSuccess() int32 {
	if x != nil {
		return x.Success
	}
	return 0
}

type PlanMonitorReq_TestInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	NoCaseServices []string               `protobuf:"bytes,1,rep,name=no_case_services,json=noCaseServices,proto3" json:"no_case_services,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PlanMonitorReq_TestInfo) Reset() {
	*x = PlanMonitorReq_TestInfo{}
	mi := &file_dispatcher_worker_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanMonitorReq_TestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanMonitorReq_TestInfo) ProtoMessage() {}

func (x *PlanMonitorReq_TestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_worker_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanMonitorReq_TestInfo.ProtoReflect.Descriptor instead.
func (*PlanMonitorReq_TestInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_worker_proto_rawDescGZIP(), []int{4, 0}
}

func (x *PlanMonitorReq_TestInfo) GetNoCaseServices() []string {
	if x != nil {
		return x.NoCaseServices
	}
	return nil
}

var File_dispatcher_worker_proto protoreflect.FileDescriptor

var file_dispatcher_worker_proto_rawDesc = []byte{
	0x0a, 0x17, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69,
	0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x09, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x48, 0x00, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x3d, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x12,
	0x4c, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x61, 0x73,
	0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x61,
	0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d,
	0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x61, 0x73, 0x65, 0x12, 0x4c, 0x0a,
	0x0e, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18,
	0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e,
	0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x74,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x56, 0x0a, 0x12, 0x75,
	0x69, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x10, 0x75, 0x69, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x9f, 0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c,
	0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x1a,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0xad, 0x02, 0x0a, 0x0e, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x93, 0x03, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c,
	0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70,
	0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa5, 0x02, 0x0a, 0x0e, 0x50, 0x6c,
	0x61, 0x6e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x34, 0x0a, 0x08, 0x54,
	0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x6f, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0e, 0x6e, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x22, 0xad, 0x02, 0x0a, 0x10, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x43, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2b,
	0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x69, 0x50, 0x6c,
	0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x09, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x89, 0x03, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65,
	0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61,
	0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x31, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c,
	0x61, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a,
	0x0e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0c, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb4, 0x05,
	0x0a, 0x17, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x61, 0x73, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19,
	0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x0c,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04,
	0x08, 0x04, 0x10, 0x05, 0x52, 0x12, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x1a, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x22, 0xbd, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19,
	0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c,
	0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x44, 0x61, 0x74, 0x61, 0x22, 0xf5, 0x02, 0x0a, 0x1a, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72,
	0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x72, 0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x88, 0x01, 0x0a,
	0x19, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x71, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2a, 0xcf, 0x02, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x22,
	0x0a, 0x1e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45,
	0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19,
	0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x54, 0x41, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x54, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x41, 0x47, 0x45, 0x4e,
	0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x1c, 0x0a,
	0x18, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x0a, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_worker_proto_rawDescOnce sync.Once
	file_dispatcher_worker_proto_rawDescData = file_dispatcher_worker_proto_rawDesc
)

func file_dispatcher_worker_proto_rawDescGZIP() []byte {
	file_dispatcher_worker_proto_rawDescOnce.Do(func() {
		file_dispatcher_worker_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_worker_proto_rawDescData)
	})
	return file_dispatcher_worker_proto_rawDescData
}

var file_dispatcher_worker_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dispatcher_worker_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_dispatcher_worker_proto_goTypes = []any{
	(WorkerType)(0),                    // 0: dispatcher.WorkerType
	(*WorkerReq)(nil),                  // 1: dispatcher.WorkerReq
	(*ComponentGroupWorkerInfo)(nil),   // 2: dispatcher.ComponentGroupWorkerInfo
	(*CaseWorkerInfo)(nil),             // 3: dispatcher.CaseWorkerInfo
	(*InterfaceCaseWorkerInfo)(nil),    // 4: dispatcher.InterfaceCaseWorkerInfo
	(*PlanMonitorReq)(nil),             // 5: dispatcher.PlanMonitorReq
	(*UICaseWorkerInfo)(nil),           // 6: dispatcher.UICaseWorkerInfo
	(*PerfCaseWorkerInfo)(nil),         // 7: dispatcher.PerfCaseWorkerInfo
	(*StabilityCaseWorkerInfo)(nil),    // 8: dispatcher.StabilityCaseWorkerInfo
	(*StabilityPlanWorkerInfo)(nil),    // 9: dispatcher.StabilityPlanWorkerInfo
	(*UIAgentComponentWorkerInfo)(nil), // 10: dispatcher.UIAgentComponentWorkerInfo
	(*UIAgentComponentTimesInfo)(nil),  // 11: dispatcher.UIAgentComponentTimesInfo
	(*PlanMonitorReq_TestInfo)(nil),    // 12: dispatcher.PlanMonitorReq.TestInfo
	(pb.TriggerMode)(0),                // 13: common.TriggerMode
	(pb1.ApiExecutionDataType)(0),      // 14: manager.ApiExecutionDataType
	(*pb.GeneralConfig)(nil),           // 15: common.GeneralConfig
	(*pb.AccountConfig)(nil),           // 16: common.AccountConfig
	(*pb1.ApiExecutionData)(nil),       // 17: manager.ApiExecutionData
	(pb.PurposeType)(0),                // 18: common.PurposeType
	(pb.PriorityType)(0),               // 19: common.PriorityType
	(*pb1.UIPlanMetaData)(nil),         // 20: manager.UIPlanMetaData
	(*pb1.PerfPlanMetaData)(nil),       // 21: manager.PerfPlanMetaData
	(*PerfPlanInfo)(nil),               // 22: dispatcher.PerfPlanInfo
	(pb.DeviceType)(0),                 // 23: common.DeviceType
	(pb.PlatformType)(0),               // 24: common.PlatformType
	(*pb.StabilityCustomScript)(nil),   // 25: common.StabilityCustomScript
	(*pb1.StabilityPlanMetaData)(nil),  // 26: manager.StabilityPlanMetaData
	(pb.ExecuteType)(0),                // 27: common.ExecuteType
	(*pb.UIAgentDevice)(nil),           // 28: common.UIAgentDevice
}
var file_dispatcher_worker_proto_depIdxs = []int32{
	13, // 0: dispatcher.WorkerReq.trigger_mode:type_name -> common.TriggerMode
	14, // 1: dispatcher.WorkerReq.execute_type:type_name -> manager.ApiExecutionDataType
	0,  // 2: dispatcher.WorkerReq.worker_type:type_name -> dispatcher.WorkerType
	15, // 3: dispatcher.WorkerReq.general_config:type_name -> common.GeneralConfig
	16, // 4: dispatcher.WorkerReq.account_config:type_name -> common.AccountConfig
	17, // 5: dispatcher.WorkerReq.node_data:type_name -> manager.ApiExecutionData
	18, // 6: dispatcher.WorkerReq.purpose_type:type_name -> common.PurposeType
	19, // 7: dispatcher.WorkerReq.priority_type:type_name -> common.PriorityType
	2,  // 8: dispatcher.WorkerReq.component_group:type_name -> dispatcher.ComponentGroupWorkerInfo
	3,  // 9: dispatcher.WorkerReq.case:type_name -> dispatcher.CaseWorkerInfo
	4,  // 10: dispatcher.WorkerReq.interface_case:type_name -> dispatcher.InterfaceCaseWorkerInfo
	6,  // 11: dispatcher.WorkerReq.ui_case:type_name -> dispatcher.UICaseWorkerInfo
	7,  // 12: dispatcher.WorkerReq.perf_case:type_name -> dispatcher.PerfCaseWorkerInfo
	8,  // 13: dispatcher.WorkerReq.stability_case:type_name -> dispatcher.StabilityCaseWorkerInfo
	9,  // 14: dispatcher.WorkerReq.stability_plan:type_name -> dispatcher.StabilityPlanWorkerInfo
	10, // 15: dispatcher.WorkerReq.ui_agent_component:type_name -> dispatcher.UIAgentComponentWorkerInfo
	12, // 16: dispatcher.PlanMonitorReq.test_info:type_name -> dispatcher.PlanMonitorReq.TestInfo
	20, // 17: dispatcher.UICaseWorkerInfo.meta_data:type_name -> manager.UIPlanMetaData
	21, // 18: dispatcher.PerfCaseWorkerInfo.meta_data:type_name -> manager.PerfPlanMetaData
	22, // 19: dispatcher.PerfCaseWorkerInfo.perf_plan_info:type_name -> dispatcher.PerfPlanInfo
	16, // 20: dispatcher.StabilityCaseWorkerInfo.account_config:type_name -> common.AccountConfig
	23, // 21: dispatcher.StabilityCaseWorkerInfo.device_type:type_name -> common.DeviceType
	24, // 22: dispatcher.StabilityCaseWorkerInfo.platform_type:type_name -> common.PlatformType
	25, // 23: dispatcher.StabilityCaseWorkerInfo.custom_script:type_name -> common.StabilityCustomScript
	26, // 24: dispatcher.StabilityPlanWorkerInfo.meta_data:type_name -> manager.StabilityPlanMetaData
	27, // 25: dispatcher.UIAgentComponentWorkerInfo.execute_type:type_name -> common.ExecuteType
	28, // 26: dispatcher.UIAgentComponentWorkerInfo.device:type_name -> common.UIAgentDevice
	1,  // 27: dispatcher.UIAgentComponentTimesInfo.req:type_name -> dispatcher.WorkerReq
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_dispatcher_worker_proto_init() }
func file_dispatcher_worker_proto_init() {
	if File_dispatcher_worker_proto != nil {
		return
	}
	file_dispatcher_base_proto_init()
	file_dispatcher_worker_proto_msgTypes[0].OneofWrappers = []any{
		(*WorkerReq_ComponentGroup)(nil),
		(*WorkerReq_Case)(nil),
		(*WorkerReq_InterfaceCase)(nil),
		(*WorkerReq_UiCase)(nil),
		(*WorkerReq_PerfCase)(nil),
		(*WorkerReq_StabilityCase)(nil),
		(*WorkerReq_StabilityPlan)(nil),
		(*WorkerReq_UiAgentComponent)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_worker_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_worker_proto_goTypes,
		DependencyIndexes: file_dispatcher_worker_proto_depIdxs,
		EnumInfos:         file_dispatcher_worker_proto_enumTypes,
		MessageInfos:      file_dispatcher_worker_proto_msgTypes,
	}.Build()
	File_dispatcher_worker_proto = out.File
	file_dispatcher_worker_proto_rawDesc = nil
	file_dispatcher_worker_proto_goTypes = nil
	file_dispatcher_worker_proto_depIdxs = nil
}
