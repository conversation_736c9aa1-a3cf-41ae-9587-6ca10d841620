package dispatcherlogic

import (
	"context"
	"sync"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ Publisher = (*UIAgentComponentPublisher)(nil)

type UIAgentComponentPublisher struct {
	*BasePublisher

	handleOnce    sync.Once
	componentData *managerpb.UIAgentComponentComponent
	executionData *managerpb.ApiExecutionData
	executeType   commonpb.ExecuteType
	deviceData    *commonpb.UIAgentDevice
}

func NewUIAgentComponentPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *UIAgentComponentPublisher {
	return &UIAgentComponentPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (p *UIAgentComponentPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	if err = p.handleMgrData(in); err != nil {
		return err
	}

	component := in.GetUiAgentComponent()
	if component == nil {
		return errorx.Err(errorx.ValidateParamError, "`UI Agent`组件内容为空")
	}

	req := &reporterpb.CreateUIAgentComponentRecordReq{
		TaskId:            p.taskId,
		ExecuteId:         p.executeId,
		ParentExecuteId:   "", // 这里创建的执行记录，父执行ID都为空
		ProjectId:         in.GetProjectId(),
		ComponentId:       component.GetComponentId(),
		ComponentName:     p.componentData.GetName(),
		TriggerMode:       in.GetTriggerMode(),
		ExecuteType:       p.executeType,
		ApplicationConfig: p.componentData.GetApplicationConfig(),
		Mode:              p.componentData.GetMode(),
		Steps:             p.componentData.GetSteps(),
		Expectation:       p.componentData.GetExpectation(),
		Variables:         p.componentData.GetVariables(),
		ForegroundCheck:   p.componentData.GetForegroundCheck(),
		Device:            p.deviceData,
		Reinstall:         component.GetReinstall(),
		Restart:           component.GetRestart(),
		ReferenceId:       component.GetReferenceId(),
		Times:             component.GetTimes(),
		Status:            pb.ComponentState_Pending.String(),
		ExecutedBy:        in.GetUserId(),
		StartedAt:         timestamppb.New(time.Now()),
	}
	if _, err = p.svcCtx.UIAgentReporterRPC.CreateUIAgentComponentRecord(p.ctx, req); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (p *UIAgentComponentPublisher) Publish(in *pb.PublishReq) (out *pb.PublishResp, err error) {
	return p.publish(in)
}

func (p *UIAgentComponentPublisher) IsValid(_ *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

func (p *UIAgentComponentPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	_, _ = p.svcCtx.UIAgentReporterRPC.ModifyUIAgentComponentRecord(
		p.ctx, &reporterpb.ModifyUIAgentComponentRecordReq{
			TaskId:      p.taskId,
			ExecuteId:   p.executeId,
			ProjectId:   in.GetProjectId(),
			ComponentId: in.GetUiAgentComponent().GetComponentId(),
			Status:      pb.ComponentState_Panic.String(),
			EndedAt:     timestamppb.New(time.Now()),
		},
	)

	if in.GetExecuteType() == managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT {
		return
	}

	// TODO: 发起回调
}

func (p *UIAgentComponentPublisher) handleMgrData(in *pb.PublishReq) (err error) {
	p.handleOnce.Do(
		func() {
			// 接口传入的`UI Agent`组件数据
			dstComponent := in.GetUiAgentComponent()
			if dstComponent == nil {
				err = errorx.Err(errorx.ValidateParamError, "`UI Agent`组件内容为空")
				return
			} else if dstComponent.GetTimes() <= 0 {
				dstComponent.Times = 1 // 默认执行1次
			}

			p.executionData, err = p.getApiExecutionData(
				&managerpb.GetApiExecutionDataReq{
					ProjectId: in.GetProjectId(),
					Type:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
					Id:        dstComponent.GetComponentId(),
				},
			)
			if err != nil {
				return
			}

			// 从`manager`获取当前的`UI Agent`组件数据
			srcComponent := p.executionData.GetUiAgentComponent()
			if srcComponent == nil {
				err = errorx.Errorf(
					errorx.InternalError,
					"获取`UI Agent`组件执行数据失败, project_id: %s, component_id: %s",
					in.GetProjectId(), dstComponent.GetComponentId(),
				)
				return
			}

			if err = p.handleComponentData(in, srcComponent); err != nil {
				return
			}

			if err = p.setDeviceData(in); err != nil {
				return
			} else {
				p.Infof("deviceData: %s", protobuf.MarshalJSONIgnoreError(p.deviceData))
			}

			if in.GetTriggerMode() == commonpb.TriggerMode_SCHEDULE {
				if srcComponent.GetMaintainedBy() != "" {
					in.UserId = srcComponent.GetMaintainedBy()
				} else {
					in.UserId = srcComponent.GetCreatedBy()
				}
			}

			p.componentData = srcComponent
			p.Infof("mgrData: %s", protobuf.MarshalJSONIgnoreError(p.executionData))
		},
	)

	return err
}

func (p *UIAgentComponentPublisher) publish(in *pb.PublishReq) (out *pb.PublishResp, err error) {
	if err = p.handleMgrData(in); err != nil {
		return nil, err
	}

	if !p.IsValid(p.executionData) || p.isStop {
		return &pb.PublishResp{TaskId: p.taskId, ExecuteId: p.executeId}, nil
	}

	var (
		referenceID = in.GetUiAgentComponent().GetReferenceId()
		times       = in.GetUiAgentComponent().GetTimes()
		tp          = constants.MQTaskTypeUIAgentWorkerExecuteComponentTask

		sender  *producer.Producer
		payload []byte
	)
	if referenceID != "" && times > 1 {
		// `UI Agent`组件执行多次，任务发到`dispatcher.mqc`进行任务拆分
		sender = p.svcCtx.DispatcherProducer
		tp = constants.MQTaskTypeDispatcherUIAgentComponent
		payload, err = protobuf.MarshalJSON(p.getDistributeReq(in))
	} else {
		// `UI Agent`组件执行一次，任务发到`uaworker`进行任务执行
		sender = p.svcCtx.UIAgentWorkerProducer
		payload, err = protobuf.MarshalJSON(p.getWorkerReq(in))
	}
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "UI Agent任务参数序列化失败, error: %s", err,
		)
	}
	p.Infof("ui agent component task, times: %d, payload: %s", times, payload)

	if _, err = sender.Send(
		p.ctx, base.NewTask(
			tp,
			payload,
			base.WithMaxRetryOptions(0),
			base.WithTimeoutOptions(time.Hour),
			base.WithRetentionOptions(time.Hour),
		), mq.ConvertPbEnumerationToQueuePriority(p.priorityType),
	); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		ProjectId: in.GetProjectId(),
		TaskId:    p.taskId,
		ExecuteId: p.executeId,
	}, nil
}

func (p *UIAgentComponentPublisher) getWorkerReq(in *pb.PublishReq) *pb.WorkerReq {
	component := in.GetUiAgentComponent()

	return &pb.WorkerReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       p.taskId,
		ExecuteId:    p.executeId,
		ExecuteType:  in.GetExecuteType(),
		WorkerType:   pb.WorkerType_WorkerType_UI_AGENT_COMPONENT,
		UserId:       in.GetUserId(),
		NodeData:     p.executionData,
		PriorityType: p.priorityType,
		Data: &pb.WorkerReq_UiAgentComponent{
			UiAgentComponent: &pb.UIAgentComponentWorkerInfo{
				ComponentId:        component.GetComponentId(),
				ComponentExecuteId: p.executeId,
				ParentExecuteId:    "", // 这里发送给`uaworker`的都是执行一次的任务，因此父执行ID为空
				ExecuteType:        p.executeType,
				Device:             p.deviceData,
				Reinstall:          component.GetReinstall(),
				Restart:            component.GetRestart(),
				ReferenceId:        component.GetReferenceId(),
				Times:              component.GetTimes(),
			},
		},
		Debug: in.GetDebug(),
	}
}

func (p *UIAgentComponentPublisher) getDistributeReq(in *pb.PublishReq) *pb.DistributeReq {
	component := in.GetUiAgentComponent()

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         p.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_UI_AGENT_COMPONENT,
		UserId:         in.GetUserId(),
		PriorityType:   p.priorityType,
		Data: &pb.DistributeReq_UiAgentComponent{
			UiAgentComponent: &pb.UIAgentComponentDistributeData{
				ComponentId:        component.GetComponentId(),
				ComponentExecuteId: p.executeId, // 这里发送给`dispatcher.mqc`的都是执行多次的任务，因此`p.executeId`将作为后续任务的父执行ID
				State:              pb.ComponentState_Pending,
				Component:          p.componentData,
				ComponentInfo: &pb.UIAgentComponentInfo{
					ExecuteType: p.executeType,
					Device:      p.deviceData,
					Reinstall:   component.GetReinstall(),
					Restart:     true, // 执行多次时需要重启应用
					ReferenceId: component.GetReferenceId(),
					Times:       component.GetTimes(),
				},
			},
		},
		Debug: in.GetDebug(),
	}
}

func (p *UIAgentComponentPublisher) handleComponentData(
	in *pb.PublishReq, src *managerpb.UIAgentComponentComponent,
) error {
	dst := in.GetUiAgentComponent()
	if dst == nil {
		return errorx.Err(errorx.ValidateParamError, "`UI Agent`组件内容为空")
	}

	p.executeType = commonpb.ExecuteType_ET_EXECUTE
	if in.GetDebug() {
		// 调试时使用接口传入的数据
		src.Name = dst.GetComponentName()
		src.Mode = dst.GetMode()
		src.Steps = dst.GetSteps()
		src.Expectation = dst.GetExpectation()
		src.Variables = dst.GetVariables()
		src.ForegroundCheck = dst.GetForegroundCheck()

		// 调试时忽略参考配置
		dst.ReferenceId = ""

		// 调试
		p.executeType = commonpb.ExecuteType_ET_DEBUG
	} else if dst.GetMode() == commonpb.UIAgentMode_UIAgentMode_STEP {
		// Step模式时忽略参考配置
		dst.ReferenceId = ""
	} else if dst.GetMode() == commonpb.UIAgentMode_UIAgentMode_AGENT &&
		dst.GetReferenceId() != "" &&
		dst.GetReferenceId() != src.GetReferenceId() {
		// Agent模式 且 参考执行，需要校验传入的参考配置是否与绑定的一致
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"传入的参考配置跟`UI Agent`组件绑定的不一致, 期望的参考配置: %s, 但传入的参考配置: %s",
			src.GetReferenceId(), dst.GetReferenceId(),
		)
	} else if dst.GetReferenceId() != "" {
		// 参考执行
		p.executeType = commonpb.ExecuteType_ET_REFERENCE
	}

	return nil
}

func (p *UIAgentComponentPublisher) setDeviceData(in *pb.PublishReq) error {
	component := in.GetUiAgentComponent()
	if component == nil {
		return errorx.Err(errorx.ValidateParamError, "`UI Agent`组件内容为空")
	}

	switch v := component.GetDevice().GetDevice().(type) {
	case *pb.UIAgentDeviceInfo_ProjectDevice:
		d := v.ProjectDevice

		out, err := p.svcCtx.ManagerRPC.GetProjectDevice(
			p.ctx, &managerpb.GetProjectDeviceReq{
				ProjectId: in.GetProjectId(),
				Usage:     commonpb.DeviceUsage_UI_TESTING,
				Udid:      d.GetUdid(),
			},
		)
		if err != nil {
			return err
		}

		pd := out.GetDevice().GetDevice()
		if pd.GetToken() != d.GetToken() {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"传入的设备令牌跟实际的不一致, 无法使用该设备, udid: %s, token: %s",
				d.GetUdid(), d.GetToken(),
			)
		}

		p.deviceData = &commonpb.UIAgentDevice{
			Device: &commonpb.UIAgentDevice_ProjectDevice_{
				ProjectDevice: &commonpb.UIAgentDevice_ProjectDevice{
					DeviceType:    pd.GetType(),
					PlatformType:  pd.GetPlatform(),
					Udid:          d.GetUdid(),
					RemoteAddress: pd.GetRemoteAddress(),
					Token:         d.GetToken(),
				},
			},
		}
	case *pb.UIAgentDeviceInfo_UserDevice:
		d := v.UserDevice

		// 对于`Android`设备，获取其真正的设备编号
		serial, err := p.getRemoteAndroidSerial(d)
		if err != nil {
			p.Errorf(
				"failed to get the remote android serial, device_type: %s, platform_type: %s, remote_address: %s, udid: %s, error: %+v",
				d.GetDeviceType(), d.GetPlatformType(), d.GetRemoteAddress(), d.GetUdid(), err,
			)
		} else if serial != "" && serial != d.GetUdid() {
			p.Infof(
				"change the udid of android device, device_type: %s, platform_type: %s, remote_address: %s, udid: %s -> %s",
				d.GetDeviceType(), d.GetPlatformType(), d.GetRemoteAddress(), d.GetUdid(), serial,
			)
			d.Udid = serial
		}

		// 通过设备编号查询该设备是否存在未结束的`UI Agent`任务
		count, err := p.countUnfinishedRecordsByUDID(d.GetUdid())
		if err != nil {
			p.Errorf(
				"failed to count unfinished ui agent component records by udid, device_type: %s, platform_type: %s, remote_address: %s, udid: %s, error: %+v",
				d.GetDeviceType(), d.GetPlatformType(), d.GetRemoteAddress(), d.GetUdid(), err,
			)
		} else if count > 0 {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the device is being used by other tasks, so it cannot be used, device_type: %s, platform_type: %s, remote_address: %s, udid: %s",
				d.GetDeviceType(), d.GetPlatformType(), d.GetRemoteAddress(), d.GetUdid(),
			)
		}

		p.deviceData = &commonpb.UIAgentDevice{
			Device: &commonpb.UIAgentDevice_UserDevice_{
				UserDevice: &commonpb.UIAgentDevice_UserDevice{
					DeviceType:    d.GetDeviceType(),
					PlatformType:  d.GetPlatformType(),
					Udid:          d.GetUdid(),
					RemoteAddress: d.GetRemoteAddress(),
				},
			},
		}
	default:
		return errorx.Errorf(
			errorx.ValidateParamError,
			"无效的设备信息类型, 期望的类型: %T or %T, 但实际类型: %T",
			(*pb.UIAgentDeviceInfo_ProjectDevice)(nil), (*pb.UIAgentDeviceInfo_UserDevice)(nil), v,
		)
	}

	return nil
}

func (p *UIAgentComponentPublisher) getRemoteAndroidSerial(device *pb.UIAgentDeviceInfo_UserDeviceInfo) (
	string, error,
) {
	var (
		deviceType    = device.GetDeviceType()
		platformType  = device.GetPlatformType()
		udid          = device.GetUdid()
		remoteAddress = device.GetRemoteAddress()
	)

	if platformType == commonpb.PlatformType_ANDROID || platformType == commonpb.PlatformType_HarmonyOS {
		out, err := p.svcCtx.ManagerRPC.GetRemoteAndroidSerial(
			p.ctx, &managerpb.GetRemoteAndroidSerialReq{
				DeviceType:    deviceType,
				RemoteAddress: remoteAddress,
			},
		)
		if err != nil {
			return udid, err
		}

		if serial := out.GetSerial(); serial != udid {
			return serial, nil
		}
	}

	return udid, nil
}

func (p *UIAgentComponentPublisher) countUnfinishedRecordsByUDID(udid string) (uint64, error) {
	out, err := p.svcCtx.UIAgentReporterRPC.GetUIAgentComponentRecordsByUDID(
		p.ctx, &reporterpb.GetUIAgentComponentRecordsByUDIDReq{
			Udid: udid,
			Condition: &rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   "status",
					Compare: qetconstants.DBIn,
					In: []string{
						pb.ComponentState_Pending.String(),
						pb.ComponentState_Init.String(),
						pb.ComponentState_Started.String(),
						pb.ComponentState_Waiting.String(),
					},
				},
			},
			Pagination: &rpc.Pagination{
				CurrentPage: 1,
				PageSize:    1,
			},
		},
	)
	if err != nil {
		return 0, err
	}

	return out.GetTotalCount(), nil
}
