package baselogic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx           context.Context
	lock          sync.RWMutex // 任务颗粒度的读写锁
	tearDownFuncs []func()     // 注册释放函数
	stateM        *state.StateManager

	svcCtx *svc.ServiceContext
	logW   *BaseLogicLogWriter
}

func NewBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger:        logx.WithContext(ctx),
		ctx:           ctx,
		svcCtx:        svcCtx,
		tearDownFuncs: make([]func(), 0, 10),
	}
}

func (l *BaseLogic) Setup() {
	l.logW = NewBaseLogicLogWriter()
	l.stateM = state.NewStateManager(pb.ComponentState_Init, errcode.GetErrCode(errorx.OK))
}

func (l *BaseLogic) DoLock(f func()) {
	l.lock.Lock()
	defer l.lock.Unlock()
	f()
}

func (l *BaseLogic) DoRLock(f func()) {
	l.lock.TryLock()

	l.lock.RLock()
	defer l.lock.RUnlock()
	f()
}

func (l *BaseLogic) RegisterTeardownFunc(f func()) {
	l.tearDownFuncs = append(l.tearDownFuncs, f)
}

func (l *BaseLogic) Context() context.Context {
	return l.ctx
}

func (l *BaseLogic) ServiceContext() *svc.ServiceContext {
	return l.svcCtx
}

func (l *BaseLogic) Teardown() {
	for _, f := range l.tearDownFuncs {
		f()
	}
}

func (l *BaseLogic) LogWriter() *BaseLogicLogWriter {
	return l.logW
}

func (l *BaseLogic) StateM() *state.StateManager {
	return l.stateM
}

func (l *BaseLogic) UpdateState(state pb.ComponentState, code errorx.Code) bool {
	return l.StateM().UpdateStateManager(state, errcode.GetErrCode(code))
}

func (l *BaseLogic) UpdateStatef(state pb.ComponentState, code errorx.Code, msg string, args ...any) {
	ok := l.UpdateState(state, code)
	if ok {
		l.StateM().SetDebugf(msg, args...)
	}
}

func (l *BaseLogic) UpdateFailureStatef(code errorx.Code, msg string, args ...any) error {
	l.UpdateStatef(pb.ComponentState_Failure, code, msg, args...)
	l.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}

func (l *BaseLogic) UpdatePanicStatef(code errorx.Code, msg string, args ...any) error {
	l.UpdateStatef(pb.ComponentState_Panic, code, msg, args...)
	l.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}

func (l *BaseLogic) CreateUIAgentComponentRecord(req *reporterpb.CreateUIAgentComponentRecordReq) error {
	err := l.createUIAgentComponentRecordByRPC(req)
	if err != nil {
		l.Errorf(
			"failed to create ui agent component record by rpc, req: %s, error: %s",
			protobuf.MarshalJSONIgnoreError(req), err,
		)
	} else {
		return nil
	}

	if err = l.createUIAgentComponentRecordByMQ(req); err != nil {
		l.Errorf(
			"failed to create ui agent component record by mq, req: %s, error: %s",
			protobuf.MarshalJSONIgnoreError(req), err,
		)
		return err
	}

	return nil
}

func (l *BaseLogic) createUIAgentComponentRecordByRPC(req *reporterpb.CreateUIAgentComponentRecordReq) error {
	_, err := l.svcCtx.UIAgentReporterRPC.CreateUIAgentComponentRecord(l.ctx, req)
	return err
}

func (l *BaseLogic) createUIAgentComponentRecordByMQ(req *reporterpb.CreateUIAgentComponentRecordReq) error {
	_, err := l.svcCtx.ReporterProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeReporterHandleUIAgentRecordTask,
			protobuf.MarshalJSONIgnoreError(
				&reporterpb.HandleUIAgentRecordTaskInfo{
					Payload: &reporterpb.HandleUIAgentRecordTaskInfo_CreateComponent{
						CreateComponent: req,
					},
				},
			),
		), base.QueuePriorityDefault,
	)
	return err
}

func (l *BaseLogic) ModifyUIAgentComponentRecord(req *reporterpb.ModifyUIAgentComponentRecordReq) error {
	err := l.modifyUIAgentComponentRecordByRPC(req)
	if err != nil {
		l.Errorf(
			"failed to modify ui agent component record by rpc, req: %s, error: %s",
			protobuf.MarshalJSONIgnoreError(req), err,
		)
	} else {
		return nil
	}

	if err = l.modifyUIAgentComponentRecordByMQ(req); err != nil {
		l.Errorf(
			"failed to modify ui agent component record by mq, req: %s, error: %s",
			protobuf.MarshalJSONIgnoreError(req), err,
		)
		return err
	}

	return nil
}

func (l *BaseLogic) modifyUIAgentComponentRecordByRPC(req *reporterpb.ModifyUIAgentComponentRecordReq) error {
	_, err := l.svcCtx.UIAgentReporterRPC.ModifyUIAgentComponentRecord(l.ctx, req)
	return err
}

func (l *BaseLogic) modifyUIAgentComponentRecordByMQ(req *reporterpb.ModifyUIAgentComponentRecordReq) error {
	_, err := l.svcCtx.ReporterProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeReporterHandleUIAgentRecordTask,
			protobuf.MarshalJSONIgnoreError(
				&reporterpb.HandleUIAgentRecordTaskInfo{
					Payload: &reporterpb.HandleUIAgentRecordTaskInfo_ModifyComponent{
						ModifyComponent: req,
					},
				},
			),
		), base.QueuePriorityDefault,
	)
	return err
}

func (l *BaseLogic) GetUIAgentComponentTimesInfo(taskID string) (*pb.UIAgentComponentTimesInfo, error) {
	key := fmt.Sprintf("%s:%s", common.ConstCacheUIAgentComponentTimesCallbackDataTaskIDPrefix, taskID)
	result, err := l.svcCtx.Redis.Get(l.ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, errorx.Errorf(errorx.NotExists, "not found the ui agent component times info, key: %s", key)
		}

		return nil, errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to get the ui agent component times info, key: %s, error: %+v",
			key, err,
		)
	}

	var info pb.UIAgentComponentTimesInfo
	if err = protobuf.UnmarshalJSONFromString(result, &info); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the ui agent component times info, key: %s, value: %s, error: %+v",
			key, result, err,
		)
	}

	return &info, nil
}

func (l *BaseLogic) SetUIAgentComponentTimesInfo(taskID string, info *pb.UIAgentComponentTimesInfo) error {
	key := fmt.Sprintf("%s:%s", common.ConstCacheUIAgentComponentTimesCallbackDataTaskIDPrefix, taskID)
	value := protobuf.MarshalJSONIgnoreError(info)
	if _, err := l.svcCtx.Redis.Set(l.ctx, key, value, 24*time.Hour).Result(); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to create the ui agent component times info, key: %s, value: %s, error: %+v",
			key, value, err,
		)
	}

	return nil
}

func (l *BaseLogic) UpdateUIAgentComponentTimesInfo(taskID string, info *pb.UIAgentComponentTimesInfo) error {
	key := fmt.Sprintf("%s:%s", common.ConstCacheUIAgentComponentTimesCallbackDataTaskIDPrefix, taskID)
	value := protobuf.MarshalJSONIgnoreError(info)
	if result, err := l.svcCtx.Redis.SetXX(l.ctx, key, value, 0).Result(); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to update the ui agent component times info, key: %s, value: %s, error: %+v",
			key, value, err,
		)
	} else if !result {
		return errorx.Errorf(errorx.NotExists, "not found the ui agent component times info, key: %s", key)
	}

	return nil
}
