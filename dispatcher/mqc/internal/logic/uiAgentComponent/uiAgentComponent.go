package uiAgentComponent

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIAgentComponent struct {
	*baselogic.DispatchLogic

	ctl  *dtctl.DispatchTaskControl
	logW *LogWriter

	times        int32
	subExecuteID string
}

func NewUIAgentComponent(ctx context.Context, svcCtx *svc.ServiceContext) *UIAgentComponent {
	return &UIAgentComponent{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *UIAgentComponent) Run(req *pb.DistributeReq) error {
	x.Setup(req)

	err := x.run()

	x.Teardown()

	return err
}

func (x *UIAgentComponent) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)

	x.times = req.GetUiAgentComponent().GetComponentInfo().GetTimes()
	if x.times > 1 {
		// 执行多次，生成子执行ID
		x.subExecuteID = utils.GenExecuteId()
	}

	x.ctl = x.GetDtControl()
	x.SetDtCtl(x.ctl)
	x.logW = NewLogWriter(x.DispatchLogic.LogWriter())
	x.logW.setTimes(x.times)
}

func (x *UIAgentComponent) run() error {
	defer func() {
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != nil {
			_ = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		if err := x.modifyRecord(); err != nil {
			_ = x.UpdatePanicStatef(codes.TaskRecordFailure, "UIAgentComponent.modifyRecord: %s", err)
		}

		x.Infof("UIAgentComponent[%s] : %s", x.Source().GetUiAgentComponent().GetComponentId(), x.Content())
	}()

	return x.publish()
}

func (x *UIAgentComponent) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	component := source.GetUiAgentComponent()

	return dtctl.NewDispatchTaskControl(
		x.Context(), x.ServiceContext().Redis, &dtctl.DispatchTaskMember{
			ComponentId:        component.GetComponentId(),
			ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
			ComponentExecuteId: component.GetComponentExecuteId(), // 父执行ID
		},
	)
}

func (x *UIAgentComponent) publish() error {
	var (
		source    = x.Source()
		data      = source.GetUiAgentComponent()
		component = data.GetComponent()
		info      = data.GetComponentInfo()
	)

	x.StateM().UpdateStateManager(pb.ComponentState_Started, errcode.GetErrCode(errorx.OK))
	x.SetTotal(int64(x.times))
	if x.times <= 0 {
		return nil
	}

	if err := x.ctl.CreateInfo(
		source.GetTaskId(),
		source.GetProjectId(),
		source.GetExecuteType().String(),
		int64(x.times),
		source.GetPriorityType(),
	); err != nil {
		return x.UpdatePanicStatef(codes.TaskDBFailure, "CreateInfo: %s", err)
	}

	req := &pb.WorkerReq{
		TriggerMode: source.GetTriggerMode(),
		TriggerRule: source.GetTriggerRule(),
		ProjectId:   source.GetProjectId(),
		TaskId:      source.GetTaskId(),
		ExecuteId:   "", // 后续设置
		ExecuteType: source.GetExecuteType(),
		WorkerType:  pb.WorkerType_WorkerType_UI_AGENT_COMPONENT,
		User:        source.GetUser(),
		UserId:      source.GetUserId(),
		NodeData: &managerpb.ApiExecutionData{
			Id:   component.GetComponentId(),
			Type: managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
			Data: &managerpb.ApiExecutionData_UiAgentComponent{
				UiAgentComponent: component,
			},
		},
		PurposeType:  source.GetPurposeType(),
		PriorityType: source.GetPriorityType(),
		Data:         nil, // 后续设置
		Debug:        source.GetDebug(),
	}
	if x.times > 1 {
		// 执行多次
		if err := x.createSubRecord(); err != nil {
			return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "创建子执行记录失败, error: %s", err)
		}

		req.ExecuteId = x.subExecuteID
		req.Data = &pb.WorkerReq_UiAgentComponent{
			UiAgentComponent: &pb.UIAgentComponentWorkerInfo{
				ComponentId:        component.GetComponentId(),
				ComponentExecuteId: x.subExecuteID,
				ParentExecuteId:    data.GetComponentExecuteId(), // 执行多次，需要设置父执行ID
				ExecuteType:        info.GetExecuteType(),
				Device:             info.GetDevice(),
				Reinstall:          info.GetReinstall(),
				Restart:            info.GetRestart(),
				ReferenceId:        info.GetReferenceId(),
				Times:              x.times, // 注：正常情况下，`uaworker`不需要关心此字段
			},
		}
	} else {
		// 执行一次
		req.ExecuteId = data.GetComponentExecuteId()
		req.Data = &pb.WorkerReq_UiAgentComponent{
			UiAgentComponent: &pb.UIAgentComponentWorkerInfo{
				ComponentId:        component.GetComponentId(),
				ComponentExecuteId: data.GetComponentExecuteId(),
				ParentExecuteId:    "", // 执行一次，没有父执行ID
				ExecuteType:        info.GetExecuteType(),
				Device:             info.GetDevice(),
				Reinstall:          info.GetReinstall(),
				Restart:            info.GetRestart(),
				ReferenceId:        info.GetReferenceId(),
				Times:              x.times, // 注：正常情况下，`uaworker`不需要关心此字段
			},
		}
	}

	payload, err := protobuf.MarshalJSON(req)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "UI Agent任务参数序列化失败, error: %s", err,
		)
	}

	if _, err = x.ServiceContext().UIAgentWorkerProducer.Send(
		x.Context(), base.NewTask(
			constants.MQTaskTypeUIAgentWorkerExecuteComponentTask,
			payload,
			base.WithMaxRetryOptions(0),
			base.WithTimeoutOptions(time.Hour),
			base.WithRetentionOptions(time.Hour),
		), mq.ConvertPbEnumerationToQueuePriority(x.Source().GetPriorityType()),
	); err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	if err = x.SetUIAgentComponentTimesInfo(
		source.GetTaskId(), &pb.UIAgentComponentTimesInfo{
			Req:   req,
			Total: x.times,
			Sent:  1,
		},
	); err != nil {
		x.Error(err)
		return err
	}

	return nil
}

func (x *UIAgentComponent) createSubRecord() error {
	var (
		source    = x.Source()
		data      = source.GetUiAgentComponent()
		component = data.GetComponent()
		info      = data.GetComponentInfo()
		times     = info.GetTimes()
	)

	if times <= 1 {
		return nil
	}

	req := &reporterpb.CreateUIAgentComponentRecordReq{
		TaskId:            source.GetTaskId(),
		ExecuteId:         x.subExecuteID,
		ParentExecuteId:   data.GetComponentExecuteId(), // 这里创建的执行记录，使用当前执行ID作为父执行ID
		ProjectId:         source.GetProjectId(),
		ComponentId:       data.GetComponentId(),
		ComponentName:     component.GetName(),
		TriggerMode:       source.GetTriggerMode(),
		ExecuteType:       info.GetExecuteType(),
		ApplicationConfig: component.GetApplicationConfig(),
		Mode:              component.GetMode(),
		Steps:             component.GetSteps(),
		Expectation:       component.GetExpectation(),
		Variables:         component.GetVariables(),
		ForegroundCheck:   component.GetForegroundCheck(),
		Device:            info.GetDevice(),
		Reinstall:         info.GetReinstall(),
		Restart:           info.GetRestart(),
		ReferenceId:       info.GetReferenceId(),
		Times:             1, // 子记录的执行次数都为1
		Status:            pb.ComponentState_Pending.String(),
		ExecutedBy:        source.GetUserId(),
		StartedAt:         timestamppb.New(time.Now()),
	}
	return x.CreateUIAgentComponentRecord(req)
}

func (x *UIAgentComponent) modifyRecord() error {
	req := &reporterpb.ModifyUIAgentComponentRecordReq{
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   x.Source().GetUiAgentComponent().GetComponentExecuteId(),
		ProjectId:   x.Source().GetProjectId(),
		ComponentId: x.Source().GetUiAgentComponent().GetComponentId(),
		Status:      x.StateM().State().String(),
		EndedAt:     nil,
		ErrMsg:      nil,
	}

	endedAt := timestamppb.New(time.Now())
	if x.Total() == 0 {
		req.EndedAt = endedAt
	}
	switch x.StateM().State() {
	case pb.ComponentState_Success:
		req.Successes = 1
		req.EndedAt = endedAt
	case pb.ComponentState_Skip,
		pb.ComponentState_Failure,
		pb.ComponentState_Panic,
		pb.ComponentState_Stop,
		pb.ComponentState_Invalid:
		req.EndedAt = endedAt
	}

	return x.ModifyUIAgentComponentRecord(req)
}

func (x *UIAgentComponent) Content() string {
	return x.logW.toJson()
}
