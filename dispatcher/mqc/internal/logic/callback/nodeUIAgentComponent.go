package callback

import (
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ Node = (*UIAgentComponentNode)(nil)

type UIAgentComponentNode struct {
	*BaseNode

	timesInfo *pb.UIAgentComponentTimesInfo
}

func NewUIAgentComponentNode(task *Callback) *UIAgentComponentNode {
	info, err := task.GetUIAgentComponentTimesInfo(task.Source().GetTaskId())
	if re, ok := errorx.RootError(err); ok && re.Code() != errorx.NotExists {
		task.Errorf("NewUIAgentComponentNode: %s", err)
	}

	return &UIAgentComponentNode{
		BaseNode: NewBaseNode(task),

		timesInfo: info,
	}
}

func (node *UIAgentComponentNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT
}

func (node *UIAgentComponentNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	var member *dtctl.DispatchTaskMember

	data := node.Task().Source().GetUiAgentComponent()
	if data.GetParentExecuteId() != "" {
		// 父执行ID非空，即为多次执行，当前执行记录为子执行记录
		member = &dtctl.DispatchTaskMember{
			ComponentId:        data.GetComponentId(),
			ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
			ComponentExecuteId: data.GetParentExecuteId(),
		}
	} else {
		// 父执行ID为空，即为单次执行，当前执行记录为主执行记录
		member = &dtctl.DispatchTaskMember{
			ComponentId:        data.GetComponentId(),
			ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
			ComponentExecuteId: data.GetComponentExecuteId(),
		}
	}

	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *UIAgentComponentNode) GetMember() *dtctl.DispatchTaskMember {
	data := node.Task().Source().GetUiAgentComponent()
	return &dtctl.DispatchTaskMember{
		ComponentId:        data.GetComponentId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
		ComponentExecuteId: data.GetComponentExecuteId(),
	}
}

func (node *UIAgentComponentNode) State() pb.ComponentState {
	return node.Task().Source().GetUiAgentComponent().GetComponentState()
}

func (node *UIAgentComponentNode) SetState(s pb.ComponentState) {
	node.Task().Source().GetUiAgentComponent().ComponentState = s
}

func (node *UIAgentComponentNode) TaskInfoProcessorSync() {
	switch node.State() {
	case pb.ComponentState_Panic, pb.ComponentState_Stop, pb.ComponentState_Invalid:
		return
	}

	if node.timesInfo == nil {
		return
	}

	defer func() {
		_ = node.Task().UpdateUIAgentComponentTimesInfo(node.Task().Source().GetTaskId(), node.timesInfo)
	}()
	if err := node.publish(); err != nil {
		node.Task().Errorf("UIAgentComponentNode: publish: %s", err)
	}
}

func (node *UIAgentComponentNode) Record() error {
	var (
		source          = node.Task().Source()
		component       = source.GetUiAgentComponent()
		parentExecuteID = component.GetParentExecuteId()
	)

	if parentExecuteID != "" {
		successes := int32(0)
		state := node.Task().StateM().State()
		if node.timesInfo != nil {
			successes = node.timesInfo.GetSuccess()

			if state == pb.ComponentState_Success && node.timesInfo.GetTotal() != node.timesInfo.GetSuccess() {
				state = pb.ComponentState_Failure
			}
		}

		_, err := node.Task().ServiceContext().UIAgentReporterRPC.ModifyUIAgentComponentRecord(
			node.Task().Context(), &reporterpb.ModifyUIAgentComponentRecordReq{
				TaskId:      source.GetTaskId(),
				ExecuteId:   parentExecuteID,
				ProjectId:   source.GetProjectId(),
				ComponentId: component.GetComponentId(),
				Successes:   successes,
				Status:      state.String(),
				EndedAt:     timestamppb.New(time.Now()),
			},
		)
		return err
	}

	return nil
}

func (node *UIAgentComponentNode) RecordCallback() (err error) {
	var (
		source    = node.Task().Source()
		component = source.GetUiAgentComponent()
	)

	var successes int32 = 0
	if component.GetComponentState() == pb.ComponentState_Success {
		successes = 1
	}

	if _, err = node.Task().ServiceContext().UIAgentReporterRPC.ModifyUIAgentComponentRecord(
		node.Task().Context(), &reporterpb.ModifyUIAgentComponentRecordReq{
			TaskId:      source.GetTaskId(),
			ExecuteId:   component.GetComponentExecuteId(),
			ProjectId:   source.GetProjectId(),
			ComponentId: component.GetComponentId(),
			Successes:   successes,
			Status:      component.GetComponentState().String(),
			EndedAt:     timestamppb.New(time.Now()),
			ErrMsg:      component.GetErrMsg(),
		},
	); err != nil {
		return err
	}

	if node.timesInfo != nil {
		node.timesInfo.Success += successes
	}

	return nil
}

func (node *UIAgentComponentNode) Teardown() error {
	return nil
}

func (node *UIAgentComponentNode) publish() error {
	if node.timesInfo == nil || node.timesInfo.GetSent() >= node.timesInfo.GetTotal() {
		return nil
	}

	req := node.timesInfo.GetReq()
	req.ExecuteId = utils.GenExecuteId()
	if v, ok := req.GetData().(*pb.WorkerReq_UiAgentComponent); ok && v.UiAgentComponent != nil {
		v.UiAgentComponent.ComponentExecuteId = req.ExecuteId
	}

	if err := node.createRecord(req); err != nil {
		return err
	}

	if _, err := node.Task().ServiceContext().UIAgentWorkerProducer.Send(
		node.Task().Context(), base.NewTask(
			constants.MQTaskTypeUIAgentWorkerExecuteComponentTask,
			protobuf.MarshalJSONIgnoreError(req),
			base.WithMaxRetryOptions(0),
			base.WithTimeoutOptions(time.Hour),
			base.WithRetentionOptions(time.Hour),
		), mq.ConvertPbEnumerationToQueuePriority(req.GetPriorityType()),
	); err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	node.timesInfo.Sent++
	return nil
}

func (node *UIAgentComponentNode) createRecord(source *pb.WorkerReq) error {
	data := source.GetNodeData().GetUiAgentComponent()
	component := source.GetUiAgentComponent()

	var times int32
	if component.GetParentExecuteId() != "" {
		times = 1 // 子记录的执行次数都为1
	} else {
		times = component.GetTimes()
	}

	req := &reporterpb.CreateUIAgentComponentRecordReq{
		TaskId:            source.GetTaskId(),
		ExecuteId:         source.GetExecuteId(),
		ParentExecuteId:   component.GetParentExecuteId(),
		ProjectId:         source.GetProjectId(),
		ComponentId:       component.GetComponentId(),
		ComponentName:     data.GetName(),
		TriggerMode:       source.GetTriggerMode(),
		ExecuteType:       component.GetExecuteType(),
		ApplicationConfig: data.GetApplicationConfig(),
		Mode:              data.GetMode(),
		Steps:             data.GetSteps(),
		Expectation:       data.GetExpectation(),
		Variables:         data.GetVariables(),
		Device:            component.GetDevice(),
		Reinstall:         component.GetReinstall(),
		Restart:           component.GetRestart(),
		ReferenceId:       component.GetReferenceId(),
		Times:             times,
		Status:            pb.ComponentState_Pending.String(),
		ExecutedBy:        source.GetUserId(),
		StartedAt:         timestamppb.New(time.Now()),
	}
	return node.Task().CreateUIAgentComponentRecord(req)
}
