package tasks

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/hibiken/asynq"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

var _ base.Handler = (*ProcessorGenerateReport)(nil)

type ProcessorGenerateReport struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorGenerateReport(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorGenerateReport{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorGenerateReport) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	consume, err := NewGenerateReportTaskLogic(ctx, processor.svcCtx).Consume(task.Payload)
	if err != nil {
		logger.Errorf("processor error: %+v", err)
		return nil, err
	}

	return consume, nil
}

type GenerateReportTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateReportTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateReportTaskLogic {
	return &GenerateReportTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateReportTaskLogic) Consume(payload []byte) (result []byte, err error) {
	var info dispatcherpb.GenerateReportTaskInfo
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the generate report task payload[%s], error: %+v", payload, err,
		)
	}

	taskID := info.GetTaskId()
	if taskID == "" {
		return nil, errorx.Errorf(
			codes.ExecuteSubTaskFailure,
			"task_id can't be empty, project_id: %s, plan_id: %s",
			info.GetProjectId(), info.GetPlanId(),
		)
	}
	defer func() {
		err = l.callbackToDispatcher(&info)

		l.cleanupWaitingTasks(taskID)
	}()

	// avoid improper termination of ui test task
	time.Sleep(2 * common.DefaultPeriodOfWatchStopSignal)

	if err = l.generateAllureReport(taskID); err != nil {
		return nil, err
	}

	defer func() {
		l.cleanupExecutionData(taskID)
	}()

	if err = l.compressedAllureReport(taskID); err != nil {
		return nil, err
	}

	return nil, nil
}

func (l *GenerateReportTaskLogic) ConsumeV1(payload []byte) (result []byte, err error) {
	var info dispatcherpb.GenerateReportTaskInfo
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the generate-report task payload[%s], error: %+v", payload, err,
		)
	}

	if info.GetTaskId() == "" {
		return nil, errorx.Errorf(
			codes.ExecuteSubTaskFailure,
			"task_id can't be empty, project_id: %s, plan_id: %s",
			info.GetProjectId(), info.GetPlanId(),
		)
	}

	var (
		shortTaskID       = info.GetTaskId()
		commandString     string
		zipCommandsString string
	)

	if ss := strings.Split(shortTaskID, ":"); len(ss) >= 2 {
		shortTaskID = ss[1]
	}

	basePath := l.svcCtx.Config.PVCPath
	frameworkPath := filepath.Join(basePath, common.ConstUITestFrameworkFolderName, shortTaskID)
	dataPath := filepath.Join(basePath, common.ConstUITestDataPathFolderName, shortTaskID)
	appFile := filepath.Join(basePath, common.ConstAppDownloadFolderName, shortTaskID+".*")

	// 注：这里不使用`shortTaskID`，因为`nginx`配置了根据`task_id:`前缀进行转发
	reportPath := filepath.Join(basePath, common.ConstUITestReportPathFolderName, info.GetTaskId())
	zipPath := filepath.Join(basePath, common.ConstUITestReportPathFolderName, fmt.Sprintf("%s.zip", info.GetTaskId()))

	commandString = fmt.Sprintf("allure generate %s -o %s", dataPath, reportPath)
	zipCommandsString = fmt.Sprintf("zip -r %s %s -x *.mp4", zipPath, reportPath)

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Minute)
	defer cancel()

	l.Infof("begin to execute the shell command, command: %q", commandString)
	cmd := utils.CommandContext(ctx, "sh", "-c", commandString)
	cmd.WaitDelay = time.Second
	output, err := cmd.CombinedOutput()
	outputString := string(output)
	if err != nil {
		logx.Errorf(
			"failed to execute the shell command, command: %q, output: %s, error: %+v",
			commandString, outputString, err,
		)
		return nil, fmt.Errorf(
			"failed to execute the shell command, command: %q, output: %s, error: %+v",
			commandString, outputString, err,
		)
	}
	logx.Infof("finish to execute the shell command, command: %q, output: %s", commandString, outputString)

	reportRemotePath := strings.Join(
		[]string{l.svcCtx.Config.ReportRemoteBaseURL, info.GetTaskId(), "index.html"}, "/",
	)

	// 删除测试数据
	defer func(ds []string) {
		for _, d := range ds {
			err := os.RemoveAll(d)
			if err != nil {
				logx.Errorf("failed to remove file or directory, path: %s, error: %+v", d, err)
			}
		}
	}([]string{appFile, dataPath, frameworkPath})

	l.Infof("begin to execute the shell command, command: %q", zipCommandsString)
	cmd = utils.CommandContext(ctx, "sh", "-c", zipCommandsString)
	cmd.WaitDelay = time.Second
	output, err = cmd.CombinedOutput()
	outputString = string(output)
	if err != nil {
		logx.Errorf(
			"failed to execute the shell command, command: %q, output: %s, error: %+v",
			zipCommandsString, outputString, err,
		)
		return nil, fmt.Errorf(
			"failed to execute the shell command, command: %q, output: %s, error: %+v",
			zipCommandsString, outputString, err,
		)
	}
	logx.Infof("finish to execute the shell command, command: %q, output: %s", zipCommandsString, outputString)

	zipRemotePath := strings.Join(
		[]string{l.svcCtx.Config.ReportRemoteBaseURL, fmt.Sprintf("%s.zip", info.GetTaskId())}, "/",
	)

	uuid, ok := asynq.GetTaskID(l.ctx)
	if !ok {
		msg := fmt.Sprintf("failed to set uuid: %s", uuid)
		return nil, errorx.Err(codes.ExecuteSubTaskFailure, msg)
	}

	data, err := protobuf.MarshalJSON(
		&dispatcherpb.UIReportCallback{
			ProjectId:         info.GetProjectId(),
			TaskId:            info.GetTaskId(),
			PlanId:            info.GetPlanId(),
			PlanExecuteId:     info.GetUiCase().GetUiPlanExecuteId(),
			ReportViewUrl:     reportRemotePath,
			ReportDownloadUrl: zipRemotePath,
		},
	)
	if err != nil {
		logx.Errorf("callback 序列化失败, error: %+v", err)
	} else {
		task := base.NewTask(constants.MQTaskTypeUIWorkerGenerateReportResult, data, base.WithMaxRetryOptions(0))
		_, err := l.svcCtx.DispatcherProducer.Send(l.ctx, task, base.QueuePriorityDefault)
		if err != nil {
			logx.Errorf("generate report callback error: %+v", err)
		}
	}

	return nil, nil
}

func (l *GenerateReportTaskLogic) generateAllureReport(taskID string) error {
	shortTaskID := taskID
	if ss := strings.Split(shortTaskID, ":"); len(ss) >= 2 {
		shortTaskID = ss[1]
	}

	basePath := l.svcCtx.Config.PVCPath
	dataPath := filepath.Join(basePath, common.ConstUITestDataPathFolderName, shortTaskID)
	// 注：这里不使用`shortTaskID`，因为`nginx`配置了根据`task_id:`前缀进行转发
	reportPath := filepath.Join(basePath, common.ConstUITestReportPathFolderName, taskID)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// allure generate /data/uiworker_tmp/uitest_data/${short_task_id} -o /data/uiworker_tmp/uitest_report/${task_id}
	cmd := utils.CommandContext(ctx, "sh", "-c", fmt.Sprintf("allure generate %s -o %s", dataPath, reportPath))
	cmd.WaitDelay = time.Second

	command := cmd.String()
	l.Infof("begin to execute the shell command, command: %q", command)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return errors.Errorf(
			"failed to execute the shell command, command: %q, output: %s, error: %+v",
			command, output, err,
		)
	}

	logx.Infof("finish to execute the shell command, command: %q, output: %s", command, output)
	return nil
}

func (l *GenerateReportTaskLogic) compressedAllureReport(taskID string) error {
	basePath := l.svcCtx.Config.PVCPath
	// 注：这里不使用`shortTaskID`，因为`nginx`配置了根据`task_id:`前缀进行转发
	reportBasePath := filepath.Join(basePath, common.ConstUITestReportPathFolderName)
	zipFilePath := filepath.Join(reportBasePath, taskID+".zip")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// cd /data/uiworker_tmp/uitest_report && zip -r -q /data/uiworker_tmp/uitest_report/${task_id}.zip ./${task_id} -x *.mp4 -x *.avi && cd - > /dev/null 2>&1
	// Deprecated: zip -r -q /data/uiworker_tmp/uitest_report/${task_id}.zip /data/uiworker_tmp/uitest_report/${task_id} -x *.mp4 -x *.avi
	cmd := utils.CommandContext(
		ctx, "sh", "-c", fmt.Sprintf(
			"cd %s && zip -r -q %s ./%s -x *.mp4 -x *.avi && cd - > /dev/null 2>&1", reportBasePath, zipFilePath,
			taskID,
		),
	)
	cmd.WaitDelay = time.Second

	command := cmd.String()
	l.Infof("begin to execute the shell command, command: %q", command)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return errors.Errorf(
			"failed to execute the shell command, command: %q, output: %s, error: %+v",
			command, output, err,
		)
	}

	logx.Infof("finish to execute the shell command, command: %q, output: %s", command, output)
	return nil
}

func (l *GenerateReportTaskLogic) callbackToDispatcher(info *dispatcherpb.GenerateReportTaskInfo) error {
	taskID := info.GetTaskId()
	payload := &dispatcherpb.UIReportCallback{
		ProjectId:     info.GetProjectId(),
		TaskId:        taskID,
		PlanId:        info.GetPlanId(),
		PlanExecuteId: info.GetUiCase().GetUiPlanExecuteId(),
		ReportViewUrl: strings.Join(
			[]string{l.svcCtx.Config.ReportRemoteBaseURL, taskID, "index.html"}, "/",
		),
		ReportDownloadUrl: strings.Join(
			[]string{l.svcCtx.Config.ReportRemoteBaseURL, taskID + ".zip"}, "/",
		),
	}
	data, err := protobuf.MarshalJSON(payload)
	if err != nil {
		return errors.Errorf(
			"failed to marshal the payload of UI reporte callback task, payload: %+v, error: %+v", payload, err,
		)
	}

	task := base.NewTask(constants.MQTaskTypeUIWorkerGenerateReportResult, data, base.WithMaxRetryOptions(0))
	if _, err = l.svcCtx.DispatcherProducer.Send(l.ctx, task, base.QueuePriorityDefault); err != nil {
		return errors.Errorf(
			"failed to send UI reporte callback task to dispatcher, payload: %s, error: %+v", data, err,
		)
	}

	return nil
}

func (l *GenerateReportTaskLogic) cleanupExecutionData(taskID string) {
	shortTaskID := taskID
	if ss := strings.Split(shortTaskID, ":"); len(ss) >= 2 {
		shortTaskID = ss[1]
	}

	basePath := l.svcCtx.Config.PVCPath

	// remove UI test data and test framework
	dataPath := filepath.Join(basePath, common.ConstUITestDataPathFolderName, shortTaskID)
	frameworkPath := filepath.Join(basePath, common.ConstUITestFrameworkFolderName, shortTaskID)

	paths := []string{dataPath, frameworkPath}

	// remove UI test app package
	pattern := filepath.Join(basePath, common.ConstAppDownloadFolderName, shortTaskID+".*")
	files, err := filepath.Glob(pattern)
	if err != nil {
		l.Errorf("failed to find matching files, pattern: %s, error: %+v", pattern, err)
	} else {
		paths = append(paths, files...)
	}

	for _, path := range paths {
		if err := os.RemoveAll(path); err != nil {
			l.Errorf("failed to remove file or directory, path: %s, error: %+v", path, err)
		}
	}
}

func (l *GenerateReportTaskLogic) cleanupWaitingTasks(taskID string) {
	if _, err := l.svcCtx.WaitingTasksModel.RemoveByTaskID(l.ctx, nil, taskID); err != nil {
		l.Errorf("failed to remove the waiting tasks, task_id: %s, error: %+v", taskID, err)
	}
}
