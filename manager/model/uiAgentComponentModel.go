package model

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ UiAgentComponentModel = (*customUiAgentComponentModel)(nil)

	uiAgentComponentInsertFields = stringx.Remove(
		uiAgentComponentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// UiAgentComponentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiAgentComponentModel.
	UiAgentComponentModel interface {
		uiAgentComponentModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiAgentComponent) squirrel.InsertBuilder
		UpdateBuilder(data *UiAgentComponent) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiAgentComponent, error)

		FindCountBySearchReq(ctx context.Context, req SearchUIAgentComponentReq) (int64, error)
		FindAllBySearchReq(ctx context.Context, req SearchUIAgentComponentReq) ([]*UiAgentComponent, error)
		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
	}

	customUiAgentComponentModel struct {
		*defaultUiAgentComponentModel

		conn sqlx.SqlConn
	}
)

// NewUiAgentComponentModel returns a model for the database table.
func NewUiAgentComponentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) UiAgentComponentModel {
	return &customUiAgentComponentModel{
		defaultUiAgentComponentModel: newUiAgentComponentModel(conn, c, opts...),
		conn:                         conn,
	}
}

func (m *customUiAgentComponentModel) Table() string {
	return m.table
}

func (m *customUiAgentComponentModel) Fields() []string {
	return uiAgentComponentFieldNames
}

func (m *customUiAgentComponentModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customUiAgentComponentModel) InsertBuilder(data *UiAgentComponent) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiAgentComponentInsertFields...).Values(
		data.ProjectId, data.CategoryId, data.ComponentId, data.Name, data.Description, data.State, data.Tags,
		data.PlatformType, data.ApplicationId, data.Mode, data.AgentModeSteps, data.StepModeSteps, data.Expectation,
		data.Variables, data.ForegroundCheck, data.ReferenceId, data.LatestExecutedAt, data.LatestResult, data.Deleted,
		data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiAgentComponentModel) UpdateBuilder(data *UiAgentComponent) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":        data.CategoryId,
		"`name`":               data.Name,
		"`description`":        data.Description,
		"`state`":              data.State,
		"`tags`":               data.Tags,
		"`platform_type`":      data.PlatformType,
		"`application_id`":     data.ApplicationId,
		"`mode`":               data.Mode,
		"`agent_mode_steps`":   data.AgentModeSteps,
		"`step_mode_steps`":    data.StepModeSteps,
		"`expectation`":        data.Expectation,
		"`variables`":          data.Variables,
		"`foreground_check`":   data.ForegroundCheck,
		"`reference_id`":       data.ReferenceId,
		"`latest_executed_at`": data.LatestExecutedAt,
		"`latest_result`":      data.LatestResult,
		"`deleted`":            data.Deleted,
		"`maintained_by`":      data.MaintainedBy,
		"`updated_by`":         data.UpdatedBy,
		"`deleted_by`":         data.DeletedBy,
		"`deleted_at`":         data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiAgentComponentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiAgentComponentFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentComponentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentComponentModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiAgentComponentModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiAgentComponent, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiAgentComponent
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiAgentComponentModel) FindCountBySearchReq(ctx context.Context, req SearchUIAgentComponentReq) (
	int64, error,
) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectID, constants.NotDeleted,
		)
	if req.DrillDown {
		sub := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectID,
				Type:          common.ConstCategoryTreeTypeUIAgentComponent,
				CategoryId:    req.CategoryID,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").
				Suffix(
					fmt.Sprintf(
						") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
					),
				),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryID)
	}
	return m.FindCount(ctx, sqlbuilder.SearchOptionsWithAlias(sb, aliasT, sqlbuilder.WithCondition(m, req.Condition)))
}

func (m *customUiAgentComponentModel) FindAllBySearchReq(
	ctx context.Context, req SearchUIAgentComponentReq,
) ([]*UiAgentComponent, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select(utils.AddTableNameToFields(aliasT, m.Fields())...).
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectID, constants.NotDeleted,
		)
	if req.DrillDown {
		sub := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectID,
				Type:          common.ConstCategoryTreeTypeUIAgentComponent,
				CategoryId:    req.CategoryID,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").
				Suffix(
					fmt.Sprintf(
						") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
					),
				),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryID)
	}

	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptionsWithAlias(
			sb, aliasT,
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

func (m *customUiAgentComponentModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeUIAgentComponent,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}
