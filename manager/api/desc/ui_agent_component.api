syntax = "v1"

import "ui_agent_component_types.api"

@server (
    prefix: manager/v1
    group: uiAgentComponent
)
service manager {
    @handler createUIAgentComponent
    post /ui_agent_component/create (CreateUIAgentComponentReq) returns (CreateUIAgentComponentResp)

    @handler removeUIAgentComponent
    put /ui_agent_component/remove (RemoveUIAgentComponentReq) returns (RemoveUIAgentComponentResp)

    @handler modifyUIAgentComponent
    put /ui_agent_component/modify (ModifyUIAgentComponentReq) returns (ModifyUIAgentComponentResp)

    @handler searchUIAgentComponent
    post /ui_agent_component/search (SearchUIAgentComponentReq) returns (SearchUIAgentComponentResp)

    @handler viewUIAgentComponent
    get /ui_agent_component/view (ViewUIAgentComponentReq) returns (ViewUIAgentComponentResp)

    @handler diffUIAgentComponent
    post /ui_agent_component/diff (DiffUIAgentComponentReq) returns (DiffUIAgentComponentResp)
}

@server (
    prefix: manager/v1
    group: uiAgentComponent
    timeout: 32s
)
service manager {
    @handler optimizeUIAgentComponentSteps
    post /ui_agent_component/steps/optimize (OptimizeUIAgentComponentStepsReq) returns (OptimizeUIAgentComponentStepsResp)

    @handler createUIAgentComponentReferenceConfig
    post /ui_agent_component/reference_config/create (CreateUIAgentComponentReferenceConfigReq) returns (CreateUIAgentComponentReferenceConfigResp)

    @handler removeUIAgentComponentReferenceConfig
    delete /ui_agent_component/reference_config/remove (RemoveUIAgentComponentReferenceConfigReq) returns (RemoveUIAgentComponentReferenceConfigResp)

    @handler modifyUIAgentComponentReferenceConfig
    put /ui_agent_component/reference_config/modify (ModifyUIAgentComponentReferenceConfigReq) returns (ModifyUIAgentComponentReferenceConfigResp)

    @handler viewUIAgentComponentReferenceConfig
    get /ui_agent_component/reference_config/view (ViewUIAgentComponentReferenceConfigReq) returns (ViewUIAgentComponentReferenceConfigResp)
}
