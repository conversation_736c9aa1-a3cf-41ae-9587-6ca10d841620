package uiAgentComponent

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DiffUIAgentComponentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDiffUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DiffUIAgentComponentLogic {
	return &DiffUIAgentComponentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DiffUIAgentComponentLogic) DiffUIAgentComponent(req *types.DiffUIAgentComponentReq) (resp *types.DiffUIAgentComponentResp, err error) {
	// todo: add your logic here and delete this line

	return
}
