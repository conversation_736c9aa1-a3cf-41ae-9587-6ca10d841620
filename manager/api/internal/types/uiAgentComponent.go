package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type UIAgentComponent struct {
	ProjectId        string                             `json:"project_id"`
	CategoryId       string                             `json:"category_id"`
	ComponentId      string                             `json:"component_id"`
	Name             string                             `json:"name"`
	Description      string                             `json:"description"`
	State            int8                               `json:"state"`
	Tags             []string                           `json:"tags"`
	PlatformType     int64                              `json:"platform_type"`
	ApplicationId    string                             `json:"application_id"`
	Mode             int8                               `json:"mode"`
	AgentModeSteps   []*types.UIAgentComponentStep      `json:"agent_mode_steps"`
	StepModeSteps    []*types.UIAgentComponentStep      `json:"step_mode_steps"`
	Expectation      *types.UIAgentComponentExpectation `json:"expectation"`
	Variables        []*types.KeyValuePair              `json:"variables"`
	ForegroundCheck  bool                               `json:"foreground_check"`
	ReferenceId      string                             `json:"reference_id"`
	LatestExecutedAt int64                              `json:"latest_executed_at"`
	LatestResult     int64                              `json:"latest_result"`
	MaintainedBy     *userinfo.FullUserInfo             `json:"maintained_by"`
	CreatedBy        *userinfo.FullUserInfo             `json:"created_by"`
	UpdatedBy        *userinfo.FullUserInfo             `json:"updated_by"`
	CreatedAt        int64                              `json:"created_at"`
	UpdatedAt        int64                              `json:"updated_at"`
}

type DiffDetail struct {
	Field   int8 `json:"field"`
	Changed bool `json:"changed"`
}

type OptimizeUIAgentComponentStepsReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	Steps     string `json:"steps" validate:"required" zh:"步骤内容"`
}

type OptimizeUIAgentComponentStepsResp struct {
	Steps string `json:"steps"`
}

type CreateUIAgentComponentReq struct {
	ProjectId       string                             `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId      string                             `json:"category_id" validate:"required" zh:"分类ID"`
	Name            string                             `json:"name" validate:"gte=1,lte=64" zh:"组件名称"`
	Description     string                             `json:"description" validate:"lte=255" zh:"组件描述"`
	Tags            []string                           `json:"tags" validate:"gte=0,dive,gte=1,lte=64" zh:"组件标签"`
	ApplicationId   string                             `json:"application_id" validate:"required" zh:"应用配置ID"`
	Mode            int8                               `json:"mode,options=1|2" validate:"oneof=1 2" zh:"模式"`
	AgentModeSteps  []*types.UIAgentComponentStep      `json:"agent_mode_steps" validate:"gte=0,dive,required" zh:"Agent模式步骤列表"`
	StepModeSteps   []*types.UIAgentComponentStep      `json:"step_mode_steps" validate:"gte=0,dive,required" zh:"Step模式步骤列表"`
	Expectation     *types.UIAgentComponentExpectation `json:"expectation" zh:"期望结果"`
	Variables       []*types.KeyValuePair              `json:"variables" validate:"gte=0,dive,required" zh:"变量列表"`
	ForegroundCheck bool                               `json:"foreground_check,default=true" zh:"是否检查App在前台"`
	MaintainedBy    string                             `json:"maintained_by" validate:"lte=64" zh:"组件维护者"`
}

type CreateUIAgentComponentResp struct {
	ComponentId string `json:"component_id"`
}

type RemoveUIAgentComponentReq struct {
	ProjectId    string   `json:"project_id" validate:"required" zh:"项目ID"`
	ComponentIds []string `json:"component_ids" validate:"gt=0" zh:"组件ID列表"`
}

type RemoveUIAgentComponentResp struct{}

type ModifyUIAgentComponentReq struct {
	ProjectId       string                             `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId      string                             `json:"category_id" validate:"required" zh:"分类ID"`
	ComponentId     string                             `json:"component_id" validate:"required" zh:"组件ID"`
	Name            string                             `json:"name" validate:"gte=1,lte=64" zh:"组件名称"`
	Description     string                             `json:"description" validate:"lte=255" zh:"组件描述"`
	Tags            []string                           `json:"tags" validate:"gte=0,dive,gte=1,lte=64" zh:"组件标签"`
	ApplicationId   string                             `json:"application_id" validate:"required" zh:"应用配置ID"`
	Mode            int8                               `json:"mode,options=1|2" validate:"oneof=1 2" zh:"模式"`
	AgentModeSteps  []*types.UIAgentComponentStep      `json:"agent_mode_steps" validate:"gte=0,dive,required" zh:"Agent模式步骤列表"`
	StepModeSteps   []*types.UIAgentComponentStep      `json:"step_mode_steps" validate:"gte=0,dive,required" zh:"Step模式步骤列表"`
	Expectation     *types.UIAgentComponentExpectation `json:"expectation" zh:"期望结果"`
	Variables       []*types.KeyValuePair              `json:"variables" validate:"gte=0,dive,required" zh:"变量列表"`
	ForegroundCheck bool                               `json:"foreground_check,default=true" zh:"是否检查App在前台"`
	State           int8                               `json:"state" validate:"oneof=1 2" zh:"组件状态"`
	MaintainedBy    string                             `json:"maintained_by" validate:"lte=64" zh:"组件维护者"`
}

type ModifyUIAgentComponentResp struct{}

type SearchUIAgentComponentReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchUIAgentComponentResp struct {
	CurrentPage uint64              `json:"current_page"`
	PageSize    uint64              `json:"page_size"`
	TotalCount  uint64              `json:"total_count"`
	TotalPage   uint64              `json:"total_page"`
	Items       []*UIAgentComponent `json:"items"`
}

type ViewUIAgentComponentReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目ID"`
	ComponentId string `form:"component_id" validate:"required" zh:"组件ID"`
}

type ViewUIAgentComponentResp struct {
	*UIAgentComponent
}

type DiffUIAgentComponentReq struct {
	ProjectId       string                             `json:"project_id" validate:"required" zh:"项目ID"`
	ComponentId     string                             `json:"component_id" validate:"required" zh:"组件ID"`
	DiffFields      []int8                             `json:"diff_fields" validate:"gt=0" zh:"需要比较的字段列表"`
	FastFail        bool                               `json:"fast_fail,default=true" zh:"是否有一个字段有变更就退出判断"`
	Name            string                             `json:"name,optional,omitempty" validate:"omitempty,gte=1,lte=64" zh:"组件名称"`
	Description     string                             `json:"description,optional,omitempty" validate:"omitempty,lte=255" zh:"组件描述"`
	Tags            []string                           `json:"tags,optional,omitempty" validate:"omitempty,gte=0,dive,gte=1,lte=64" zh:"组件标签"`
	ApplicationId   string                             `json:"application_id,optional,omitempty" zh:"应用配置ID"`
	Mode            int8                               `json:"mode,optional,omitempty" validate:"omitempty,oneof=1 2" zh:"模式"`
	AgentModeSteps  []*types.UIAgentComponentStep      `json:"agent_mode_steps,optional,omitempty" validate:"omitempty,gte=0,dive,required" zh:"Agent模式步骤列表"`
	StepModeSteps   []*types.UIAgentComponentStep      `json:"step_mode_steps,optional,omitempty" validate:"omitempty,gte=0,dive,required" zh:"Step模式步骤列表"`
	Expectation     *types.UIAgentComponentExpectation `json:"expectation,optional,omitempty" zh:"期望结果"`
	Variables       []*types.KeyValuePair              `json:"variables,optional,omitempty" validate:"omitempty,gte=0,dive,required" zh:"变量列表"`
	ForegroundCheck bool                               `json:"foreground_check,optional,omitempty" zh:"是否检查App在前台"`
}

type DiffUIAgentComponentResp struct {
	Changed bool          `json:"changed"`
	Details []*DiffDetail `json:"details"`
}

type CreateUIAgentComponentReferenceConfigReq struct {
	ProjectId          string  `json:"project_id" validate:"required" zh:"项目ID"`
	ComponentId        string  `json:"component_id" validate:"required" zh:"组件ID"`
	ReferenceTaskId    string  `json:"reference_task_id" validate:"required" zh:"参考的任务ID"`
	ReferenceExecuteId string  `json:"reference_execute_id" validate:"required" zh:"参考的执行ID"`
	StepIds            []int64 `json:"step_ids" validate:"gt=0" zh:"参考的步骤ID列表"`
}

type CreateUIAgentComponentReferenceConfigResp struct {
	ConfigId string `json:"config_id"`
}

type RemoveUIAgentComponentReferenceConfigReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目ID"`
	ComponentId string `form:"component_id" validate:"required" zh:"组件ID"`
	ReferenceId string `form:"reference_id" validate:"required" zh:"参考配置ID"`
}

type RemoveUIAgentComponentReferenceConfigResp struct{}

type ModifyUIAgentComponentReferenceConfigReq struct {
	ProjectId     string  `json:"project_id" validate:"required" zh:"项目ID"`
	ComponentId   string  `json:"component_id" validate:"required" zh:"组件ID"`
	ReferenceId   string  `json:"reference_id" validate:"required" zh:"参考配置ID"`
	StepIds       []int64 `json:"step_ids" validate:"gt=0" zh:"参考的步骤ID列表"`
	RemoveStepIds []int64 `json:"remove_step_ids,optional,omitempty" zh:"需要删除的步骤ID列表"` // Deprecated: use `StepIds` instead.
}

type ModifyUIAgentComponentReferenceConfigResp struct{}

type ViewUIAgentComponentReferenceConfigReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目ID"`
	ComponentId string `form:"component_id" validate:"required" zh:"组件ID"`
	ReferenceId string `form:"reference_id" validate:"required" zh:"参考配置ID"`
}

type ViewUIAgentComponentReferenceConfigResp struct {
	Steps []*types.UIAgentComponentStepRecord `json:"steps"`
}
