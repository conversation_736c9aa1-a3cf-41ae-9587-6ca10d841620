-- TT SLA数据展示
CREATE TABLE IF NOT EXISTS `sla_threshold`
(
    `id`            INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `platform_type` TINYINT(1)   NOT NULL DEFAULT '1' COMMENT '平台类型（Android、IOS）',
    `branch_type`   TINYINT(1)   NOT NULL DEFAULT '1' COMMENT '分支类型（release、testing）',
    `name`          VARCHAR(255) NOT NULL COMMENT '阈值指标',
    `unit`          VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '阈值单位',
    `value`         INT          NOT NULL COMMENT '阈值数值',
    `deleted`       TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_st_project_id_platform_type_branch_type_name` (`project_id`, `platform_type`, `branch_type`, `name`),
    KEY `ix_st_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_st_project_id_platform_type_branch_type_deleted` (`project_id`, `platform_type`, `branch_type`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'SLA阈值配置表';

CREATE TABLE IF NOT EXISTS `sla_report_notifier`
(
    `id`             int                                     NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`     varchar(64) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '项目ID',
    `account`        varchar(64) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户名（工号）',
    `fullname`       varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
    `full_dept_name` varchar(256) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '完整部门名称',
    `email`          varchar(64) COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '邮箱',
    `lark_user_id`   varchar(64) COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '飞书用户ID',
    `deleted`        tinyint                                 NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     varchar(64) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     varchar(64) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     varchar(64) COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`     timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     timestamp                               NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_srn_project_id_account` (`project_id`, `account`),
    KEY `ix_srn_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'SLA报告通知人员表';


-- UI Agent
CREATE TABLE IF NOT EXISTS `prompt_configuration`
(
    `id`          INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`  VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `config_id`   VARCHAR(64)  NOT NULL COMMENT 'Prompt配置ID',
    `purpose`     TINYINT      NOT NULL DEFAULT 1 COMMENT '用途（UI_AGENT）',
    `category`    TINYINT      NOT NULL COMMENT '分类（背景、UI组件、异常处理）',
    `name`        VARCHAR(64)  NOT NULL COMMENT 'Prompt配置名称',
    `description` VARCHAR(255) NULL COMMENT 'Prompt配置描述',
    `content`     TEXT         NOT NULL COMMENT 'Prompt配置内容',
    `deleted`     TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pc_project_id_config_id` (`project_id`, `config_id`),
    KEY `ix_pc_project_id_purpose_category` (`project_id`, `purpose`, `category`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='Prompt配置表';

CREATE TABLE IF NOT EXISTS `prompt_configuration_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（应用配置）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（应用配置ID）',
    `config_id`      VARCHAR(64) NOT NULL COMMENT 'Prompt配置ID',
    `deleted`        TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_pcrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_pcrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_pcrr_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='Prompt配置引用关系表';

CREATE TABLE IF NOT EXISTS `application_configuration`
(
    `id`                INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`        VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `config_id`         VARCHAR(64)  NOT NULL COMMENT '应用配置ID',
    `name`              VARCHAR(64)  NOT NULL COMMENT '应用配置名称',
    `description`       VARCHAR(255) NULL COMMENT '应用配置描述',
    `platform_type`     TINYINT      NOT NULL DEFAULT 1 COMMENT '平台类型（Android、IOS）',
    `app_id`            VARCHAR(64)  NOT NULL COMMENT '应用ID（Android：package_name；IOS：bundle_id）',
    `app_download_link` VARCHAR(255) NOT NULL COMMENT 'APP下载地址',
    `deleted`           TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ac_project_id_config_id` (`project_id`, `config_id`),
    KEY `ix_ac_project_id_platform_type` (`project_id`, `platform_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='应用配置表';

CREATE TABLE IF NOT EXISTS `application_configuration_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（UI Agent组件）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（UI Agent组件ID）',
    `config_id`      VARCHAR(64) NOT NULL COMMENT '应用配置ID',
    `deleted`        TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_acrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_acrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_acrr_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='应用配置引用关系表';

CREATE TABLE IF NOT EXISTS `ui_agent_component`
(
    `id`                 INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`         VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `category_id`        VARCHAR(64)  NOT NULL COMMENT '所属分类ID',
    `component_id`       VARCHAR(64)  NOT NULL COMMENT '组件ID',
    `name`               VARCHAR(64)  NOT NULL COMMENT '组件名称',
    `description`        VARCHAR(255) NULL COMMENT '组件描述',
    `state`              TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '组件状态（生效、失效）',
    `tags`               JSON         NULL COMMENT '标签',
    `platform_type`      TINYINT      NOT NULL DEFAULT 1 COMMENT '平台类型（Android、IOS）',
    `application_id`     VARCHAR(64)  NOT NULL COMMENT '应用配置ID',
    `mode`               TINYINT      NOT NULL DEFAULT 1 COMMENT '模式（Agent模式、Step模式）',
    `agent_mode_steps`   JSON         NOT NULL COMMENT 'Agent模式步骤列表',
    `step_mode_steps`    JSON         NOT NULL COMMENT 'Step模式步骤列表',
    `expectation`        JSON         NOT NULL COMMENT '期望结果',
    `variables`          JSON         NOT NULL COMMENT '变量列表',
    `foreground_check`   TINYINT      NOT NULL DEFAULT 1 COMMENT '是否检查App在前台',
    `reference_id`       VARCHAR(64)  NULL COMMENT '参考配置ID',
    `latest_executed_at` TIMESTAMP    NULL COMMENT '最近一次执行时间',
    `latest_result`      TINYINT      NOT NULL DEFAULT 1 COMMENT '最近一次执行结果（未执行、成功、失败）',
    `deleted`            TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`      VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`         VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_uac_project_id_component_id` (`project_id`, `component_id`),
    KEY `ix_uac_project_id_category_id_deleted` (`project_id`, `category_id`, `deleted`),
    KEY `ix_uac_project_id_name_deleted` (`project_id`, `name`, `deleted`),
    KEY `ix_uac_project_id_platform_type_deleted` (`project_id`, `platform_type`, `deleted`),
    KEY `ix_uac_project_id_application_id_deleted` (`project_id`, `application_id`, `deleted`),
    KEY `ix_uac_project_id_steps_image` (`project_id`, (CAST(`step_mode_steps`->'$[*].expectation.image' AS CHAR(64) ARRAY))),
    KEY `ix_uac_project_id_expectation_image` (`project_id`, (CAST(`expectation`->'$.image' AS CHAR(64)))),
    KEY `ix_uac_project_id_latest_result_deleted` (`project_id`, `latest_result`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='UI Agent组件表';

CREATE TABLE IF NOT EXISTS `ui_agent_image`
(
    `id`          INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`  VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `image_id`    VARCHAR(64)  NOT NULL COMMENT '图片ID',
    `name`        VARCHAR(64)  NOT NULL COMMENT '图片名称',
    `description` VARCHAR(255) NULL COMMENT '图片描述',
    `extension`   VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '图片文件的扩展名',
    `hash`        VARCHAR(32)  NOT NULL COMMENT '图片文件的一致性哈希值（MD5）',
    `size`        INT          NOT NULL DEFAULT 0 COMMENT '图片文件的大小',
    `path`        VARCHAR(255) NOT NULL COMMENT '图片文件的路径',
    `deleted`     TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_uai_project_id_image_id` (`project_id`, `image_id`),
    KEY `ix_uai_project_id_hash_deleted` (`project_id`, `hash`, `deleted`),
    KEY `ix_uai_project_id_path_deleted` (`project_id`, `path`, `deleted`),
    KEY `ix_uai_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='UI Agent图片表';

CREATE TABLE IF NOT EXISTS `ui_agent_image_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（UI Agent组件）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（UI Agent组件ID）',
    `image_id`       VARCHAR(64) NOT NULL COMMENT '图片ID',
    `deleted`        TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_acrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_acrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_acrr_project_id_image_id_deleted` (`project_id`, `image_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='UI Agent图片引用关系表';
