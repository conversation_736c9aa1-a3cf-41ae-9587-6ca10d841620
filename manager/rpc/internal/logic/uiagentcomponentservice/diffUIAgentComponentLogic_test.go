package uiagentcomponentservicelogic

import (
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/jsonx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestDiffUIAgentComponentLogic_compareStringSlicesIgnoreOrder(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name     string
		a        []string
		b        []string
		expected bool
	}{
		{
			name:     "相同的切片",
			a:        []string{"tag1", "tag2", "tag3"},
			b:        []string{"tag1", "tag2", "tag3"},
			expected: true,
		},
		{
			name:     "不同顺序但相同内容",
			a:        []string{"tag1", "tag2", "tag3"},
			b:        []string{"tag3", "tag1", "tag2"},
			expected: true,
		},
		{
			name:     "不同内容",
			a:        []string{"tag1", "tag2"},
			b:        []string{"tag1", "tag3"},
			expected: false,
		},
		{
			name:     "不同长度",
			a:        []string{"tag1", "tag2"},
			b:        []string{"tag1", "tag2", "tag3"},
			expected: false,
		},
		{
			name:     "空切片",
			a:        []string{},
			b:        []string{},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.compareStringSlicesIgnoreOrder(tt.a, tt.b)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDiffUIAgentComponentLogic_compareName(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name       string
		reqName    string
		originName string
		expected   bool
	}{
		{
			name:       "相同名称",
			reqName:    "test-component",
			originName: "test-component",
			expected:   false,
		},
		{
			name:       "不同名称",
			reqName:    "new-component",
			originName: "old-component",
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &pb.DiffUIAgentComponentReq{
				Name: tt.reqName,
			}
			origin := &model.UiAgentComponent{
				Name: tt.originName,
			}
			result := logic.compareName(req, origin)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDiffUIAgentComponentLogic_compareDescription(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name            string
		reqDesc         string
		originDesc      sql.NullString
		expected        bool
	}{
		{
			name:       "相同描述",
			reqDesc:    "test description",
			originDesc: sql.NullString{String: "test description", Valid: true},
			expected:   false,
		},
		{
			name:       "不同描述",
			reqDesc:    "new description",
			originDesc: sql.NullString{String: "old description", Valid: true},
			expected:   true,
		},
		{
			name:       "新描述为空，原描述不为空",
			reqDesc:    "",
			originDesc: sql.NullString{String: "old description", Valid: true},
			expected:   true,
		},
		{
			name:       "新描述不为空，原描述为空",
			reqDesc:    "new description",
			originDesc: sql.NullString{Valid: false},
			expected:   true,
		},
		{
			name:       "都为空",
			reqDesc:    "",
			originDesc: sql.NullString{Valid: false},
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &pb.DiffUIAgentComponentReq{
				Description: tt.reqDesc,
			}
			origin := &model.UiAgentComponent{
				Description: tt.originDesc,
			}
			result := logic.compareDescription(req, origin)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDiffUIAgentComponentLogic_compareTags(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name        string
		reqTags     []string
		originTags  []string
		expected    bool
		expectError bool
	}{
		{
			name:        "相同标签",
			reqTags:     []string{"tag1", "tag2"},
			originTags:  []string{"tag1", "tag2"},
			expected:    false,
			expectError: false,
		},
		{
			name:        "不同顺序但相同标签",
			reqTags:     []string{"tag1", "tag2"},
			originTags:  []string{"tag2", "tag1"},
			expected:    false,
			expectError: false,
		},
		{
			name:        "不同标签",
			reqTags:     []string{"tag1", "tag2"},
			originTags:  []string{"tag1", "tag3"},
			expected:    true,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &pb.DiffUIAgentComponentReq{
				Tags: tt.reqTags,
			}

			var originTagsJSON sql.NullString
			if len(tt.originTags) > 0 {
				tagsStr := jsonx.MarshalToStringIgnoreError(tt.originTags)
				originTagsJSON = sql.NullString{String: tagsStr, Valid: true}
			}

			origin := &model.UiAgentComponent{
				Tags: originTagsJSON,
			}

			result, err := logic.compareTags(req, origin)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDiffUIAgentComponentLogic_compareStepExpectation(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name     string
		newExp   *commonpb.UIAgentComponentExpectation
		originExp *commonpb.UIAgentComponentExpectation
		expected bool
	}{
		{
			name:     "都为nil",
			newExp:   nil,
			originExp: nil,
			expected: true,
		},
		{
			name:     "新期望为nil，原期望不为nil",
			newExp:   nil,
			originExp: &commonpb.UIAgentComponentExpectation{Text: "test"},
			expected: false,
		},
		{
			name:     "新期望不为nil，原期望为nil",
			newExp:   &commonpb.UIAgentComponentExpectation{Text: "test"},
			originExp: nil,
			expected: false,
		},
		{
			name:     "相同期望",
			newExp:   &commonpb.UIAgentComponentExpectation{Text: "test", Image: "image1"},
			originExp: &commonpb.UIAgentComponentExpectation{Text: "test", Image: "image1"},
			expected: true,
		},
		{
			name:     "不同期望",
			newExp:   &commonpb.UIAgentComponentExpectation{Text: "test1", Image: "image1"},
			originExp: &commonpb.UIAgentComponentExpectation{Text: "test2", Image: "image1"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.compareStepExpectation(tt.newExp, tt.originExp)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDiffUIAgentComponentLogic_compareVariablesList(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name        string
		newVars     []*commonpb.GeneralConfigVar
		originVars  []*commonpb.GeneralConfigVar
		expected    bool
	}{
		{
			name:        "相同变量",
			newVars:     []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value1"}},
			originVars:  []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value1"}},
			expected:    true,
		},
		{
			name:        "不同变量值",
			newVars:     []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value1"}},
			originVars:  []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value2"}},
			expected:    false,
		},
		{
			name:        "不同变量键",
			newVars:     []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value1"}},
			originVars:  []*commonpb.GeneralConfigVar{{Key: "key2", Value: "value1"}},
			expected:    false,
		},
		{
			name:        "不同长度",
			newVars:     []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value1"}},
			originVars:  []*commonpb.GeneralConfigVar{{Key: "key1", Value: "value1"}, {Key: "key2", Value: "value2"}},
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.compareVariablesList(tt.newVars, tt.originVars)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDiffUIAgentComponentLogic_compareFields(t *testing.T) {
	logic := &DiffUIAgentComponentLogic{}

	tests := []struct {
		name           string
		diffFields     []int32
		fastFail       bool
		req            *pb.DiffUIAgentComponentReq
		origin         *model.UiAgentComponent
		expectedChanged bool
		expectedDetails int
	}{
		{
			name:       "单个字段变更 - 名称",
			diffFields: []int32{1}, // DiffFieldName
			fastFail:   false,
			req: &pb.DiffUIAgentComponentReq{
				Name: "new-name",
			},
			origin: &model.UiAgentComponent{
				Name: "old-name",
			},
			expectedChanged: true,
			expectedDetails: 1,
		},
		{
			name:       "多个字段变更 - 快速失败",
			diffFields: []int32{1, 2}, // DiffFieldName, DiffFieldDescription
			fastFail:   true,
			req: &pb.DiffUIAgentComponentReq{
				Name:        "new-name",
				Description: "new-desc",
			},
			origin: &model.UiAgentComponent{
				Name:        "old-name",
				Description: sql.NullString{String: "old-desc", Valid: true},
			},
			expectedChanged: true,
			expectedDetails: 1, // 快速失败，只返回第一个变更的字段
		},
		{
			name:       "多个字段变更 - 不快速失败",
			diffFields: []int32{1, 2}, // DiffFieldName, DiffFieldDescription
			fastFail:   false,
			req: &pb.DiffUIAgentComponentReq{
				Name:        "new-name",
				Description: "new-desc",
			},
			origin: &model.UiAgentComponent{
				Name:        "old-name",
				Description: sql.NullString{String: "old-desc", Valid: true},
			},
			expectedChanged: true,
			expectedDetails: 2, // 返回所有字段的比较结果
		},
		{
			name:       "无变更",
			diffFields: []int32{1, 2}, // DiffFieldName, DiffFieldDescription
			fastFail:   false,
			req: &pb.DiffUIAgentComponentReq{
				Name:        "same-name",
				Description: "same-desc",
			},
			origin: &model.UiAgentComponent{
				Name:        "same-name",
				Description: sql.NullString{String: "same-desc", Valid: true},
			},
			expectedChanged: false,
			expectedDetails: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			changed, details, err := logic.compareFields(tt.req, tt.origin, tt.diffFields, tt.fastFail)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedChanged, changed)
			assert.Equal(t, tt.expectedDetails, len(details))

			// 验证详情中的字段编号
			for i, detail := range details {
				assert.Equal(t, tt.diffFields[i], detail.Field)
			}
		})
	}
}
