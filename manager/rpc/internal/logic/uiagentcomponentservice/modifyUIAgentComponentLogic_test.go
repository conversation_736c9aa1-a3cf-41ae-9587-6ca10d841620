package uiagentcomponentservicelogic

import (
	"database/sql"
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestModifyUIAgentComponentLogic_hasExecutionDataChanged(t *testing.T) {
	logic := &ModifyUIAgentComponentLogic{}

	// 创建原始组件数据
	origin := &model.UiAgentComponent{
		ApplicationId:  "app_id_1",
		Mode:           int64(commonpb.UIAgentMode_UIAgentMode_AGENT),
		AgentModeSteps: `[{"content": "step1", "expectation": {"text": "expect1", "image": ""}, "waiting_time": 1}]`,
		StepModeSteps:  `[{"content": "step2", "expectation": {"text": "expect2", "image": ""}, "waiting_time": 2}]`,
		Expectation: sql.NullString{
			String: `{"text": "original expectation", "image": ""}`,
			Valid:  true,
		},
		Variables: `[{"key": "var1", "value": "value1"}]`,
	}

	tests := []struct {
		name     string
		req      *pb.ModifyUIAgentComponentReq
		expected bool
	}{
		{
			name: "应用配置变更",
			req: &pb.ModifyUIAgentComponentReq{
				ApplicationId: "app_id_2", // 变更应用配置
				Mode:          commonpb.UIAgentMode_UIAgentMode_AGENT,
				AgentModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content:     "step1",
						Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"},
						WaitingTime: 1.0,
					},
				},
				StepModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"},
						WaitingTime: 2.0,
					},
				},
				Expectation: &commonpb.UIAgentComponentExpectation{Text: "original expectation"},
				Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1"}},
			},
			expected: true,
		},
		{
			name: "模式变更",
			req: &pb.ModifyUIAgentComponentReq{
				ApplicationId: "app_id_1",
				Mode:          commonpb.UIAgentMode_UIAgentMode_STEP, // 变更模式
				AgentModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step1", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"},
						WaitingTime: 1.0,
					},
				},
				StepModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"},
						WaitingTime: 2.0,
					},
				},
				Expectation: &commonpb.UIAgentComponentExpectation{Text: "original expectation"},
				Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1"}},
			},
			expected: true,
		},
		{
			name: "Agent模式步骤变更",
			req: &pb.ModifyUIAgentComponentReq{
				ApplicationId: "app_id_1",
				Mode:          commonpb.UIAgentMode_UIAgentMode_AGENT,
				AgentModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step1_modified", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"},
						WaitingTime: 1.0,
					}, // 变更步骤内容
				},
				StepModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"},
						WaitingTime: 2.0,
					},
				},
				Expectation: &commonpb.UIAgentComponentExpectation{Text: "original expectation"},
				Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1"}},
			},
			expected: true,
		},
		{
			name: "期望变更",
			req: &pb.ModifyUIAgentComponentReq{
				ApplicationId: "app_id_1",
				Mode:          commonpb.UIAgentMode_UIAgentMode_AGENT,
				AgentModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step1", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"},
						WaitingTime: 1.0,
					},
				},
				StepModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"},
						WaitingTime: 2.0,
					},
				},
				Expectation: &commonpb.UIAgentComponentExpectation{Text: "modified expectation"}, // 变更期望
				Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1"}},
			},
			expected: true,
		},
		{
			name: "变量变更",
			req: &pb.ModifyUIAgentComponentReq{
				ApplicationId: "app_id_1",
				Mode:          commonpb.UIAgentMode_UIAgentMode_AGENT,
				AgentModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step1", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"},
						WaitingTime: 1.0,
					},
				},
				StepModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"},
						WaitingTime: 2.0,
					},
				},
				Expectation: &commonpb.UIAgentComponentExpectation{Text: "original expectation"},
				Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1_modified"}}, // 变更变量值
			},
			expected: true,
		},
		{
			name: "无变更",
			req: &pb.ModifyUIAgentComponentReq{
				ApplicationId: "app_id_1",
				Mode:          commonpb.UIAgentMode_UIAgentMode_AGENT,
				AgentModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step1", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"},
						WaitingTime: 1.0,
					},
				},
				StepModeSteps: []*commonpb.UIAgentComponentStep{
					{
						Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"},
						WaitingTime: 2.0,
					},
				},
				Expectation: &commonpb.UIAgentComponentExpectation{Text: "original expectation"},
				Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1"}},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				result, err := logic.hasExecutionDataChanged(tt.req, origin)
				if err != nil {
					t.Errorf("hasExecutionDataChanged() error = %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("hasExecutionDataChanged() = %v, expected %v", result, tt.expected)
				}
			},
		)
	}
}

func TestModifyUIAgentComponentLogic_JSONFormatDifference(t *testing.T) {
	logic := &ModifyUIAgentComponentLogic{}

	// 测试JSON格式差异的情况 - 相同内容但格式不同
	origin := &model.UiAgentComponent{
		ApplicationId: "app_id_1",
		Mode:          int64(commonpb.UIAgentMode_UIAgentMode_AGENT),
		// 模拟数据库存储的JSON格式（可能有不同的空格、换行等）
		AgentModeSteps: `[{"content":"step1", "expectation":{"text": "expect1", "image": ""}, "waiting_time":1}]`,
		StepModeSteps:  `[{"content": "step2", "expectation": {"text": "expect2", "image": ""}, "waiting_time": 2}]`,
		Expectation: sql.NullString{
			String: `{"text": "original expectation", "image": ""}`,
			Valid:  true,
		},
		Variables: `[{"key": "var1", "value": "value1"}]`,
	}

	// 请求中的数据（通过protobuf序列化后可能格式略有不同）
	req := &pb.ModifyUIAgentComponentReq{
		ApplicationId: "app_id_1",
		Mode:          commonpb.UIAgentMode_UIAgentMode_AGENT,
		AgentModeSteps: []*commonpb.UIAgentComponentStep{
			{Content: "step1", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect1"}, WaitingTime: 1.0},
		},
		StepModeSteps: []*commonpb.UIAgentComponentStep{
			{Content: "step2", Expectation: &commonpb.UIAgentComponentExpectation{Text: "expect2"}, WaitingTime: 2.0},
		},
		Expectation: &commonpb.UIAgentComponentExpectation{Text: "original expectation"},
		Variables:   []*commonpb.GeneralConfigVar{{Key: "var1", Value: "value1"}},
	}

	// 应该检测为无变更（即使JSON格式略有不同）
	result, err := logic.hasExecutionDataChanged(req, origin)
	if err != nil {
		t.Errorf("hasExecutionDataChanged() error = %v", err)
		return
	}
	if result {
		t.Errorf("hasExecutionDataChanged() = %v, expected false (should handle JSON format differences)", result)
	}
}
