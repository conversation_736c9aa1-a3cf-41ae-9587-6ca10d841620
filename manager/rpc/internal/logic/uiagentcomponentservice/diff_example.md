# DiffUIAgentComponent 接口使用示例

## 接口说明

`DiffUIAgentComponent` 接口用于比较 UI Agent 组件的字段变更。

## 字段枚举

根据需求，字段枚举对应关系如下：

| 字段编号 | 字段名称 | 说明 |
|---------|---------|------|
| 1 | name | 组件名称 |
| 2 | description | 组件描述 |
| 3 | tags | 组件标签（顺序不敏感） |
| 4 | application_id | 应用配置ID |
| 5 | mode | 模式（Agent模式、Step模式） |
| 6 | agent_mode_steps | Agent模式步骤列表 |
| 7 | step_mode_steps | Step模式步骤列表 |
| 8 | expectation | 期望结果 |
| 9 | variables | 变量列表 |
| 10 | foreground_check | 是否检查App在前台 |

## 请求示例

### 1. 比较单个字段（名称）

```json
{
  "project_id": "project_id:test-project",
  "component_id": "ui_agent_component_id:test-component",
  "diff_fields": [1],
  "fast_fail": false,
  "name": "新的组件名称"
}
```

### 2. 比较多个字段（快速失败）

```json
{
  "project_id": "project_id:test-project",
  "component_id": "ui_agent_component_id:test-component",
  "diff_fields": [1, 2, 3],
  "fast_fail": true,
  "name": "新的组件名称",
  "description": "新的组件描述",
  "tags": ["tag1", "tag2", "tag3"]
}
```

### 3. 比较所有字段

```json
{
  "project_id": "project_id:test-project",
  "component_id": "ui_agent_component_id:test-component",
  "diff_fields": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
  "fast_fail": false,
  "name": "新的组件名称",
  "description": "新的组件描述",
  "tags": ["tag1", "tag2"],
  "application_id": "application_config_id:new-app",
  "mode": 1,
  "agent_mode_steps": [
    {
      "content": "点击登录按钮",
      "expectation": {
        "text": "登录成功"
      },
      "waiting_time": 2.5
    }
  ],
  "step_mode_steps": [],
  "expectation": {
    "text": "测试完成",
    "image": "ui_agent_image_id:test-image"
  },
  "variables": [
    {
      "key": "username",
      "value": "testuser"
    }
  ],
  "foreground_check": true
}
```

## 响应示例

### 1. 有变更的响应

```json
{
  "changed": true,
  "details": [
    {
      "field": 1,
      "changed": true
    },
    {
      "field": 2,
      "changed": false
    },
    {
      "field": 3,
      "changed": true
    }
  ]
}
```

### 2. 无变更的响应

```json
{
  "changed": false,
  "details": [
    {
      "field": 1,
      "changed": false
    },
    {
      "field": 2,
      "changed": false
    }
  ]
}
```

### 3. 快速失败的响应

当 `fast_fail` 为 `true` 且发现第一个字段有变更时：

```json
{
  "changed": true,
  "details": [
    {
      "field": 1,
      "changed": true
    }
  ]
}
```

## 特殊比较逻辑

### 1. 标签列表比较
- 标签列表的比较**不考虑顺序**
- `["tag1", "tag2"]` 和 `["tag2", "tag1"]` 被认为是相同的

### 2. 步骤列表比较
- 步骤列表的比较**考虑顺序**
- 比较步骤的内容、等待时间和期望结果

### 3. 变量列表比较
- 变量列表的比较**考虑顺序**
- 比较变量的键和值

### 4. 期望结果比较
- 比较文本和图片字段
- 处理 nil 值的情况

## 错误处理

接口会返回以下类型的错误：

1. **项目ID验证失败**: 当项目不存在时
2. **组件ID验证失败**: 当组件不存在时
3. **序列化错误**: 当数据库中的JSON数据无法反序列化时
4. **不支持的字段**: 当传入的字段编号不在1-10范围内时

## 使用建议

1. **选择合适的字段**: 只比较需要的字段，避免不必要的性能开销
2. **合理使用快速失败**: 当只需要知道是否有变更时，可以启用快速失败
3. **处理错误**: 妥善处理可能的序列化错误和验证错误
