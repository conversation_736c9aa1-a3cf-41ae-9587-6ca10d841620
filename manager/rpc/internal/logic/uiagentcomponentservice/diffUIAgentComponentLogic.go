package uiagentcomponentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DiffUIAgentComponentLogic struct {
	*BaseLogic
}

func NewDiffUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DiffUIAgentComponentLogic {
	return &DiffUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DiffUIAgentComponent 比较`UI Agent`组件
func (l *DiffUIAgentComponentLogic) DiffUIAgentComponent(in *pb.DiffUIAgentComponentReq) (
	out *pb.DiffUIAgentComponentResp, err error,
) {
	//var (
	//	projectID   = in.GetProjectId()
	//	componentID = in.GetComponentId()
	//)
	//
	//// validate the project_id in req
	//if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
	//	return nil, err
	//}
	//
	//// validate the component_id in req
	//origin, err := model.CheckUIAgentComponentByComponentID(
	//	l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	//)
	//if err != nil {
	//	return nil, err
	//}

	return &pb.DiffUIAgentComponentResp{}, nil
}
