package uiagentcomponentservicelogic

import (
	"context"
	"sort"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// DiffField 比较字段枚举
type DiffField int32

const (
	DiffFieldName            DiffField = 1  // 组件名称
	DiffFieldDescription     DiffField = 2  // 组件描述
	DiffFieldTags            DiffField = 3  // 组件标签
	DiffFieldApplicationID   DiffField = 4  // 应用配置ID
	DiffFieldMode            DiffField = 5  // 模式
	DiffFieldAgentModeSteps  DiffField = 6  // Agent模式步骤列表
	DiffFieldStepModeSteps   DiffField = 7  // Step模式步骤列表
	DiffFieldExpectation     DiffField = 8  // 期望结果
	DiffFieldVariables       DiffField = 9  // 变量列表
	DiffFieldForegroundCheck DiffField = 10 // 是否检查App在前台
)

type DiffUIAgentComponentLogic struct {
	*BaseLogic
}

func NewDiffUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DiffUIAgentComponentLogic {
	return &DiffUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DiffUIAgentComponent 比较`UI Agent`组件
func (l *DiffUIAgentComponentLogic) DiffUIAgentComponent(in *pb.DiffUIAgentComponentReq) (
	out *pb.DiffUIAgentComponentResp, err error,
) {
	var (
		projectID   = in.GetProjectId()
		componentID = in.GetComponentId()
		diffFields  = in.GetDiffFields()
		fastFail    = in.GetFastFail()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the component_id in req
	origin, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	)
	if err != nil {
		return nil, err
	}

	// 执行比较逻辑
	changed, details, err := l.compareFields(in, origin, diffFields, fastFail)
	if err != nil {
		return nil, err
	}

	return &pb.DiffUIAgentComponentResp{
		Changed: changed,
		Details: details,
	}, nil
}

// compareFields 比较指定字段
func (l *DiffUIAgentComponentLogic) compareFields(
	in *pb.DiffUIAgentComponentReq,
	origin *model.UiAgentComponent,
	diffFields []int32,
	fastFail bool,
) (bool, []*pb.DiffDetail, error) {
	var (
		overallChanged = false
		details        = make([]*pb.DiffDetail, 0, len(diffFields))
	)

	for _, fieldNum := range diffFields {
		field := DiffField(fieldNum)
		changed, err := l.compareField(in, origin, field)
		if err != nil {
			return false, nil, err
		}

		// 添加比较结果到详情中
		details = append(details, &pb.DiffDetail{
			Field:   fieldNum,
			Changed: changed,
		})

		if changed {
			overallChanged = true
			// 如果启用快速失败且发现变更，立即返回
			if fastFail {
				break
			}
		}
	}

	return overallChanged, details, nil
}

// compareField 比较单个字段
func (l *DiffUIAgentComponentLogic) compareField(
	in *pb.DiffUIAgentComponentReq,
	origin *model.UiAgentComponent,
	field DiffField,
) (bool, error) {
	switch field {
	case DiffFieldName:
		return l.compareName(in, origin), nil
	case DiffFieldDescription:
		return l.compareDescription(in, origin), nil
	case DiffFieldTags:
		return l.compareTags(in, origin)
	case DiffFieldApplicationID:
		return l.compareApplicationID(in, origin), nil
	case DiffFieldMode:
		return l.compareMode(in, origin), nil
	case DiffFieldAgentModeSteps:
		return l.compareAgentModeSteps(in, origin)
	case DiffFieldStepModeSteps:
		return l.compareStepModeSteps(in, origin)
	case DiffFieldExpectation:
		return l.compareExpectation(in, origin)
	case DiffFieldVariables:
		return l.compareVariables(in, origin)
	case DiffFieldForegroundCheck:
		return l.compareForegroundCheck(in, origin), nil
	default:
		return false, errorx.Errorf(errorx.ProhibitedBehavior, "unsupported diff field: %d", field)
	}
}

// compareName 比较组件名称
func (l *DiffUIAgentComponentLogic) compareName(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) bool {
	return in.GetName() != origin.Name
}

// compareDescription 比较组件描述
func (l *DiffUIAgentComponentLogic) compareDescription(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) bool {
	newDesc := in.GetDescription()
	originDesc := ""
	if origin.Description.Valid {
		originDesc = origin.Description.String
	}
	return newDesc != originDesc
}

// compareApplicationID 比较应用配置ID
func (l *DiffUIAgentComponentLogic) compareApplicationID(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) bool {
	return in.GetApplicationId() != origin.ApplicationId
}

// compareMode 比较模式
func (l *DiffUIAgentComponentLogic) compareMode(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) bool {
	return int64(in.GetMode()) != origin.Mode
}

// compareForegroundCheck 比较是否检查App在前台
func (l *DiffUIAgentComponentLogic) compareForegroundCheck(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) bool {
	return in.GetForegroundCheck() != (origin.ForegroundCheck == 1)
}

// compareTags 比较标签列表（顺序不敏感）
func (l *DiffUIAgentComponentLogic) compareTags(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
	newTags := in.GetTags()
	var originTags []string

	if origin.Tags.Valid && origin.Tags.String != "" {
		if err := jsonx.UnmarshalFromString(origin.Tags.String, &originTags); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin tags: %v", err)
		}
	}

	// 标签比较不考虑顺序，所以需要排序后比较
	return !l.compareStringSlicesIgnoreOrder(newTags, originTags), nil
}

// compareStringSlicesIgnoreOrder 比较两个字符串切片是否相同（忽略顺序）
func (l *DiffUIAgentComponentLogic) compareStringSlicesIgnoreOrder(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	// 创建副本并排序
	aCopy := make([]string, len(a))
	bCopy := make([]string, len(b))
	copy(aCopy, a)
	copy(bCopy, b)

	sort.Strings(aCopy)
	sort.Strings(bCopy)

	// 逐个比较
	for i := range aCopy {
		if aCopy[i] != bCopy[i] {
			return false
		}
	}

	return true
}

// compareAgentModeSteps 比较Agent模式步骤列表
func (l *DiffUIAgentComponentLogic) compareAgentModeSteps(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
	newSteps := in.GetAgentModeSteps()
	var originSteps []*commonpb.UIAgentComponentStep

	if origin.AgentModeSteps != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(origin.AgentModeSteps, &originSteps); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin agent mode steps: %v", err)
		}
	}

	return !l.compareSteps(newSteps, originSteps), nil
}

// compareStepModeSteps 比较Step模式步骤列表
func (l *DiffUIAgentComponentLogic) compareStepModeSteps(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
	newSteps := in.GetStepModeSteps()
	var originSteps []*commonpb.UIAgentComponentStep

	if origin.StepModeSteps != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(origin.StepModeSteps, &originSteps); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin step mode steps: %v", err)
		}
	}

	return !l.compareSteps(newSteps, originSteps), nil
}

// compareSteps 比较步骤列表是否相同
func (l *DiffUIAgentComponentLogic) compareSteps(newSteps, originSteps []*commonpb.UIAgentComponentStep) bool {
	// 比较步骤列表的长度
	if len(newSteps) != len(originSteps) {
		return false
	}

	// 逐个比较步骤
	for i, newStep := range newSteps {
		if i >= len(originSteps) {
			return false
		}

		originStep := originSteps[i]

		// 比较步骤内容
		if newStep.GetContent() != originStep.GetContent() {
			return false
		}

		// 比较等待时间
		if newStep.GetWaitingTime() != originStep.GetWaitingTime() {
			return false
		}

		// 比较期望结果
		if !l.compareStepExpectation(newStep.GetExpectation(), originStep.GetExpectation()) {
			return false
		}
	}

	return true
}

// compareStepExpectation 比较步骤期望是否相同
func (l *DiffUIAgentComponentLogic) compareStepExpectation(newExp, originExp *commonpb.UIAgentComponentExpectation) bool {
	// 处理nil情况
	if newExp == nil && originExp == nil {
		return true
	}
	if newExp == nil || originExp == nil {
		return false
	}

	// 比较文本和图片
	return newExp.GetText() == originExp.GetText() && newExp.GetImage() == originExp.GetImage()
}

// compareExpectation 比较期望结果
func (l *DiffUIAgentComponentLogic) compareExpectation(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
	newExp := in.GetExpectation()
	var originExpPtr *commonpb.UIAgentComponentExpectation

	if origin.Expectation.Valid && origin.Expectation.String != "" {
		var originExp commonpb.UIAgentComponentExpectation
		if err := protobuf.UnmarshalJSONFromString(origin.Expectation.String, &originExp); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin expectation: %v", err)
		}
		originExpPtr = &originExp
	}

	return !l.compareStepExpectation(newExp, originExpPtr), nil
}

// compareVariables 比较变量列表
func (l *DiffUIAgentComponentLogic) compareVariables(in *pb.DiffUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
	newVars := in.GetVariables()
	var originVars []*commonpb.GeneralConfigVar

	if origin.Variables != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(origin.Variables, &originVars); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin variables: %v", err)
		}
	}

	return !l.compareVariablesList(newVars, originVars), nil
}

// compareVariablesList 比较两个变量列表是否相同
func (l *DiffUIAgentComponentLogic) compareVariablesList(newVars, originVars []*commonpb.GeneralConfigVar) bool {
	// 比较变量列表的长度
	if len(newVars) != len(originVars) {
		return false
	}

	// 逐个比较变量
	for i, newVar := range newVars {
		if i >= len(originVars) {
			return false
		}

		originVar := originVars[i]

		// 比较键和值
		if newVar.GetKey() != originVar.GetKey() || newVar.GetValue() != originVar.GetValue() {
			return false
		}
	}

	return true
}
