package uiagentcomponentservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyUIAgentComponentLogic struct {
	*BaseLogic
}

func NewModifyUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyUIAgentComponentLogic {
	return &ModifyUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyUIAgentComponent 编辑UIAgent组件
func (l *ModifyUIAgentComponentLogic) ModifyUIAgentComponent(in *pb.ModifyUIAgentComponentReq) (
	out *pb.ModifyUIAgentComponentResp, err error,
) {
	var (
		projectID      = in.GetProjectId()
		categoryID     = in.GetCategoryId()
		componentID    = in.GetComponentId()
		applicationID  = in.GetApplicationId()
		mode           = in.GetMode()
		agentModeSteps = in.GetAgentModeSteps()
		stepModeSteps  = in.GetStepModeSteps()
		expectation    = in.GetExpectation()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, projectID, common.ConstCategoryTreeTypeUIAgentComponent, categoryID,
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the type of parent category[%s] does not support creation of sub category",
			c.CategoryType,
		)
	}

	// validate the component_id in req
	origin, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	)
	if err != nil {
		return nil, err
	}

	var app *model.ApplicationConfiguration
	// validate the application_id in req
	if app, err = model.CheckApplicationConfigurationByConfigID(
		l.ctx, l.svcCtx.ApplicationConfigModel, projectID, applicationID,
	); err != nil {
		return nil, err
	}

	if mode == commonpb.UIAgentMode_UIAgentMode_AGENT && len(agentModeSteps) == 0 {
		return nil, errorx.Err(errorx.ProhibitedBehavior, "the agent mode steps cannot be empty when the mode is agent")
	} else if mode == commonpb.UIAgentMode_UIAgentMode_STEP && len(stepModeSteps) == 0 {
		return nil, errorx.Err(errorx.ProhibitedBehavior, "the step mode steps cannot be empty when the mode is step")
	}

	// get images from step_mode_steps
	images, err := l.getImagesFromSteps(projectID, stepModeSteps)
	if err != nil {
		return nil, err
	}
	if expectation != nil && len(expectation.GetImage()) != 0 {
		image, err := l.getImageByImageID(projectID, expectation.GetImage())
		if err != nil {
			return nil, err
		}

		images = append(images, image)
	}

	// get variables from steps, include agent_mode_steps, step_mode_steps and expectation
	variableOfUsed, err := getVariablesFromSteps(agentModeSteps)
	if err != nil {
		return nil, errorx.Err(errorx.ProhibitedBehavior, err.Error())
	}
	vars, err := getVariablesFromSteps(stepModeSteps)
	if err != nil {
		return nil, errorx.Err(errorx.ProhibitedBehavior, err.Error())
	}
	variableOfUsed.InPlaceUnion(vars)
	if expectation != nil && len(expectation.GetText()) != 0 {
		vars, err = getVariablesFromContent(expectation.GetText())
		if err != nil {
			return nil, errorx.Err(errorx.ProhibitedBehavior, err.Error())
		}

		variableOfUsed.InPlaceUnion(vars)
	}
	variableOfDefined, err := getVariablesFromPairs(in.GetVariables())
	if err != nil {
		return nil, errorx.Err(errorx.ProhibitedBehavior, err.Error())
	}
	if s := variableOfUsed.Difference(variableOfDefined); s.Size() > 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"%d variables are used but not defined, variables: %s", s.Size(), s.String(),
		)
	}

	var component *model.UiAgentComponent
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, projectID, componentID)
	fn := func() error {
		component, err = l.modify(in, origin, app, images)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyUIAgentComponentResp{
		Component: &pb.UIAgentComponent{},
	}
	if err = utils.Copy(out.Component, component, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui agent component to response, component: %s, error: %+v",
			jsonx.MarshalIgnoreError(component), err,
		)
	}

	return out, nil
}

func (l *ModifyUIAgentComponentLogic) modify(
	in *pb.ModifyUIAgentComponentReq,
	origin *model.UiAgentComponent,
	app *model.ApplicationConfiguration,
	images []*model.UiAgentImage,
) (*model.UiAgentComponent, error) {
	var (
		projectID    = in.GetProjectId()
		description  = in.GetDescription()
		maintainedBy = in.GetMaintainedBy()
		latestResult = origin.LatestResult

		tags, expectation sql.NullString
	)

	// 检测执行相关数据是否发生变更
	hasChanged, err := l.hasExecutionDataChanged(in, origin)
	if err != nil {
		return nil, err
	}
	if hasChanged {
		// 如果执行相关数据发生变更，则将验证状态重置为未执行
		l.Infof(
			"the execution data of ui agent component has changed, project_id: %s, component_id: %s, latest_result: %s->%s",
			projectID, in.GetComponentId(), commonpb.ExecutedResult(latestResult), commonpb.ExecutedResult_TER_INIT,
		)
		latestResult = int64(commonpb.ExecutedResult_TER_INIT)
	}

	if len(in.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(in.GetTags())
		tags.Valid = true
	}

	if in.GetExpectation() != nil && (in.GetExpectation().GetText() != "" || in.GetExpectation().GetImage() != "") {
		expectation.String = protobuf.MarshalJSONToStringIgnoreError(in.GetExpectation())
		expectation.Valid = true
	}

	if maintainedBy != "" {
		if user, err := l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of stability plan, project_id: %s, maintained_by: %s",
				projectID, maintainedBy,
			)
		}
	} else {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	now := time.Now()
	component := &model.UiAgentComponent{
		Id:          origin.Id,
		ProjectId:   origin.ProjectId,
		CategoryId:  in.GetCategoryId(),
		ComponentId: origin.ComponentId,
		Name:        in.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		State:            int64(in.GetState()),
		Tags:             tags,
		PlatformType:     app.PlatformType,
		ApplicationId:    app.ConfigId,
		Mode:             int64(in.GetMode()),
		AgentModeSteps:   protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetAgentModeSteps()),
		StepModeSteps:    protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetStepModeSteps()),
		Expectation:      expectation,
		Variables:        protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetVariables()),
		ForegroundCheck:  cast.ToInt64(in.GetForegroundCheck()),
		ReferenceId:      origin.ReferenceId,
		LatestExecutedAt: origin.LatestExecutedAt,
		LatestResult:     latestResult,
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: now,
	}

	// update ui agent component in a transaction
	if err := l.svcCtx.UIAgentComponentModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.UIAgentComponentModel.Update(context, session, component); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIAgentComponentModel.Table(), jsonx.MarshalIgnoreError(component), err,
				)
			}

			// create tags and tag references of ui agent component
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     component.ProjectId,
					ReferenceType: common.ConstReferenceTypeUIAgentComponent,
					ReferenceId:   component.ComponentId,
					Tags:          in.GetTags(),
				},
			); err != nil {
				return err
			}

			// update the application configuration reference of ui agent component
			if err := l.updateApplicationConfigRelationship(context, session, component, app.ConfigId); err != nil {
				return err
			}

			// update the image reference of ui agent component
			if err := l.updateImageRelationship(context, session, component, images); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return component, nil
}

type checkFunc func(*pb.ModifyUIAgentComponentReq, *model.UiAgentComponent) (bool, error)

// hasExecutionDataChanged 检测执行相关数据是否发生变更
func (l *ModifyUIAgentComponentLogic) hasExecutionDataChanged(
	in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent,
) (bool, error) {
	for _, fn := range []checkFunc{
		func(in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
			return in.GetApplicationId() != origin.ApplicationId, nil
		}, // 1. 检查「应用配置」是否变更
		func(in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
			return int64(in.GetMode()) != origin.Mode, nil
		}, // 2. 检查「模式」是否变更
		func(in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent) (bool, error) {
			return in.GetForegroundCheck() != (origin.ForegroundCheck == 1), nil
		}, // 3. 检查「是否检查App在前台」是否变更
		l.hasStepsChanged,       // 4. 检查「步骤列表」是否变更（根据模式选择对应的步骤列表）
		l.hasExpectationChanged, // 5. 检查「期望」是否变更
		l.hasVariablesChanged,   // 6. 检查「变量列表」是否变更
	} {
		changed, err := fn(in, origin)
		if err != nil {
			return false, err
		} else if changed {
			return true, nil
		}
	}

	return false, nil
}

// hasStepsChanged 检查步骤列表是否变更
func (l *ModifyUIAgentComponentLogic) hasStepsChanged(
	in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent,
) (bool, error) {
	var (
		stepsJSON string
		toSteps   []*commonpb.UIAgentComponentStep
	)
	switch in.GetMode() {
	case commonpb.UIAgentMode_UIAgentMode_AGENT:
		stepsJSON = origin.AgentModeSteps
		toSteps = in.GetAgentModeSteps()
	case commonpb.UIAgentMode_UIAgentMode_STEP:
		stepsJSON = origin.StepModeSteps
		toSteps = in.GetStepModeSteps()
	default:
		return false, errorx.Errorf(errorx.ProhibitedBehavior, "invalid mode: %v", in.GetMode())
	}

	var fromSteps []*commonpb.UIAgentComponentStep
	if stepsJSON != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(stepsJSON, &fromSteps); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin steps: %v", err)
		}
	}

	same := l.compareSteps(fromSteps, toSteps)
	return !same, nil
}

// compareSteps 比较步骤列表是否相同
func (l *ModifyUIAgentComponentLogic) compareSteps(
	fromSteps, toSteps []*commonpb.UIAgentComponentStep,
) bool {
	// 比较步骤列表的长度
	if len(toSteps) != len(fromSteps) {
		return false
	}

	// 逐个比较步骤
	for i, toStep := range toSteps {
		if i >= len(fromSteps) {
			return false
		}

		fromStep := fromSteps[i]

		// 比较步骤内容
		if toStep.GetContent() != fromStep.GetContent() {
			return false
		}

		// 比较等待时间
		if toStep.GetWaitingTime() != fromStep.GetWaitingTime() {
			return false
		}

		// 比较期望结果
		if !l.compareExpectation(fromStep.GetExpectation(), toStep.GetExpectation()) {
			return false
		}
	}

	return true
}

// hasExpectationChanged 检查期望是否变更
func (l *ModifyUIAgentComponentLogic) hasExpectationChanged(
	in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent,
) (bool, error) {
	var fromExpectation commonpb.UIAgentComponentExpectation
	if origin.Expectation.Valid && origin.Expectation.String != "" {
		if err := protobuf.UnmarshalJSONFromString(origin.Expectation.String, &fromExpectation); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin expectation: %v", err)
		}
	}

	same := l.compareExpectation(&fromExpectation, in.GetExpectation())
	return !same, nil
}

// compareExpectation 比较两个期望是否相同
func (l *ModifyUIAgentComponentLogic) compareExpectation(
	fromExpectation, toExpectation *commonpb.UIAgentComponentExpectation,
) bool {
	// 处理nil情况
	if fromExpectation == nil && toExpectation == nil {
		return true
	}
	if fromExpectation == nil || toExpectation == nil {
		return false
	}

	// 比较文本和图片
	return toExpectation.GetText() == fromExpectation.GetText() && toExpectation.GetImage() == fromExpectation.GetImage()
}

// hasVariablesChanged 检查变量列表是否变更
func (l *ModifyUIAgentComponentLogic) hasVariablesChanged(
	in *pb.ModifyUIAgentComponentReq, origin *model.UiAgentComponent,
) (bool, error) {
	var fromVariables []*commonpb.GeneralConfigVar
	if origin.Variables != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(origin.Variables, &fromVariables); err != nil {
			return false, errorx.Errorf(errorx.SerializationError, "failed to unmarshal origin variables: %v", err)
		}
	}

	same := l.compareVariables(fromVariables, in.GetVariables())
	return !same, nil
}

// compareVariables 比较两个变量列表是否相同
func (l *ModifyUIAgentComponentLogic) compareVariables(
	fromVariables, toVariables []*commonpb.GeneralConfigVar,
) bool {
	// 比较变量列表的长度
	if len(toVariables) != len(fromVariables) {
		return false
	}

	// 逐个比较变量
	for i, toVar := range toVariables {
		if i >= len(fromVariables) {
			return false
		}

		fromVar := fromVariables[i]

		// 比较键和值
		if toVar.GetKey() != fromVar.GetKey() || toVar.GetValue() != fromVar.GetValue() {
			return false
		}
	}

	return true
}
