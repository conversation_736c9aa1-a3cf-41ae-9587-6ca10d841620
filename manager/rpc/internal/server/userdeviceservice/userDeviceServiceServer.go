// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	userdeviceservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/userdeviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UserDeviceServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedUserDeviceServiceServer
}

func NewUserDeviceServiceServer(svcCtx *svc.ServiceContext) *UserDeviceServiceServer {
	return &UserDeviceServiceServer{
		svcCtx: svcCtx,
	}
}

// GetRemoteAndroidSerial 获取远程Android设备的设备编号
func (s *UserDeviceServiceServer) GetRemoteAndroidSerial(ctx context.Context, in *pb.GetRemoteAndroidSerialReq) (*pb.GetRemoteAndroidSerialResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := userdeviceservicelogic.NewGetRemoteAndroidSerialLogic(ctx, s.svcCtx)

	return l.GetRemoteAndroidSerial(in)
}
