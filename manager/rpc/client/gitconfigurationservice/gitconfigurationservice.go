// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package gitconfigurationservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type (
	AcquireProjectDeviceByConditionReq          = pb.AcquireProjectDeviceByConditionReq
	AcquireProjectDeviceByConditionResp         = pb.AcquireProjectDeviceByConditionResp
	AcquireProjectDeviceByUDIDReq               = pb.AcquireProjectDeviceByUDIDReq
	AcquireProjectDeviceByUDIDResp              = pb.AcquireProjectDeviceByUDIDResp
	AcquireProjectDeviceReq                     = pb.AcquireProjectDeviceReq
	AcquireProjectDeviceResp                    = pb.AcquireProjectDeviceResp
	AddApiCaseToApiSuiteReq                     = pb.AddApiCaseToApiSuiteReq
	AddApiCaseToApiSuiteResp                    = pb.AddApiCaseToApiSuiteResp
	AddCaseToApiSuiteReq                        = pb.AddCaseToApiSuiteReq
	AddCaseToApiSuiteResp                       = pb.AddCaseToApiSuiteResp
	AddCaseToUIPlanReq                          = pb.AddCaseToUIPlanReq
	AddCaseToUIPlanResp                         = pb.AddCaseToUIPlanResp
	AddSuiteToApiPlanReq                        = pb.AddSuiteToApiPlanReq
	AddSuiteToApiPlanResp                       = pb.AddSuiteToApiPlanResp
	AdvancedSearchSuiteNotInApiPlanReq          = pb.AdvancedSearchSuiteNotInApiPlanReq
	AdvancedSearchSuiteNotInApiPlanResp         = pb.AdvancedSearchSuiteNotInApiPlanResp
	ApiExecutionData                            = pb.ApiExecutionData
	ApiExecutionData_ChildData                  = pb.ApiExecutionData_ChildData
	ApiExecutionData_ErrorData                  = pb.ApiExecutionData_ErrorData
	ApiExecutionData_ServiceCasesData           = pb.ApiExecutionData_ServiceCasesData
	ApproveReviewRecordReq                      = pb.ApproveReviewRecordReq
	ApproveReviewRecordResp                     = pb.ApproveReviewRecordResp
	BatchDeleteCaseFailLogReq                   = pb.BatchDeleteCaseFailLogReq
	BatchDeleteCaseFailLogResp                  = pb.BatchDeleteCaseFailLogResp
	CheckLikePlanReq                            = pb.CheckLikePlanReq
	CheckLikePlanResp                           = pb.CheckLikePlanResp
	CheckPlanStateReq                           = pb.CheckPlanStateReq
	CheckPlanStateResp                          = pb.CheckPlanStateResp
	CreateAccountConfigurationReq               = pb.CreateAccountConfigurationReq
	CreateAccountConfigurationResp              = pb.CreateAccountConfigurationResp
	CreateApiCaseReq                            = pb.CreateApiCaseReq
	CreateApiCaseResp                           = pb.CreateApiCaseResp
	CreateApiPlanReq                            = pb.CreateApiPlanReq
	CreateApiPlanResp                           = pb.CreateApiPlanResp
	CreateApiSuiteReq                           = pb.CreateApiSuiteReq
	CreateApiSuiteResp                          = pb.CreateApiSuiteResp
	CreateApplicationConfigurationReq           = pb.CreateApplicationConfigurationReq
	CreateApplicationConfigurationResp          = pb.CreateApplicationConfigurationResp
	CreateCategoryReq                           = pb.CreateCategoryReq
	CreateCategoryResp                          = pb.CreateCategoryResp
	CreateComponentGroupReq                     = pb.CreateComponentGroupReq
	CreateComponentGroupResp                    = pb.CreateComponentGroupResp
	CreateGeneralConfigurationReq               = pb.CreateGeneralConfigurationReq
	CreateGeneralConfigurationResp              = pb.CreateGeneralConfigurationResp
	CreateGitConfigurationReq                   = pb.CreateGitConfigurationReq
	CreateGitConfigurationResp                  = pb.CreateGitConfigurationResp
	CreateInterfaceCaseReq                      = pb.CreateInterfaceCaseReq
	CreateInterfaceCaseResp                     = pb.CreateInterfaceCaseResp
	CreateInterfaceConfigReq                    = pb.CreateInterfaceConfigReq
	CreateInterfaceConfigResp                   = pb.CreateInterfaceConfigResp
	CreateInterfaceDocumentReq                  = pb.CreateInterfaceDocumentReq
	CreateInterfaceDocumentResp                 = pb.CreateInterfaceDocumentResp
	CreateInterfaceSchemaReq                    = pb.CreateInterfaceSchemaReq
	CreateInterfaceSchemaResp                   = pb.CreateInterfaceSchemaResp
	CreateOrModifyDataProcessingFunctionReq     = pb.CreateOrModifyDataProcessingFunctionReq
	CreateOrModifyDataProcessingFunctionResp    = pb.CreateOrModifyDataProcessingFunctionResp
	CreateOrModifyFailedCaseReq                 = pb.CreateOrModifyFailedCaseReq
	CreateOrModifyFailedCaseResp                = pb.CreateOrModifyFailedCaseResp
	CreatePerfCaseV2Req                         = pb.CreatePerfCaseV2Req
	CreatePerfCaseV2Resp                        = pb.CreatePerfCaseV2Resp
	CreatePerfLarkMemberReq                     = pb.CreatePerfLarkMemberReq
	CreatePerfLarkMemberResp                    = pb.CreatePerfLarkMemberResp
	CreatePerfPlanReq                           = pb.CreatePerfPlanReq
	CreatePerfPlanResp                          = pb.CreatePerfPlanResp
	CreatePerfPlanV2Req                         = pb.CreatePerfPlanV2Req
	CreatePerfPlanV2Resp                        = pb.CreatePerfPlanV2Resp
	CreatePerfStopRuleReq                       = pb.CreatePerfStopRuleReq
	CreatePerfStopRuleResp                      = pb.CreatePerfStopRuleResp
	CreatePlanNotifyReq                         = pb.CreatePlanNotifyReq
	CreatePlanNotifyResp                        = pb.CreatePlanNotifyResp
	CreateProjectReq                            = pb.CreateProjectReq
	CreateProjectResp                           = pb.CreateProjectResp
	CreatePromptConfigurationReq                = pb.CreatePromptConfigurationReq
	CreatePromptConfigurationResp               = pb.CreatePromptConfigurationResp
	CreateProtobufConfigurationReq              = pb.CreateProtobufConfigurationReq
	CreateProtobufConfigurationResp             = pb.CreateProtobufConfigurationResp
	CreateReviewRecordReq                       = pb.CreateReviewRecordReq
	CreateReviewRecordResp                      = pb.CreateReviewRecordResp
	CreateStabilityPlanReq                      = pb.CreateStabilityPlanReq
	CreateStabilityPlanResp                     = pb.CreateStabilityPlanResp
	CreateTagReq                                = pb.CreateTagReq
	CreateTagResp                               = pb.CreateTagResp
	CreateUIAgentComponentReq                   = pb.CreateUIAgentComponentReq
	CreateUIAgentComponentResp                  = pb.CreateUIAgentComponentResp
	CreateUiPlanReq                             = pb.CreateUiPlanReq
	CreateUiPlanResp                            = pb.CreateUiPlanResp
	DeleteCaseFailLog                           = pb.DeleteCaseFailLog
	DeleteDisabledProjectDeviceReq              = pb.DeleteDisabledProjectDeviceReq
	DeleteDisabledProjectDeviceResp             = pb.DeleteDisabledProjectDeviceResp
	DeleteFailLogCaseReq                        = pb.DeleteFailLogCaseReq
	DeleteFailLogCaseResp                       = pb.DeleteFailLogCaseResp
	DeleteLarkChatReq                           = pb.DeleteLarkChatReq
	DeleteLarkChatResp                          = pb.DeleteLarkChatResp
	DeletePerfLarkChatReq                       = pb.DeletePerfLarkChatReq
	DeletePerfLarkChatResp                      = pb.DeletePerfLarkChatResp
	DiffUIAgentComponentReq                     = pb.DiffUIAgentComponentReq
	DiffUIAgentComponentResp                    = pb.DiffUIAgentComponentResp
	GetApiExecutionDataReq                      = pb.GetApiExecutionDataReq
	GetApiExecutionDataStreamReq                = pb.GetApiExecutionDataStreamReq
	GetApiExecutionDataStreamReq_IdVersion      = pb.GetApiExecutionDataStreamReq_IdVersion
	GetApiExecutionDataStructureReq             = pb.GetApiExecutionDataStructureReq
	GetCaseTreeOfNotAddedToUIPlanReq            = pb.GetCaseTreeOfNotAddedToUIPlanReq
	GetCaseTreeOfNotAddedToUIPlanResp           = pb.GetCaseTreeOfNotAddedToUIPlanResp
	GetCaseTreeOfUIPlanReq                      = pb.GetCaseTreeOfUIPlanReq
	GetCaseTreeOfUIPlanResp                     = pb.GetCaseTreeOfUIPlanResp
	GetCategoryTreeReq                          = pb.GetCategoryTreeReq
	GetCategoryTreeResp                         = pb.GetCategoryTreeResp
	GetInterfaceCoverageDataReq                 = pb.GetInterfaceCoverageDataReq
	GetInterfaceCoverageDataResp                = pb.GetInterfaceCoverageDataResp
	GetInterfaceCoverageTeamsReq                = pb.GetInterfaceCoverageTeamsReq
	GetInterfaceCoverageTeamsResp               = pb.GetInterfaceCoverageTeamsResp
	GetPlanNotifyReq                            = pb.GetPlanNotifyReq
	GetPlanNotifyResp                           = pb.GetPlanNotifyResp
	GetProjectDeviceReq                         = pb.GetProjectDeviceReq
	GetProjectDeviceResp                        = pb.GetProjectDeviceResp
	GetRemoteAndroidSerialReq                   = pb.GetRemoteAndroidSerialReq
	GetRemoteAndroidSerialResp                  = pb.GetRemoteAndroidSerialResp
	GetSlaThresholdReq                          = pb.GetSlaThresholdReq
	GetSlaThresholdResp                         = pb.GetSlaThresholdResp
	HandleLikePlanReq                           = pb.HandleLikePlanReq
	HandleLikePlanResp                          = pb.HandleLikePlanResp
	ListDisableCaseInUIPlanReq                  = pb.ListDisableCaseInUIPlanReq
	ListDisableCaseInUIPlanResp                 = pb.ListDisableCaseInUIPlanResp
	LocalImportInterfaceDefinitionReq           = pb.LocalImportInterfaceDefinitionReq
	LocalImportInterfaceDefinitionReq_Target    = pb.LocalImportInterfaceDefinitionReq_Target
	LocalImportInterfaceDefinitionResp          = pb.LocalImportInterfaceDefinitionResp
	MaintainApiCaseReq                          = pb.MaintainApiCaseReq
	MaintainApiCaseResp                         = pb.MaintainApiCaseResp
	MaintainInterfaceCaseReq                    = pb.MaintainInterfaceCaseReq
	MaintainInterfaceCaseResp                   = pb.MaintainInterfaceCaseResp
	MockInterfaceDocumentReq                    = pb.MockInterfaceDocumentReq
	MockInterfaceDocumentResp                   = pb.MockInterfaceDocumentResp
	ModifyAccountConfigurationReq               = pb.ModifyAccountConfigurationReq
	ModifyAccountConfigurationResp              = pb.ModifyAccountConfigurationResp
	ModifyApiCaseReq                            = pb.ModifyApiCaseReq
	ModifyApiCaseResp                           = pb.ModifyApiCaseResp
	ModifyApiPlanReferenceStateReq              = pb.ModifyApiPlanReferenceStateReq
	ModifyApiPlanReferenceStateResp             = pb.ModifyApiPlanReferenceStateResp
	ModifyApiPlanReq                            = pb.ModifyApiPlanReq
	ModifyApiPlanResp                           = pb.ModifyApiPlanResp
	ModifyApiSuiteReferenceStateReq             = pb.ModifyApiSuiteReferenceStateReq
	ModifyApiSuiteReferenceStateResp            = pb.ModifyApiSuiteReferenceStateResp
	ModifyApiSuiteReq                           = pb.ModifyApiSuiteReq
	ModifyApiSuiteResp                          = pb.ModifyApiSuiteResp
	ModifyApplicationConfigurationReq           = pb.ModifyApplicationConfigurationReq
	ModifyApplicationConfigurationResp          = pb.ModifyApplicationConfigurationResp
	ModifyCategoryReq                           = pb.ModifyCategoryReq
	ModifyCategoryResp                          = pb.ModifyCategoryResp
	ModifyComponentGroupReq                     = pb.ModifyComponentGroupReq
	ModifyComponentGroupResp                    = pb.ModifyComponentGroupResp
	ModifyGeneralConfigurationReq               = pb.ModifyGeneralConfigurationReq
	ModifyGeneralConfigurationResp              = pb.ModifyGeneralConfigurationResp
	ModifyGitConfigurationReq                   = pb.ModifyGitConfigurationReq
	ModifyGitConfigurationResp                  = pb.ModifyGitConfigurationResp
	ModifyInterfaceCaseReq                      = pb.ModifyInterfaceCaseReq
	ModifyInterfaceCaseResp                     = pb.ModifyInterfaceCaseResp
	ModifyInterfaceConfigReq                    = pb.ModifyInterfaceConfigReq
	ModifyInterfaceConfigResp                   = pb.ModifyInterfaceConfigResp
	ModifyInterfaceDocumentReferenceStateReq    = pb.ModifyInterfaceDocumentReferenceStateReq
	ModifyInterfaceDocumentReferenceStateResp   = pb.ModifyInterfaceDocumentReferenceStateResp
	ModifyInterfaceDocumentReq                  = pb.ModifyInterfaceDocumentReq
	ModifyInterfaceDocumentResp                 = pb.ModifyInterfaceDocumentResp
	ModifyInterfaceSchemaReq                    = pb.ModifyInterfaceSchemaReq
	ModifyInterfaceSchemaResp                   = pb.ModifyInterfaceSchemaResp
	ModifyLarkChatReq                           = pb.ModifyLarkChatReq
	ModifyLarkChatResp                          = pb.ModifyLarkChatResp
	ModifyPerfCaseV2Req                         = pb.ModifyPerfCaseV2Req
	ModifyPerfCaseV2Resp                        = pb.ModifyPerfCaseV2Resp
	ModifyPerfLarkChatReq                       = pb.ModifyPerfLarkChatReq
	ModifyPerfLarkChatResp                      = pb.ModifyPerfLarkChatResp
	ModifyPerfPlanReq                           = pb.ModifyPerfPlanReq
	ModifyPerfPlanResp                          = pb.ModifyPerfPlanResp
	ModifyPerfPlanV2Req                         = pb.ModifyPerfPlanV2Req
	ModifyPerfPlanV2Resp                        = pb.ModifyPerfPlanV2Resp
	ModifyPerfStopRuleReq                       = pb.ModifyPerfStopRuleReq
	ModifyPerfStopRuleResp                      = pb.ModifyPerfStopRuleResp
	ModifyPlanNotifyReq                         = pb.ModifyPlanNotifyReq
	ModifyPlanNotifyResp                        = pb.ModifyPlanNotifyResp
	ModifyProjectCoverageFunctionReq            = pb.ModifyProjectCoverageFunctionReq
	ModifyProjectCoverageFunctionResp           = pb.ModifyProjectCoverageFunctionResp
	ModifyProjectDeviceReq                      = pb.ModifyProjectDeviceReq
	ModifyProjectDeviceResp                     = pb.ModifyProjectDeviceResp
	ModifyProjectReq                            = pb.ModifyProjectReq
	ModifyProjectResp                           = pb.ModifyProjectResp
	ModifyProjectReviewFunctionReq              = pb.ModifyProjectReviewFunctionReq
	ModifyProjectReviewFunctionResp             = pb.ModifyProjectReviewFunctionResp
	ModifyPromptConfigurationReq                = pb.ModifyPromptConfigurationReq
	ModifyPromptConfigurationResp               = pb.ModifyPromptConfigurationResp
	ModifyProtobufConfigurationReq              = pb.ModifyProtobufConfigurationReq
	ModifyProtobufConfigurationResp             = pb.ModifyProtobufConfigurationResp
	ModifyReviewRecordReq                       = pb.ModifyReviewRecordReq
	ModifyReviewRecordResp                      = pb.ModifyReviewRecordResp
	ModifySlaThresholdReq                       = pb.ModifySlaThresholdReq
	ModifySlaThresholdResp                      = pb.ModifySlaThresholdResp
	ModifyStabilityPlanReq                      = pb.ModifyStabilityPlanReq
	ModifyStabilityPlanResp                     = pb.ModifyStabilityPlanResp
	ModifyTagReq                                = pb.ModifyTagReq
	ModifyTagResp                               = pb.ModifyTagResp
	ModifyUIAgentComponentReq                   = pb.ModifyUIAgentComponentReq
	ModifyUIAgentComponentResp                  = pb.ModifyUIAgentComponentResp
	ModifyUiPlanReq                             = pb.ModifyUiPlanReq
	ModifyUiPlanResp                            = pb.ModifyUiPlanResp
	MoveCategoryTreeReq                         = pb.MoveCategoryTreeReq
	MoveCategoryTreeResp                        = pb.MoveCategoryTreeResp
	PublishApiCaseReq                           = pb.PublishApiCaseReq
	PublishApiCaseResp                          = pb.PublishApiCaseResp
	PublishInterfaceCaseReq                     = pb.PublishInterfaceCaseReq
	PublishInterfaceCaseResp                    = pb.PublishInterfaceCaseResp
	ReleaseProjectDeviceReq                     = pb.ReleaseProjectDeviceReq
	ReleaseProjectDeviceResp                    = pb.ReleaseProjectDeviceResp
	RemoveAccountConfigurationReq               = pb.RemoveAccountConfigurationReq
	RemoveAccountConfigurationResp              = pb.RemoveAccountConfigurationResp
	RemoveApiCaseFromApiSuiteReq                = pb.RemoveApiCaseFromApiSuiteReq
	RemoveApiCaseFromApiSuiteResp               = pb.RemoveApiCaseFromApiSuiteResp
	RemoveApiCaseReq                            = pb.RemoveApiCaseReq
	RemoveApiCaseResp                           = pb.RemoveApiCaseResp
	RemoveApiPlanReq                            = pb.RemoveApiPlanReq
	RemoveApiPlanResp                           = pb.RemoveApiPlanResp
	RemoveApiSuiteReq                           = pb.RemoveApiSuiteReq
	RemoveApiSuiteResp                          = pb.RemoveApiSuiteResp
	RemoveApplicationConfigurationReq           = pb.RemoveApplicationConfigurationReq
	RemoveApplicationConfigurationResp          = pb.RemoveApplicationConfigurationResp
	RemoveCaseFromApiSuiteReq                   = pb.RemoveCaseFromApiSuiteReq
	RemoveCaseFromApiSuiteResp                  = pb.RemoveCaseFromApiSuiteResp
	RemoveCaseFromUIPlanReq                     = pb.RemoveCaseFromUIPlanReq
	RemoveCaseFromUIPlanResp                    = pb.RemoveCaseFromUIPlanResp
	RemoveCategoryReq                           = pb.RemoveCategoryReq
	RemoveCategoryResp                          = pb.RemoveCategoryResp
	RemoveComponentGroupReq                     = pb.RemoveComponentGroupReq
	RemoveComponentGroupResp                    = pb.RemoveComponentGroupResp
	RemoveDataProcessingFunctionReq             = pb.RemoveDataProcessingFunctionReq
	RemoveDataProcessingFunctionResp            = pb.RemoveDataProcessingFunctionResp
	RemoveGeneralConfigurationReq               = pb.RemoveGeneralConfigurationReq
	RemoveGeneralConfigurationResp              = pb.RemoveGeneralConfigurationResp
	RemoveGitConfigurationReq                   = pb.RemoveGitConfigurationReq
	RemoveGitConfigurationResp                  = pb.RemoveGitConfigurationResp
	RemoveInterfaceCaseReq                      = pb.RemoveInterfaceCaseReq
	RemoveInterfaceCaseResp                     = pb.RemoveInterfaceCaseResp
	RemoveInterfaceConfigReq                    = pb.RemoveInterfaceConfigReq
	RemoveInterfaceConfigResp                   = pb.RemoveInterfaceConfigResp
	RemoveInterfaceDocumentReq                  = pb.RemoveInterfaceDocumentReq
	RemoveInterfaceDocumentResp                 = pb.RemoveInterfaceDocumentResp
	RemoveInterfaceSchemaReq                    = pb.RemoveInterfaceSchemaReq
	RemoveInterfaceSchemaResp                   = pb.RemoveInterfaceSchemaResp
	RemovePerfCaseReq                           = pb.RemovePerfCaseReq
	RemovePerfCaseResp                          = pb.RemovePerfCaseResp
	RemovePerfCaseV2Req                         = pb.RemovePerfCaseV2Req
	RemovePerfCaseV2Resp                        = pb.RemovePerfCaseV2Resp
	RemovePerfDataReq                           = pb.RemovePerfDataReq
	RemovePerfDataResp                          = pb.RemovePerfDataResp
	RemovePerfLarkMemberReq                     = pb.RemovePerfLarkMemberReq
	RemovePerfLarkMemberResp                    = pb.RemovePerfLarkMemberResp
	RemovePerfPlanReq                           = pb.RemovePerfPlanReq
	RemovePerfPlanResp                          = pb.RemovePerfPlanResp
	RemovePerfPlanV2Req                         = pb.RemovePerfPlanV2Req
	RemovePerfPlanV2Resp                        = pb.RemovePerfPlanV2Resp
	RemovePerfStopRuleReq                       = pb.RemovePerfStopRuleReq
	RemovePerfStopRuleResp                      = pb.RemovePerfStopRuleResp
	RemovePlanNotifyReq                         = pb.RemovePlanNotifyReq
	RemovePlanNotifyResp                        = pb.RemovePlanNotifyResp
	RemoveProjectReq                            = pb.RemoveProjectReq
	RemoveProjectResp                           = pb.RemoveProjectResp
	RemovePromptConfigurationReq                = pb.RemovePromptConfigurationReq
	RemovePromptConfigurationResp               = pb.RemovePromptConfigurationResp
	RemoveProtobufConfigurationReq              = pb.RemoveProtobufConfigurationReq
	RemoveProtobufConfigurationResp             = pb.RemoveProtobufConfigurationResp
	RemoveStabilityPlanReq                      = pb.RemoveStabilityPlanReq
	RemoveStabilityPlanResp                     = pb.RemoveStabilityPlanResp
	RemoveSuiteFromApiPlanReq                   = pb.RemoveSuiteFromApiPlanReq
	RemoveSuiteFromApiPlanResp                  = pb.RemoveSuiteFromApiPlanResp
	RemoveTagReq                                = pb.RemoveTagReq
	RemoveTagResp                               = pb.RemoveTagResp
	RemoveUIAgentComponentReq                   = pb.RemoveUIAgentComponentReq
	RemoveUIAgentComponentResp                  = pb.RemoveUIAgentComponentResp
	RemoveUiPlanReq                             = pb.RemoveUiPlanReq
	RemoveUiPlanResp                            = pb.RemoveUiPlanResp
	RevokeReviewRecordReq                       = pb.RevokeReviewRecordReq
	RevokeReviewRecordResp                      = pb.RevokeReviewRecordResp
	SearchAccountConfigurationReq               = pb.SearchAccountConfigurationReq
	SearchAccountConfigurationResp              = pb.SearchAccountConfigurationResp
	SearchAdvancedSearchConditionReq            = pb.SearchAdvancedSearchConditionReq
	SearchAdvancedSearchConditionResp           = pb.SearchAdvancedSearchConditionResp
	SearchAdvancedSearchFieldReq                = pb.SearchAdvancedSearchFieldReq
	SearchAdvancedSearchFieldResp               = pb.SearchAdvancedSearchFieldResp
	SearchApiCaseInApiSuiteReq                  = pb.SearchApiCaseInApiSuiteReq
	SearchApiCaseInApiSuiteResp                 = pb.SearchApiCaseInApiSuiteResp
	SearchApiCaseNotInApiSuiteReq               = pb.SearchApiCaseNotInApiSuiteReq
	SearchApiCaseNotInApiSuiteResp              = pb.SearchApiCaseNotInApiSuiteResp
	SearchApiCaseReferenceReq                   = pb.SearchApiCaseReferenceReq
	SearchApiCaseReferenceResp                  = pb.SearchApiCaseReferenceResp
	SearchApiCaseReq                            = pb.SearchApiCaseReq
	SearchApiCaseResp                           = pb.SearchApiCaseResp
	SearchApiPlanReq                            = pb.SearchApiPlanReq
	SearchApiPlanResp                           = pb.SearchApiPlanResp
	SearchApiSuiteReferenceReq                  = pb.SearchApiSuiteReferenceReq
	SearchApiSuiteReferenceResp                 = pb.SearchApiSuiteReferenceResp
	SearchApiSuiteReq                           = pb.SearchApiSuiteReq
	SearchApiSuiteResp                          = pb.SearchApiSuiteResp
	SearchApplicationConfigurationReferenceReq  = pb.SearchApplicationConfigurationReferenceReq
	SearchApplicationConfigurationReferenceResp = pb.SearchApplicationConfigurationReferenceResp
	SearchApplicationConfigurationReq           = pb.SearchApplicationConfigurationReq
	SearchApplicationConfigurationResp          = pb.SearchApplicationConfigurationResp
	SearchCaseFailLogReq                        = pb.SearchCaseFailLogReq
	SearchCaseFailLogResp                       = pb.SearchCaseFailLogResp
	SearchCaseInApiPlanReq                      = pb.SearchCaseInApiPlanReq
	SearchCaseInApiPlanResp                     = pb.SearchCaseInApiPlanResp
	SearchCaseInApiSuiteReq                     = pb.SearchCaseInApiSuiteReq
	SearchCaseInApiSuiteResp                    = pb.SearchCaseInApiSuiteResp
	SearchCaseInPerfPlanReq                     = pb.SearchCaseInPerfPlanReq
	SearchCaseInPerfPlanResp                    = pb.SearchCaseInPerfPlanResp
	SearchCaseInPerfPlanV2Req                   = pb.SearchCaseInPerfPlanV2Req
	SearchCaseInPerfPlanV2Resp                  = pb.SearchCaseInPerfPlanV2Resp
	SearchCaseInUIPlanReq                       = pb.SearchCaseInUIPlanReq
	SearchCaseInUIPlanResp                      = pb.SearchCaseInUIPlanResp
	SearchCaseNotInUIPlanReq                    = pb.SearchCaseNotInUIPlanReq
	SearchCaseNotInUIPlanResp                   = pb.SearchCaseNotInUIPlanResp
	SearchCaseReq                               = pb.SearchCaseReq
	SearchCaseResp                              = pb.SearchCaseResp
	SearchCategoryReq                           = pb.SearchCategoryReq
	SearchCategoryResp                          = pb.SearchCategoryResp
	SearchComponentGroupReferenceReq            = pb.SearchComponentGroupReferenceReq
	SearchComponentGroupReferenceResp           = pb.SearchComponentGroupReferenceResp
	SearchComponentGroupReq                     = pb.SearchComponentGroupReq
	SearchComponentGroupResp                    = pb.SearchComponentGroupResp
	SearchDataProcessingFunctionReq             = pb.SearchDataProcessingFunctionReq
	SearchDataProcessingFunctionResp            = pb.SearchDataProcessingFunctionResp
	SearchGeneralConfigurationReq               = pb.SearchGeneralConfigurationReq
	SearchGeneralConfigurationResp              = pb.SearchGeneralConfigurationResp
	SearchGitConfigurationReq                   = pb.SearchGitConfigurationReq
	SearchGitConfigurationResp                  = pb.SearchGitConfigurationResp
	SearchInterfaceCaseReferenceReq             = pb.SearchInterfaceCaseReferenceReq
	SearchInterfaceCaseReferenceResp            = pb.SearchInterfaceCaseReferenceResp
	SearchInterfaceCaseReq                      = pb.SearchInterfaceCaseReq
	SearchInterfaceCaseResp                     = pb.SearchInterfaceCaseResp
	SearchInterfaceConfigReq                    = pb.SearchInterfaceConfigReq
	SearchInterfaceConfigResp                   = pb.SearchInterfaceConfigResp
	SearchInterfaceDocumentReferenceReq         = pb.SearchInterfaceDocumentReferenceReq
	SearchInterfaceDocumentReferenceResp        = pb.SearchInterfaceDocumentReferenceResp
	SearchInterfaceDocumentReq                  = pb.SearchInterfaceDocumentReq
	SearchInterfaceDocumentResp                 = pb.SearchInterfaceDocumentResp
	SearchInterfaceSchemaReq                    = pb.SearchInterfaceSchemaReq
	SearchInterfaceSchemaResp                   = pb.SearchInterfaceSchemaResp
	SearchLarkChatReq                           = pb.SearchLarkChatReq
	SearchLarkChatResp                          = pb.SearchLarkChatResp
	SearchPerfCaseV2Req                         = pb.SearchPerfCaseV2Req
	SearchPerfCaseV2Resp                        = pb.SearchPerfCaseV2Resp
	SearchPerfDataReq                           = pb.SearchPerfDataReq
	SearchPerfDataResp                          = pb.SearchPerfDataResp
	SearchPerfLarkChatReq                       = pb.SearchPerfLarkChatReq
	SearchPerfLarkChatResp                      = pb.SearchPerfLarkChatResp
	SearchPerfLarkMemberReq                     = pb.SearchPerfLarkMemberReq
	SearchPerfLarkMemberResp                    = pb.SearchPerfLarkMemberResp
	SearchPerfPlanReq                           = pb.SearchPerfPlanReq
	SearchPerfPlanResp                          = pb.SearchPerfPlanResp
	SearchPerfPlanV2Req                         = pb.SearchPerfPlanV2Req
	SearchPerfPlanV2Resp                        = pb.SearchPerfPlanV2Resp
	SearchPerfStopRuleReq                       = pb.SearchPerfStopRuleReq
	SearchPerfStopRuleResp                      = pb.SearchPerfStopRuleResp
	SearchPlanNotifyReq                         = pb.SearchPlanNotifyReq
	SearchPlanNotifyResp                        = pb.SearchPlanNotifyResp
	SearchProjectDeviceReferenceReq             = pb.SearchProjectDeviceReferenceReq
	SearchProjectDeviceReferenceResp            = pb.SearchProjectDeviceReferenceResp
	SearchProjectDeviceReq                      = pb.SearchProjectDeviceReq
	SearchProjectDeviceResp                     = pb.SearchProjectDeviceResp
	SearchProjectReq                            = pb.SearchProjectReq
	SearchProjectResp                           = pb.SearchProjectResp
	SearchProjectUserReq                        = pb.SearchProjectUserReq
	SearchProjectUserResp                       = pb.SearchProjectUserResp
	SearchPromptConfigurationReferenceReq       = pb.SearchPromptConfigurationReferenceReq
	SearchPromptConfigurationReferenceResp      = pb.SearchPromptConfigurationReferenceResp
	SearchPromptConfigurationReq                = pb.SearchPromptConfigurationReq
	SearchPromptConfigurationResp               = pb.SearchPromptConfigurationResp
	SearchProtobufConfigurationReq              = pb.SearchProtobufConfigurationReq
	SearchProtobufConfigurationResp             = pb.SearchProtobufConfigurationResp
	SearchProtobufInPerfPlanV2Req               = pb.SearchProtobufInPerfPlanV2Req
	SearchProtobufInPerfPlanV2Resp              = pb.SearchProtobufInPerfPlanV2Resp
	SearchReviewRecordReq                       = pb.SearchReviewRecordReq
	SearchReviewRecordResp                      = pb.SearchReviewRecordResp
	SearchRuleInPerfPlanV2Req                   = pb.SearchRuleInPerfPlanV2Req
	SearchRuleInPerfPlanV2Resp                  = pb.SearchRuleInPerfPlanV2Resp
	SearchServiceCaseNotInApiSuiteReq           = pb.SearchServiceCaseNotInApiSuiteReq
	SearchServiceCaseNotInApiSuiteResp          = pb.SearchServiceCaseNotInApiSuiteResp
	SearchSlaNotifierReq                        = pb.SearchSlaNotifierReq
	SearchSlaNotifierResp                       = pb.SearchSlaNotifierResp
	SearchStabilityPlanReq                      = pb.SearchStabilityPlanReq
	SearchStabilityPlanResp                     = pb.SearchStabilityPlanResp
	SearchSuiteInApiPlanReq                     = pb.SearchSuiteInApiPlanReq
	SearchSuiteInApiPlanResp                    = pb.SearchSuiteInApiPlanResp
	SearchSuiteNotInApiPlanReq                  = pb.SearchSuiteNotInApiPlanReq
	SearchSuiteNotInApiPlanResp                 = pb.SearchSuiteNotInApiPlanResp
	SearchTagReq                                = pb.SearchTagReq
	SearchTagResp                               = pb.SearchTagResp
	SearchUIAgentComponentReq                   = pb.SearchUIAgentComponentReq
	SearchUIAgentComponentResp                  = pb.SearchUIAgentComponentResp
	SearchUiPlanByProjectIdConfigIdReq          = pb.SearchUiPlanByProjectIdConfigIdReq
	SearchUiPlanByProjectIdConfigIdResp         = pb.SearchUiPlanByProjectIdConfigIdResp
	SearchUiPlanReq                             = pb.SearchUiPlanReq
	SearchUiPlanResp                            = pb.SearchUiPlanResp
	SearchUnassignedProjectDeviceReq            = pb.SearchUnassignedProjectDeviceReq
	SearchUnassignedProjectDeviceResp           = pb.SearchUnassignedProjectDeviceResp
	SyncGitConfigurationByWebhookReq            = pb.SyncGitConfigurationByWebhookReq
	SyncGitConfigurationByWebhookResp           = pb.SyncGitConfigurationByWebhookResp
	SyncGitConfigurationReq                     = pb.SyncGitConfigurationReq
	SyncGitConfigurationResp                    = pb.SyncGitConfigurationResp
	TestGitConfigurationReq                     = pb.TestGitConfigurationReq
	TestGitConfigurationResp                    = pb.TestGitConfigurationResp
	UpdateInterfaceCoverageDataReq              = pb.UpdateInterfaceCoverageDataReq
	UpdateInterfaceCoverageDataResp             = pb.UpdateInterfaceCoverageDataResp
	UpdateInterfaceDocumentTagsReq              = pb.UpdateInterfaceDocumentTagsReq
	UpdateInterfaceDocumentTagsResp             = pb.UpdateInterfaceDocumentTagsResp
	UpdateLarkChatReq                           = pb.UpdateLarkChatReq
	UpdateLarkChatResp                          = pb.UpdateLarkChatResp
	UpdatePerfLarkChatReq                       = pb.UpdatePerfLarkChatReq
	UpdatePerfLarkChatResp                      = pb.UpdatePerfLarkChatResp
	UpdatePerfPlanByCaseReq                     = pb.UpdatePerfPlanByCaseReq
	UpdatePerfPlanByCaseResp                    = pb.UpdatePerfPlanByCaseResp
	UpdatePerfPlanByChatIDReq                   = pb.UpdatePerfPlanByChatIDReq
	UpdatePerfPlanByChatIDResp                  = pb.UpdatePerfPlanByChatIDResp
	UpdateUIAgentComponentResultReq             = pb.UpdateUIAgentComponentResultReq
	UpdateUIAgentComponentResultResp            = pb.UpdateUIAgentComponentResultResp
	ViewAccountConfigurationReq                 = pb.ViewAccountConfigurationReq
	ViewAccountConfigurationResp                = pb.ViewAccountConfigurationResp
	ViewApiCaseReq                              = pb.ViewApiCaseReq
	ViewApiCaseResp                             = pb.ViewApiCaseResp
	ViewApiPlanReq                              = pb.ViewApiPlanReq
	ViewApiPlanResp                             = pb.ViewApiPlanResp
	ViewApiSuiteReq                             = pb.ViewApiSuiteReq
	ViewApiSuiteResp                            = pb.ViewApiSuiteResp
	ViewApplicationConfigurationReq             = pb.ViewApplicationConfigurationReq
	ViewApplicationConfigurationResp            = pb.ViewApplicationConfigurationResp
	ViewComponentGroupReq                       = pb.ViewComponentGroupReq
	ViewComponentGroupResp                      = pb.ViewComponentGroupResp
	ViewDataProcessingFunctionReq               = pb.ViewDataProcessingFunctionReq
	ViewDataProcessingFunctionResp              = pb.ViewDataProcessingFunctionResp
	ViewGeneralConfigurationReq                 = pb.ViewGeneralConfigurationReq
	ViewGeneralConfigurationResp                = pb.ViewGeneralConfigurationResp
	ViewGitConfigurationReq                     = pb.ViewGitConfigurationReq
	ViewGitConfigurationResp                    = pb.ViewGitConfigurationResp
	ViewInterfaceCaseReq                        = pb.ViewInterfaceCaseReq
	ViewInterfaceCaseResp                       = pb.ViewInterfaceCaseResp
	ViewInterfaceConfigReq                      = pb.ViewInterfaceConfigReq
	ViewInterfaceConfigResp                     = pb.ViewInterfaceConfigResp
	ViewInterfaceDocumentReq                    = pb.ViewInterfaceDocumentReq
	ViewInterfaceDocumentResp                   = pb.ViewInterfaceDocumentResp
	ViewInterfaceSchemaReq                      = pb.ViewInterfaceSchemaReq
	ViewInterfaceSchemaResp                     = pb.ViewInterfaceSchemaResp
	ViewPerfCaseReq                             = pb.ViewPerfCaseReq
	ViewPerfCaseResp                            = pb.ViewPerfCaseResp
	ViewPerfCaseV2Req                           = pb.ViewPerfCaseV2Req
	ViewPerfCaseV2Resp                          = pb.ViewPerfCaseV2Resp
	ViewPerfPlanReq                             = pb.ViewPerfPlanReq
	ViewPerfPlanResp                            = pb.ViewPerfPlanResp
	ViewPerfPlanV2Req                           = pb.ViewPerfPlanV2Req
	ViewPerfPlanV2Resp                          = pb.ViewPerfPlanV2Resp
	ViewPerfStopRuleReq                         = pb.ViewPerfStopRuleReq
	ViewPerfStopRuleResp                        = pb.ViewPerfStopRuleResp
	ViewProjectReq                              = pb.ViewProjectReq
	ViewProjectResp                             = pb.ViewProjectResp
	ViewPromptConfigurationReq                  = pb.ViewPromptConfigurationReq
	ViewPromptConfigurationResp                 = pb.ViewPromptConfigurationResp
	ViewProtobufConfigurationReq                = pb.ViewProtobufConfigurationReq
	ViewProtobufConfigurationResp               = pb.ViewProtobufConfigurationResp
	ViewStabilityPlanReq                        = pb.ViewStabilityPlanReq
	ViewStabilityPlanResp                       = pb.ViewStabilityPlanResp
	ViewTagReq                                  = pb.ViewTagReq
	ViewTagResp                                 = pb.ViewTagResp
	ViewUIAgentComponentReq                     = pb.ViewUIAgentComponentReq
	ViewUIAgentComponentResp                    = pb.ViewUIAgentComponentResp
	ViewUiPlanReq                               = pb.ViewUiPlanReq
	ViewUiPlanResp                              = pb.ViewUiPlanResp

	GitConfigurationService interface {
		// CreateGitConfiguration 创建Git配置
		CreateGitConfiguration(ctx context.Context, in *CreateGitConfigurationReq, opts ...grpc.CallOption) (*CreateGitConfigurationResp, error)
		// RemoveGitConfiguration 删除Git配置
		RemoveGitConfiguration(ctx context.Context, in *RemoveGitConfigurationReq, opts ...grpc.CallOption) (*RemoveGitConfigurationResp, error)
		// ModifyGitConfiguration 编辑Git配置
		ModifyGitConfiguration(ctx context.Context, in *ModifyGitConfigurationReq, opts ...grpc.CallOption) (*ModifyGitConfigurationResp, error)
		// SearchGitConfiguration 搜索Git配置
		SearchGitConfiguration(ctx context.Context, in *SearchGitConfigurationReq, opts ...grpc.CallOption) (*SearchGitConfigurationResp, error)
		// ViewGitConfiguration 查看Git配置
		ViewGitConfiguration(ctx context.Context, in *ViewGitConfigurationReq, opts ...grpc.CallOption) (*ViewGitConfigurationResp, error)
		// TestGitConfiguration 测试Git配置
		TestGitConfiguration(ctx context.Context, in *TestGitConfigurationReq, opts ...grpc.CallOption) (*TestGitConfigurationResp, error)
		// SyncGitConfiguration 通过手动同步Git配置对应的Git项目测试数据
		SyncGitConfiguration(ctx context.Context, in *SyncGitConfigurationReq, opts ...grpc.CallOption) (*SyncGitConfigurationResp, error)
		// SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
		SyncGitConfigurationByWebhook(ctx context.Context, in *SyncGitConfigurationByWebhookReq, opts ...grpc.CallOption) (*SyncGitConfigurationByWebhookResp, error)
	}

	defaultGitConfigurationService struct {
		cli zrpc.Client
	}
)

func NewGitConfigurationService(cli zrpc.Client) GitConfigurationService {
	return &defaultGitConfigurationService{
		cli: cli,
	}
}

// CreateGitConfiguration 创建Git配置
func (m *defaultGitConfigurationService) CreateGitConfiguration(ctx context.Context, in *CreateGitConfigurationReq, opts ...grpc.CallOption) (*CreateGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.CreateGitConfiguration(ctx, in, opts...)
}

// RemoveGitConfiguration 删除Git配置
func (m *defaultGitConfigurationService) RemoveGitConfiguration(ctx context.Context, in *RemoveGitConfigurationReq, opts ...grpc.CallOption) (*RemoveGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.RemoveGitConfiguration(ctx, in, opts...)
}

// ModifyGitConfiguration 编辑Git配置
func (m *defaultGitConfigurationService) ModifyGitConfiguration(ctx context.Context, in *ModifyGitConfigurationReq, opts ...grpc.CallOption) (*ModifyGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.ModifyGitConfiguration(ctx, in, opts...)
}

// SearchGitConfiguration 搜索Git配置
func (m *defaultGitConfigurationService) SearchGitConfiguration(ctx context.Context, in *SearchGitConfigurationReq, opts ...grpc.CallOption) (*SearchGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.SearchGitConfiguration(ctx, in, opts...)
}

// ViewGitConfiguration 查看Git配置
func (m *defaultGitConfigurationService) ViewGitConfiguration(ctx context.Context, in *ViewGitConfigurationReq, opts ...grpc.CallOption) (*ViewGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.ViewGitConfiguration(ctx, in, opts...)
}

// TestGitConfiguration 测试Git配置
func (m *defaultGitConfigurationService) TestGitConfiguration(ctx context.Context, in *TestGitConfigurationReq, opts ...grpc.CallOption) (*TestGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.TestGitConfiguration(ctx, in, opts...)
}

// SyncGitConfiguration 通过手动同步Git配置对应的Git项目测试数据
func (m *defaultGitConfigurationService) SyncGitConfiguration(ctx context.Context, in *SyncGitConfigurationReq, opts ...grpc.CallOption) (*SyncGitConfigurationResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.SyncGitConfiguration(ctx, in, opts...)
}

// SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
func (m *defaultGitConfigurationService) SyncGitConfigurationByWebhook(ctx context.Context, in *SyncGitConfigurationByWebhookReq, opts ...grpc.CallOption) (*SyncGitConfigurationByWebhookResp, error) {
	client := pb.NewGitConfigurationServiceClient(m.cli.Conn())
	return client.SyncGitConfigurationByWebhook(ctx, in, opts...)
}
