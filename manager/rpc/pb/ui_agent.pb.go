// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/ui_agent.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UIAgentComponent `UI Agent`组件
type UIAgentComponent struct {
	state            protoimpl.MessageState          `protogen:"open.v1"`
	ProjectId        string                          `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                       // 项目ID
	CategoryId       string                          `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                    // 所属分类ID
	ComponentId      string                          `protobuf:"bytes,3,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"`                                 // 组件ID
	Name             string                          `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                                 // 组件名称
	Description      string                          `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                                   // 组件描述
	Tags             []string                        `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty"`                                                                 // 标签
	PlatformType     pb.PlatformType                 `protobuf:"varint,14,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"`   // 平台类型
	ApplicationId    string                          `protobuf:"bytes,15,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`                          // 应用配置ID
	Mode             pb.UIAgentMode                  `protobuf:"varint,16,opt,name=mode,proto3,enum=common.UIAgentMode" json:"mode,omitempty"`                                        // 模式（Agent模式、Step模式）
	AgentModeSteps   []*pb.UIAgentComponentStep      `protobuf:"bytes,17,rep,name=agent_mode_steps,json=agentModeSteps,proto3" json:"agent_mode_steps,omitempty"`                     // Agent模式的步骤列表
	StepModeSteps    []*pb.UIAgentComponentStep      `protobuf:"bytes,18,rep,name=step_mode_steps,json=stepModeSteps,proto3" json:"step_mode_steps,omitempty"`                        // Step模式的步骤列表
	Expectation      *pb.UIAgentComponentExpectation `protobuf:"bytes,19,opt,name=expectation,proto3" json:"expectation,omitempty"`                                                   // 期望结果
	Variables        []*pb.GeneralConfigVar          `protobuf:"bytes,20,rep,name=variables,proto3" json:"variables,omitempty"`                                                       // 变量列表
	ForegroundCheck  bool                            `protobuf:"varint,21,opt,name=foreground_check,json=foregroundCheck,proto3" json:"foreground_check,omitempty"`                   // 是否检查App在前台
	ReferenceId      string                          `protobuf:"bytes,22,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                                // 参考配置ID
	LatestExecutedAt int64                           `protobuf:"varint,31,opt,name=latest_executed_at,json=latestExecutedAt,proto3" json:"latest_executed_at,omitempty"`              // 最近一次执行时间
	LatestResult     pb.ExecutedResult               `protobuf:"varint,32,opt,name=latest_result,json=latestResult,proto3,enum=common.ExecutedResult" json:"latest_result,omitempty"` // 最近一次结果
	State            CommonState                     `protobuf:"varint,41,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                     // 状态
	MaintainedBy     string                          `protobuf:"bytes,42,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                             // 维护者
	CreatedBy        string                          `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                      // 创建者
	UpdatedBy        string                          `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                      // 更新者
	CreatedAt        int64                           `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                     // 创建时间
	UpdatedAt        int64                           `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                     // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UIAgentComponent) Reset() {
	*x = UIAgentComponent{}
	mi := &file_manager_ui_agent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponent) ProtoMessage() {}

func (x *UIAgentComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_ui_agent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponent.ProtoReflect.Descriptor instead.
func (*UIAgentComponent) Descriptor() ([]byte, []int) {
	return file_manager_ui_agent_proto_rawDescGZIP(), []int{0}
}

func (x *UIAgentComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UIAgentComponent) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *UIAgentComponent) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *UIAgentComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UIAgentComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UIAgentComponent) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *UIAgentComponent) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *UIAgentComponent) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UIAgentComponent) GetMode() pb.UIAgentMode {
	if x != nil {
		return x.Mode
	}
	return pb.UIAgentMode(0)
}

func (x *UIAgentComponent) GetAgentModeSteps() []*pb.UIAgentComponentStep {
	if x != nil {
		return x.AgentModeSteps
	}
	return nil
}

func (x *UIAgentComponent) GetStepModeSteps() []*pb.UIAgentComponentStep {
	if x != nil {
		return x.StepModeSteps
	}
	return nil
}

func (x *UIAgentComponent) GetExpectation() *pb.UIAgentComponentExpectation {
	if x != nil {
		return x.Expectation
	}
	return nil
}

func (x *UIAgentComponent) GetVariables() []*pb.GeneralConfigVar {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *UIAgentComponent) GetForegroundCheck() bool {
	if x != nil {
		return x.ForegroundCheck
	}
	return false
}

func (x *UIAgentComponent) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *UIAgentComponent) GetLatestExecutedAt() int64 {
	if x != nil {
		return x.LatestExecutedAt
	}
	return 0
}

func (x *UIAgentComponent) GetLatestResult() pb.ExecutedResult {
	if x != nil {
		return x.LatestResult
	}
	return pb.ExecutedResult(0)
}

func (x *UIAgentComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *UIAgentComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *UIAgentComponent) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *UIAgentComponent) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *UIAgentComponent) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UIAgentComponent) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type DiffDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         int32                  `protobuf:"varint,1,opt,name=field,proto3" json:"field,omitempty"`     // 字段
	Changed       bool                   `protobuf:"varint,2,opt,name=changed,proto3" json:"changed,omitempty"` // 是否有变更
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DiffDetail) Reset() {
	*x = DiffDetail{}
	mi := &file_manager_ui_agent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DiffDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiffDetail) ProtoMessage() {}

func (x *DiffDetail) ProtoReflect() protoreflect.Message {
	mi := &file_manager_ui_agent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiffDetail.ProtoReflect.Descriptor instead.
func (*DiffDetail) Descriptor() ([]byte, []int) {
	return file_manager_ui_agent_proto_rawDescGZIP(), []int{1}
}

func (x *DiffDetail) GetField() int32 {
	if x != nil {
		return x.Field
	}
	return 0
}

func (x *DiffDetail) GetChanged() bool {
	if x != nil {
		return x.Changed
	}
	return false
}

type DeleteUnusedUIAgentImageTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUnusedUIAgentImageTaskInfo) Reset() {
	*x = DeleteUnusedUIAgentImageTaskInfo{}
	mi := &file_manager_ui_agent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUnusedUIAgentImageTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUnusedUIAgentImageTaskInfo) ProtoMessage() {}

func (x *DeleteUnusedUIAgentImageTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_ui_agent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUnusedUIAgentImageTaskInfo.ProtoReflect.Descriptor instead.
func (*DeleteUnusedUIAgentImageTaskInfo) Descriptor() ([]byte, []int) {
	return file_manager_ui_agent_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteUnusedUIAgentImageTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type UpdateUIAgentComponentResultTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`       // 项目ID
	ComponentId   string                 `protobuf:"bytes,2,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"` // 组件ID
	ExecutedAt    int64                  `protobuf:"varint,11,opt,name=executed_at,json=executedAt,proto3" json:"executed_at,omitempty"`  // 执行时间
	Result        pb.ExecutedResult      `protobuf:"varint,12,opt,name=result,proto3,enum=common.ExecutedResult" json:"result,omitempty"` // 执行结果
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUIAgentComponentResultTaskInfo) Reset() {
	*x = UpdateUIAgentComponentResultTaskInfo{}
	mi := &file_manager_ui_agent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUIAgentComponentResultTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUIAgentComponentResultTaskInfo) ProtoMessage() {}

func (x *UpdateUIAgentComponentResultTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_ui_agent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUIAgentComponentResultTaskInfo.ProtoReflect.Descriptor instead.
func (*UpdateUIAgentComponentResultTaskInfo) Descriptor() ([]byte, []int) {
	return file_manager_ui_agent_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUIAgentComponentResultTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdateUIAgentComponentResultTaskInfo) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *UpdateUIAgentComponentResultTaskInfo) GetExecutedAt() int64 {
	if x != nil {
		return x.ExecutedAt
	}
	return 0
}

func (x *UpdateUIAgentComponentResultTaskInfo) GetResult() pb.ExecutedResult {
	if x != nil {
		return x.Result
	}
	return pb.ExecutedResult(0)
}

var File_manager_ui_agent_proto protoreflect.FileDescriptor

var file_manager_ui_agent_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdd, 0x07,
	0x0a, 0x10, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12,
	0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x10, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x65, 0x70, 0x52, 0x0e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x65,
	0x70, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x65, 0x70, 0x52, 0x0d, 0x73, 0x74, 0x65, 0x70, 0x4d,
	0x6f, 0x64, 0x65, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x36, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x61, 0x72, 0x52, 0x09, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x6f, 0x72, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0c, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x3c, 0x0a,
	0x0a, 0x44, 0x69, 0x66, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x22, 0x61, 0x0a, 0x20, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x6e, 0x75, 0x73, 0x65, 0x64, 0x55, 0x49, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e,
	0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x80,
	0x02, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b,
	0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42,
	0x22, 0x72, 0x20, 0x32, 0x1e, 0x28, 0x3f, 0x3a, 0x5e, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e,
	0x2b, 0x3f, 0x29, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x2e, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_ui_agent_proto_rawDescOnce sync.Once
	file_manager_ui_agent_proto_rawDescData = file_manager_ui_agent_proto_rawDesc
)

func file_manager_ui_agent_proto_rawDescGZIP() []byte {
	file_manager_ui_agent_proto_rawDescOnce.Do(func() {
		file_manager_ui_agent_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_ui_agent_proto_rawDescData)
	})
	return file_manager_ui_agent_proto_rawDescData
}

var file_manager_ui_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_manager_ui_agent_proto_goTypes = []any{
	(*UIAgentComponent)(nil),                     // 0: manager.UIAgentComponent
	(*DiffDetail)(nil),                           // 1: manager.DiffDetail
	(*DeleteUnusedUIAgentImageTaskInfo)(nil),     // 2: manager.DeleteUnusedUIAgentImageTaskInfo
	(*UpdateUIAgentComponentResultTaskInfo)(nil), // 3: manager.UpdateUIAgentComponentResultTaskInfo
	(pb.PlatformType)(0),                         // 4: common.PlatformType
	(pb.UIAgentMode)(0),                          // 5: common.UIAgentMode
	(*pb.UIAgentComponentStep)(nil),              // 6: common.UIAgentComponentStep
	(*pb.UIAgentComponentExpectation)(nil),       // 7: common.UIAgentComponentExpectation
	(*pb.GeneralConfigVar)(nil),                  // 8: common.GeneralConfigVar
	(pb.ExecutedResult)(0),                       // 9: common.ExecutedResult
	(CommonState)(0),                             // 10: manager.CommonState
}
var file_manager_ui_agent_proto_depIdxs = []int32{
	4,  // 0: manager.UIAgentComponent.platform_type:type_name -> common.PlatformType
	5,  // 1: manager.UIAgentComponent.mode:type_name -> common.UIAgentMode
	6,  // 2: manager.UIAgentComponent.agent_mode_steps:type_name -> common.UIAgentComponentStep
	6,  // 3: manager.UIAgentComponent.step_mode_steps:type_name -> common.UIAgentComponentStep
	7,  // 4: manager.UIAgentComponent.expectation:type_name -> common.UIAgentComponentExpectation
	8,  // 5: manager.UIAgentComponent.variables:type_name -> common.GeneralConfigVar
	9,  // 6: manager.UIAgentComponent.latest_result:type_name -> common.ExecutedResult
	10, // 7: manager.UIAgentComponent.state:type_name -> manager.CommonState
	9,  // 8: manager.UpdateUIAgentComponentResultTaskInfo.result:type_name -> common.ExecutedResult
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_manager_ui_agent_proto_init() }
func file_manager_ui_agent_proto_init() {
	if File_manager_ui_agent_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_ui_agent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_ui_agent_proto_goTypes,
		DependencyIndexes: file_manager_ui_agent_proto_depIdxs,
		MessageInfos:      file_manager_ui_agent_proto_msgTypes,
	}.Build()
	File_manager_ui_agent_proto = out.File
	file_manager_ui_agent_proto_rawDesc = nil
	file_manager_ui_agent_proto_goTypes = nil
	file_manager_ui_agent_proto_depIdxs = nil
}
