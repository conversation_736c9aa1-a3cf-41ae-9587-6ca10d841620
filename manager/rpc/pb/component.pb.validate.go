// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/component.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on VariableManual with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VariableManual) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableManual with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VariableManualMultiError,
// or nil if none found.
func (m *VariableManual) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableManual) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := VariableManualValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VariableManualMultiError(errors)
	}

	return nil
}

// VariableManualMultiError is an error wrapping multiple validation errors
// returned by VariableManual.ValidateAll() if the designated constraints
// aren't met.
type VariableManualMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableManualMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableManualMultiError) AllErrors() []error { return m }

// VariableManualValidationError is the validation error returned by
// VariableManual.Validate if the designated constraints aren't met.
type VariableManualValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableManualValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableManualValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableManualValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableManualValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableManualValidationError) ErrorName() string { return "VariableManualValidationError" }

// Error satisfies the builtin error interface
func (e VariableManualValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableManual.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableManualValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableManualValidationError{}

// Validate checks the field values on VariableExport with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VariableExport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableExport with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VariableExportMultiError,
// or nil if none found.
func (m *VariableExport) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableExport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetNodeId() != "" {

		if utf8.RuneCountInString(m.GetNodeId()) < 1 {
			err := VariableExportValidationError{
				field:  "NodeId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := VariableExportValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VariableExportMultiError(errors)
	}

	return nil
}

// VariableExportMultiError is an error wrapping multiple validation errors
// returned by VariableExport.ValidateAll() if the designated constraints
// aren't met.
type VariableExportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableExportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableExportMultiError) AllErrors() []error { return m }

// VariableExportValidationError is the validation error returned by
// VariableExport.Validate if the designated constraints aren't met.
type VariableExportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableExportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableExportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableExportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableExportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableExportValidationError) ErrorName() string { return "VariableExportValidationError" }

// Error satisfies the builtin error interface
func (e VariableExportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableExport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableExportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableExportValidationError{}

// Validate checks the field values on VariableEnvironment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VariableEnvironment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableEnvironment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VariableEnvironmentMultiError, or nil if none found.
func (m *VariableEnvironment) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableEnvironment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := VariableEnvironmentValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VariableEnvironmentMultiError(errors)
	}

	return nil
}

// VariableEnvironmentMultiError is an error wrapping multiple validation
// errors returned by VariableEnvironment.ValidateAll() if the designated
// constraints aren't met.
type VariableEnvironmentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableEnvironmentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableEnvironmentMultiError) AllErrors() []error { return m }

// VariableEnvironmentValidationError is the validation error returned by
// VariableEnvironment.Validate if the designated constraints aren't met.
type VariableEnvironmentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableEnvironmentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableEnvironmentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableEnvironmentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableEnvironmentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableEnvironmentValidationError) ErrorName() string {
	return "VariableEnvironmentValidationError"
}

// Error satisfies the builtin error interface
func (e VariableEnvironmentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableEnvironment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableEnvironmentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableEnvironmentValidationError{}

// Validate checks the field values on VariableFunction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VariableFunction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableFunction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VariableFunctionMultiError, or nil if none found.
func (m *VariableFunction) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableFunction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := VariableFunctionValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := FunctionType_name[int32(m.GetType())]; !ok {
		err := VariableFunctionValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetParameters()) > 0 {

		for idx, item := range m.GetParameters() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, VariableFunctionValidationError{
							field:  fmt.Sprintf("Parameters[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, VariableFunctionValidationError{
							field:  fmt.Sprintf("Parameters[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return VariableFunctionValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return VariableFunctionMultiError(errors)
	}

	return nil
}

// VariableFunctionMultiError is an error wrapping multiple validation errors
// returned by VariableFunction.ValidateAll() if the designated constraints
// aren't met.
type VariableFunctionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableFunctionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableFunctionMultiError) AllErrors() []error { return m }

// VariableFunctionValidationError is the validation error returned by
// VariableFunction.Validate if the designated constraints aren't met.
type VariableFunctionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableFunctionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableFunctionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableFunctionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableFunctionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableFunctionValidationError) ErrorName() string { return "VariableFunctionValidationError" }

// Error satisfies the builtin error interface
func (e VariableFunctionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableFunction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableFunctionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableFunctionValidationError{}

// Validate checks the field values on VariableHeader with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VariableHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableHeader with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VariableHeaderMultiError,
// or nil if none found.
func (m *VariableHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKey() != "" {

		if utf8.RuneCountInString(m.GetKey()) < 1 {
			err := VariableHeaderValidationError{
				field:  "Key",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VariableHeaderMultiError(errors)
	}

	return nil
}

// VariableHeaderMultiError is an error wrapping multiple validation errors
// returned by VariableHeader.ValidateAll() if the designated constraints
// aren't met.
type VariableHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableHeaderMultiError) AllErrors() []error { return m }

// VariableHeaderValidationError is the validation error returned by
// VariableHeader.Validate if the designated constraints aren't met.
type VariableHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableHeaderValidationError) ErrorName() string { return "VariableHeaderValidationError" }

// Error satisfies the builtin error interface
func (e VariableHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableHeaderValidationError{}

// Validate checks the field values on VariableBody with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VariableBody) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableBody with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VariableBodyMultiError, or
// nil if none found.
func (m *VariableBody) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableBody) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ExtractType_name[int32(m.GetType())]; !ok {
		err := VariableBodyValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetExpression() != "" {

		if utf8.RuneCountInString(m.GetExpression()) < 1 {
			err := VariableBodyValidationError{
				field:  "Expression",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VariableBodyMultiError(errors)
	}

	return nil
}

// VariableBodyMultiError is an error wrapping multiple validation errors
// returned by VariableBody.ValidateAll() if the designated constraints aren't met.
type VariableBodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableBodyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableBodyMultiError) AllErrors() []error { return m }

// VariableBodyValidationError is the validation error returned by
// VariableBody.Validate if the designated constraints aren't met.
type VariableBodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableBodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableBodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableBodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableBodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableBodyValidationError) ErrorName() string { return "VariableBodyValidationError" }

// Error satisfies the builtin error interface
func (e VariableBodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableBody.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableBodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableBodyValidationError{}

// Validate checks the field values on Import with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Import) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Import with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ImportMultiError, or nil if none found.
func (m *Import) ValidateAll() error {
	return m.validate(true)
}

func (m *Import) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := ImportValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := ImportValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := ImportValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := ImportValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := ImportValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	if len(errors) > 0 {
		return ImportMultiError(errors)
	}

	return nil
}

// ImportMultiError is an error wrapping multiple validation errors returned by
// Import.ValidateAll() if the designated constraints aren't met.
type ImportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportMultiError) AllErrors() []error { return m }

// ImportValidationError is the validation error returned by Import.Validate if
// the designated constraints aren't met.
type ImportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportValidationError) ErrorName() string { return "ImportValidationError" }

// Error satisfies the builtin error interface
func (e ImportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportValidationError{}

// Validate checks the field values on Export with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Export) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Export with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ExportMultiError, or nil if none found.
func (m *Export) ValidateAll() error {
	return m.validate(true)
}

func (m *Export) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := ExportValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetExport() == nil {
		err := ExportValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	if len(errors) > 0 {
		return ExportMultiError(errors)
	}

	return nil
}

// ExportMultiError is an error wrapping multiple validation errors returned by
// Export.ValidateAll() if the designated constraints aren't met.
type ExportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportMultiError) AllErrors() []error { return m }

// ExportValidationError is the validation error returned by Export.Validate if
// the designated constraints aren't met.
type ExportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportValidationError) ErrorName() string { return "ExportValidationError" }

// Error satisfies the builtin error interface
func (e ExportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportValidationError{}

// Validate checks the field values on ComponentGroupComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComponentGroupComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComponentGroupComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComponentGroupComponentMultiError, or nil if none found.
func (m *ComponentGroupComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ComponentGroupComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ComponentGroupId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Version

	for idx, item := range m.GetImports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupComponentValidationError{
					field:  fmt.Sprintf("Imports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetExports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupComponentValidationError{
					field:  fmt.Sprintf("Exports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ComponentGroupComponentMultiError(errors)
	}

	return nil
}

// ComponentGroupComponentMultiError is an error wrapping multiple validation
// errors returned by ComponentGroupComponent.ValidateAll() if the designated
// constraints aren't met.
type ComponentGroupComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentGroupComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentGroupComponentMultiError) AllErrors() []error { return m }

// ComponentGroupComponentValidationError is the validation error returned by
// ComponentGroupComponent.Validate if the designated constraints aren't met.
type ComponentGroupComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentGroupComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentGroupComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentGroupComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentGroupComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentGroupComponentValidationError) ErrorName() string {
	return "ComponentGroupComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ComponentGroupComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponentGroupComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentGroupComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentGroupComponentValidationError{}

// Validate checks the field values on CaseComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseComponentMultiError, or
// nil if none found.
func (m *CaseComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for Version

	// no validation rules for MaintainedBy

	if len(errors) > 0 {
		return CaseComponentMultiError(errors)
	}

	return nil
}

// CaseComponentMultiError is an error wrapping multiple validation errors
// returned by CaseComponent.ValidateAll() if the designated constraints
// aren't met.
type CaseComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseComponentMultiError) AllErrors() []error { return m }

// CaseComponentValidationError is the validation error returned by
// CaseComponent.Validate if the designated constraints aren't met.
type CaseComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseComponentValidationError) ErrorName() string { return "CaseComponentValidationError" }

// Error satisfies the builtin error interface
func (e CaseComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseComponentValidationError{}

// Validate checks the field values on SuiteComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SuiteComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuiteComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SuiteComponentMultiError,
// or nil if none found.
func (m *SuiteComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *SuiteComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for CaseExecutionMode

	if len(errors) > 0 {
		return SuiteComponentMultiError(errors)
	}

	return nil
}

// SuiteComponentMultiError is an error wrapping multiple validation errors
// returned by SuiteComponent.ValidateAll() if the designated constraints
// aren't met.
type SuiteComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuiteComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuiteComponentMultiError) AllErrors() []error { return m }

// SuiteComponentValidationError is the validation error returned by
// SuiteComponent.Validate if the designated constraints aren't met.
type SuiteComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuiteComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuiteComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuiteComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuiteComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuiteComponentValidationError) ErrorName() string { return "SuiteComponentValidationError" }

// Error satisfies the builtin error interface
func (e SuiteComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuiteComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuiteComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuiteComponentValidationError{}

// Validate checks the field values on PlanComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlanComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PlanComponentMultiError, or
// nil if none found.
func (m *PlanComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for Type

	// no validation rules for Purpose

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanComponentValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanComponentValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanComponentValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfigs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanComponentValidationError{
						field:  fmt.Sprintf("AccountConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanComponentValidationError{
						field:  fmt.Sprintf("AccountConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanComponentValidationError{
					field:  fmt.Sprintf("AccountConfigs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SuiteExecutionMode

	// no validation rules for CaseExecutionMode

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return PlanComponentMultiError(errors)
	}

	return nil
}

// PlanComponentMultiError is an error wrapping multiple validation errors
// returned by PlanComponent.ValidateAll() if the designated constraints
// aren't met.
type PlanComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanComponentMultiError) AllErrors() []error { return m }

// PlanComponentValidationError is the validation error returned by
// PlanComponent.Validate if the designated constraints aren't met.
type PlanComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanComponentValidationError) ErrorName() string { return "PlanComponentValidationError" }

// Error satisfies the builtin error interface
func (e PlanComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanComponentValidationError{}

// Validate checks the field values on InterfaceCaseComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceCaseComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceCaseComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceCaseComponentMultiError, or nil if none found.
func (m *InterfaceCaseComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceCaseComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DocumentId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for Version

	// no validation rules for MaintainedBy

	if len(errors) > 0 {
		return InterfaceCaseComponentMultiError(errors)
	}

	return nil
}

// InterfaceCaseComponentMultiError is an error wrapping multiple validation
// errors returned by InterfaceCaseComponent.ValidateAll() if the designated
// constraints aren't met.
type InterfaceCaseComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceCaseComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceCaseComponentMultiError) AllErrors() []error { return m }

// InterfaceCaseComponentValidationError is the validation error returned by
// InterfaceCaseComponent.Validate if the designated constraints aren't met.
type InterfaceCaseComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceCaseComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceCaseComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceCaseComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceCaseComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceCaseComponentValidationError) ErrorName() string {
	return "InterfaceCaseComponentValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceCaseComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceCaseComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceCaseComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceCaseComponentValidationError{}

// Validate checks the field values on InterfaceDocumentComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceDocumentComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceDocumentComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceDocumentComponentMultiError, or nil if none found.
func (m *InterfaceDocumentComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceDocumentComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DocumentId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for CaseExecutionMode

	if len(errors) > 0 {
		return InterfaceDocumentComponentMultiError(errors)
	}

	return nil
}

// InterfaceDocumentComponentMultiError is an error wrapping multiple
// validation errors returned by InterfaceDocumentComponent.ValidateAll() if
// the designated constraints aren't met.
type InterfaceDocumentComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceDocumentComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceDocumentComponentMultiError) AllErrors() []error { return m }

// InterfaceDocumentComponentValidationError is the validation error returned
// by InterfaceDocumentComponent.Validate if the designated constraints aren't met.
type InterfaceDocumentComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceDocumentComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceDocumentComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceDocumentComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceDocumentComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceDocumentComponentValidationError) ErrorName() string {
	return "InterfaceDocumentComponentValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceDocumentComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceDocumentComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceDocumentComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceDocumentComponentValidationError{}

// Validate checks the field values on UICaseComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UICaseComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICaseComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UICaseComponentMultiError, or nil if none found.
func (m *UICaseComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UICaseComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Path

	// no validation rules for State

	// no validation rules for Udid

	if len(errors) > 0 {
		return UICaseComponentMultiError(errors)
	}

	return nil
}

// UICaseComponentMultiError is an error wrapping multiple validation errors
// returned by UICaseComponent.ValidateAll() if the designated constraints
// aren't met.
type UICaseComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICaseComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICaseComponentMultiError) AllErrors() []error { return m }

// UICaseComponentValidationError is the validation error returned by
// UICaseComponent.Validate if the designated constraints aren't met.
type UICaseComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICaseComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICaseComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICaseComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICaseComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICaseComponentValidationError) ErrorName() string { return "UICaseComponentValidationError" }

// Error satisfies the builtin error interface
func (e UICaseComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICaseComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICaseComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICaseComponentValidationError{}

// Validate checks the field values on UISuiteComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UISuiteComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UISuiteComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UISuiteComponentMultiError, or nil if none found.
func (m *UISuiteComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UISuiteComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Path

	// no validation rules for State

	if len(errors) > 0 {
		return UISuiteComponentMultiError(errors)
	}

	return nil
}

// UISuiteComponentMultiError is an error wrapping multiple validation errors
// returned by UISuiteComponent.ValidateAll() if the designated constraints
// aren't met.
type UISuiteComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UISuiteComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UISuiteComponentMultiError) AllErrors() []error { return m }

// UISuiteComponentValidationError is the validation error returned by
// UISuiteComponent.Validate if the designated constraints aren't met.
type UISuiteComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UISuiteComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UISuiteComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UISuiteComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UISuiteComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UISuiteComponentValidationError) ErrorName() string { return "UISuiteComponentValidationError" }

// Error satisfies the builtin error interface
func (e UISuiteComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUISuiteComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UISuiteComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UISuiteComponentValidationError{}

// Validate checks the field values on UIPlanMetaData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UIPlanMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIPlanMetaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UIPlanMetaDataMultiError,
// or nil if none found.
func (m *UIPlanMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *UIPlanMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PriorityType

	if all {
		switch v := interface{}(m.GetGitConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIPlanMetaDataValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIPlanMetaDataValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIPlanMetaDataValidationError{
				field:  "GitConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SuiteExecutionMode

	// no validation rules for CaseExecutionMode

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	// no validation rules for PackageName

	// no validation rules for AppName

	// no validation rules for CallbackUrl

	// no validation rules for AppDownloadLink

	// no validation rules for AppVersion

	// no validation rules for TestLanguage

	// no validation rules for TestLanguageVersion

	// no validation rules for TestFramework

	// no validation rules for ExecutionEnvironment

	// no validation rules for FailRetry

	// no validation rules for Together

	if len(errors) > 0 {
		return UIPlanMetaDataMultiError(errors)
	}

	return nil
}

// UIPlanMetaDataMultiError is an error wrapping multiple validation errors
// returned by UIPlanMetaData.ValidateAll() if the designated constraints
// aren't met.
type UIPlanMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIPlanMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIPlanMetaDataMultiError) AllErrors() []error { return m }

// UIPlanMetaDataValidationError is the validation error returned by
// UIPlanMetaData.Validate if the designated constraints aren't met.
type UIPlanMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIPlanMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIPlanMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIPlanMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIPlanMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIPlanMetaDataValidationError) ErrorName() string { return "UIPlanMetaDataValidationError" }

// Error satisfies the builtin error interface
func (e UIPlanMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIPlanMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIPlanMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIPlanMetaDataValidationError{}

// Validate checks the field values on UIPlanComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UIPlanComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIPlanComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIPlanComponentMultiError, or nil if none found.
func (m *UIPlanComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UIPlanComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIPlanComponentValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIPlanComponentValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIPlanComponentValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return UIPlanComponentMultiError(errors)
	}

	return nil
}

// UIPlanComponentMultiError is an error wrapping multiple validation errors
// returned by UIPlanComponent.ValidateAll() if the designated constraints
// aren't met.
type UIPlanComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIPlanComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIPlanComponentMultiError) AllErrors() []error { return m }

// UIPlanComponentValidationError is the validation error returned by
// UIPlanComponent.Validate if the designated constraints aren't met.
type UIPlanComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIPlanComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIPlanComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIPlanComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIPlanComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIPlanComponentValidationError) ErrorName() string { return "UIPlanComponentValidationError" }

// Error satisfies the builtin error interface
func (e UIPlanComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIPlanComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIPlanComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIPlanComponentValidationError{}

// Validate checks the field values on ServiceComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServiceComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceComponentMultiError, or nil if none found.
func (m *ServiceComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ServiceName

	// no validation rules for ServiceId

	if len(errors) > 0 {
		return ServiceComponentMultiError(errors)
	}

	return nil
}

// ServiceComponentMultiError is an error wrapping multiple validation errors
// returned by ServiceComponent.ValidateAll() if the designated constraints
// aren't met.
type ServiceComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceComponentMultiError) AllErrors() []error { return m }

// ServiceComponentValidationError is the validation error returned by
// ServiceComponent.Validate if the designated constraints aren't met.
type ServiceComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceComponentValidationError) ErrorName() string { return "ServiceComponentValidationError" }

// Error satisfies the builtin error interface
func (e ServiceComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceComponentValidationError{}

// Validate checks the field values on PerfDataComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfDataComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfDataComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfDataComponentMultiError, or nil if none found.
func (m *PerfDataComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfDataComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DataId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Extension

	// no validation rules for Hash

	// no validation rules for Size

	// no validation rules for Path

	// no validation rules for NumberOfVu

	if len(errors) > 0 {
		return PerfDataComponentMultiError(errors)
	}

	return nil
}

// PerfDataComponentMultiError is an error wrapping multiple validation errors
// returned by PerfDataComponent.ValidateAll() if the designated constraints
// aren't met.
type PerfDataComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfDataComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfDataComponentMultiError) AllErrors() []error { return m }

// PerfDataComponentValidationError is the validation error returned by
// PerfDataComponent.Validate if the designated constraints aren't met.
type PerfDataComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfDataComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfDataComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfDataComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfDataComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfDataComponentValidationError) ErrorName() string {
	return "PerfDataComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PerfDataComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfDataComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfDataComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfDataComponentValidationError{}

// Validate checks the field values on PerfCaseComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseComponentMultiError, or nil if none found.
func (m *PerfCaseComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Extension

	// no validation rules for Hash

	// no validation rules for Size

	// no validation rules for Path

	// no validation rules for TargetRps

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseComponentValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSetupSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("SetupSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("SetupSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseComponentValidationError{
					field:  fmt.Sprintf("SetupSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSerialSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseComponentValidationError{
					field:  fmt.Sprintf("SerialSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetParallelSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseComponentValidationError{
					field:  fmt.Sprintf("ParallelSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTeardownSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("TeardownSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseComponentValidationError{
						field:  fmt.Sprintf("TeardownSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseComponentValidationError{
					field:  fmt.Sprintf("TeardownSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPerfData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseComponentValidationError{
					field:  "PerfData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseComponentValidationError{
					field:  "PerfData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseComponentValidationError{
				field:  "PerfData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NumberOfVu

	if all {
		switch v := interface{}(m.GetLoadGenerator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseComponentValidationError{
					field:  "LoadGenerator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseComponentValidationError{
					field:  "LoadGenerator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoadGenerator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseComponentValidationError{
				field:  "LoadGenerator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	if len(errors) > 0 {
		return PerfCaseComponentMultiError(errors)
	}

	return nil
}

// PerfCaseComponentMultiError is an error wrapping multiple validation errors
// returned by PerfCaseComponent.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseComponentMultiError) AllErrors() []error { return m }

// PerfCaseComponentValidationError is the validation error returned by
// PerfCaseComponent.Validate if the designated constraints aren't met.
type PerfCaseComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseComponentValidationError) ErrorName() string {
	return "PerfCaseComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCaseComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseComponentValidationError{}

// Validate checks the field values on PerfSuiteComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfSuiteComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfSuiteComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfSuiteComponentMultiError, or nil if none found.
func (m *PerfSuiteComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfSuiteComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	if len(errors) > 0 {
		return PerfSuiteComponentMultiError(errors)
	}

	return nil
}

// PerfSuiteComponentMultiError is an error wrapping multiple validation errors
// returned by PerfSuiteComponent.ValidateAll() if the designated constraints
// aren't met.
type PerfSuiteComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfSuiteComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfSuiteComponentMultiError) AllErrors() []error { return m }

// PerfSuiteComponentValidationError is the validation error returned by
// PerfSuiteComponent.Validate if the designated constraints aren't met.
type PerfSuiteComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfSuiteComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfSuiteComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfSuiteComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfSuiteComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfSuiteComponentValidationError) ErrorName() string {
	return "PerfSuiteComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PerfSuiteComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfSuiteComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfSuiteComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfSuiteComponentValidationError{}

// Validate checks the field values on PerfPlanMetaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfPlanMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfPlanMetaDataMultiError, or nil if none found.
func (m *PerfPlanMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Protocol

	// no validation rules for TargetEnv

	for idx, item := range m.GetProtobufConfigs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanMetaDataValidationError{
						field:  fmt.Sprintf("ProtobufConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanMetaDataValidationError{
						field:  fmt.Sprintf("ProtobufConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanMetaDataValidationError{
					field:  fmt.Sprintf("ProtobufConfigs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanMetaDataValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanMetaDataValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TargetMaxRps

	// no validation rules for Duration

	if all {
		switch v := interface{}(m.GetKeepalive()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "Keepalive",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "Keepalive",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKeepalive()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanMetaDataValidationError{
				field:  "Keepalive",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Delay

	if all {
		switch v := interface{}(m.GetRateLimits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "RateLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanMetaDataValidationError{
					field:  "RateLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateLimits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanMetaDataValidationError{
				field:  "RateLimits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetServices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanMetaDataValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanMetaDataValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanMetaDataValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanMetaDataValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanMetaDataValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanMetaDataValidationError{
					field:  fmt.Sprintf("Rules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerfPlanMetaDataMultiError(errors)
	}

	return nil
}

// PerfPlanMetaDataMultiError is an error wrapping multiple validation errors
// returned by PerfPlanMetaData.ValidateAll() if the designated constraints
// aren't met.
type PerfPlanMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanMetaDataMultiError) AllErrors() []error { return m }

// PerfPlanMetaDataValidationError is the validation error returned by
// PerfPlanMetaData.Validate if the designated constraints aren't met.
type PerfPlanMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanMetaDataValidationError) ErrorName() string { return "PerfPlanMetaDataValidationError" }

// Error satisfies the builtin error interface
func (e PerfPlanMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanMetaDataValidationError{}

// Validate checks the field values on PerfPlanComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfPlanComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfPlanComponentMultiError, or nil if none found.
func (m *PerfPlanComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanComponentValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanComponentValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanComponentValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return PerfPlanComponentMultiError(errors)
	}

	return nil
}

// PerfPlanComponentMultiError is an error wrapping multiple validation errors
// returned by PerfPlanComponent.ValidateAll() if the designated constraints
// aren't met.
type PerfPlanComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanComponentMultiError) AllErrors() []error { return m }

// PerfPlanComponentValidationError is the validation error returned by
// PerfPlanComponent.Validate if the designated constraints aren't met.
type PerfPlanComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanComponentValidationError) ErrorName() string {
	return "PerfPlanComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PerfPlanComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanComponentValidationError{}

// Validate checks the field values on StabilityPlanMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityPlanMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlanMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityPlanMetaDataMultiError, or nil if none found.
func (m *StabilityPlanMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlanMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanMetaDataValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLarkChats() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityPlanMetaDataValidationError{
						field:  fmt.Sprintf("LarkChats[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityPlanMetaDataValidationError{
						field:  fmt.Sprintf("LarkChats[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityPlanMetaDataValidationError{
					field:  fmt.Sprintf("LarkChats[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	if all {
		switch v := interface{}(m.GetDevices()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "Devices",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "Devices",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevices()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanMetaDataValidationError{
				field:  "Devices",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PackageName

	// no validation rules for AppVersion

	// no validation rules for AppDownloadLink

	if all {
		switch v := interface{}(m.GetCustomScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanMetaDataValidationError{
				field:  "CustomScript",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Duration

	if len(errors) > 0 {
		return StabilityPlanMetaDataMultiError(errors)
	}

	return nil
}

// StabilityPlanMetaDataMultiError is an error wrapping multiple validation
// errors returned by StabilityPlanMetaData.ValidateAll() if the designated
// constraints aren't met.
type StabilityPlanMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanMetaDataMultiError) AllErrors() []error { return m }

// StabilityPlanMetaDataValidationError is the validation error returned by
// StabilityPlanMetaData.Validate if the designated constraints aren't met.
type StabilityPlanMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanMetaDataValidationError) ErrorName() string {
	return "StabilityPlanMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityPlanMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlanMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanMetaDataValidationError{}

// Validate checks the field values on StabilityPlanComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityPlanComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlanComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityPlanComponentMultiError, or nil if none found.
func (m *StabilityPlanComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlanComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanComponentValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanComponentValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanComponentValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return StabilityPlanComponentMultiError(errors)
	}

	return nil
}

// StabilityPlanComponentMultiError is an error wrapping multiple validation
// errors returned by StabilityPlanComponent.ValidateAll() if the designated
// constraints aren't met.
type StabilityPlanComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanComponentMultiError) AllErrors() []error { return m }

// StabilityPlanComponentValidationError is the validation error returned by
// StabilityPlanComponent.Validate if the designated constraints aren't met.
type StabilityPlanComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanComponentValidationError) ErrorName() string {
	return "StabilityPlanComponentValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityPlanComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlanComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanComponentValidationError{}

// Validate checks the field values on UIAgentComponentComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentComponentMultiError, or nil if none found.
func (m *UIAgentComponentComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ComponentId

	// no validation rules for Name

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetApplicationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentComponentValidationError{
					field:  "ApplicationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentComponentValidationError{
					field:  "ApplicationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentComponentValidationError{
				field:  "ApplicationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Mode

	for idx, item := range m.GetSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentComponentValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentComponentValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentComponentValidationError{
					field:  fmt.Sprintf("Steps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentComponentValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentComponentValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentComponentValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVariables() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentComponentValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentComponentValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentComponentValidationError{
					field:  fmt.Sprintf("Variables[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ForegroundCheck

	// no validation rules for ReferenceId

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return UIAgentComponentComponentMultiError(errors)
	}

	return nil
}

// UIAgentComponentComponentMultiError is an error wrapping multiple validation
// errors returned by UIAgentComponentComponent.ValidateAll() if the
// designated constraints aren't met.
type UIAgentComponentComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentComponentMultiError) AllErrors() []error { return m }

// UIAgentComponentComponentValidationError is the validation error returned by
// UIAgentComponentComponent.Validate if the designated constraints aren't met.
type UIAgentComponentComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentComponentValidationError) ErrorName() string {
	return "UIAgentComponentComponentValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentComponentValidationError{}

// Validate checks the field values on UIAgentCaseComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentCaseComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentCaseComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentCaseComponentMultiError, or nil if none found.
func (m *UIAgentCaseComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentCaseComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UIAgentCaseComponentMultiError(errors)
	}

	return nil
}

// UIAgentCaseComponentMultiError is an error wrapping multiple validation
// errors returned by UIAgentCaseComponent.ValidateAll() if the designated
// constraints aren't met.
type UIAgentCaseComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentCaseComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentCaseComponentMultiError) AllErrors() []error { return m }

// UIAgentCaseComponentValidationError is the validation error returned by
// UIAgentCaseComponent.Validate if the designated constraints aren't met.
type UIAgentCaseComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentCaseComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentCaseComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentCaseComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentCaseComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentCaseComponentValidationError) ErrorName() string {
	return "UIAgentCaseComponentValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentCaseComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentCaseComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentCaseComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentCaseComponentValidationError{}

// Validate checks the field values on UIAgentPlanComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentPlanComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentPlanComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentPlanComponentMultiError, or nil if none found.
func (m *UIAgentPlanComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentPlanComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UIAgentPlanComponentMultiError(errors)
	}

	return nil
}

// UIAgentPlanComponentMultiError is an error wrapping multiple validation
// errors returned by UIAgentPlanComponent.ValidateAll() if the designated
// constraints aren't met.
type UIAgentPlanComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentPlanComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentPlanComponentMultiError) AllErrors() []error { return m }

// UIAgentPlanComponentValidationError is the validation error returned by
// UIAgentPlanComponent.Validate if the designated constraints aren't met.
type UIAgentPlanComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentPlanComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentPlanComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentPlanComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentPlanComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentPlanComponentValidationError) ErrorName() string {
	return "UIAgentPlanComponentValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentPlanComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentPlanComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentPlanComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentPlanComponentValidationError{}

// Validate checks the field values on StartComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StartComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StartComponentMultiError,
// or nil if none found.
func (m *StartComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *StartComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return StartComponentMultiError(errors)
	}

	return nil
}

// StartComponentMultiError is an error wrapping multiple validation errors
// returned by StartComponent.ValidateAll() if the designated constraints
// aren't met.
type StartComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartComponentMultiError) AllErrors() []error { return m }

// StartComponentValidationError is the validation error returned by
// StartComponent.Validate if the designated constraints aren't met.
type StartComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartComponentValidationError) ErrorName() string { return "StartComponentValidationError" }

// Error satisfies the builtin error interface
func (e StartComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartComponentValidationError{}

// Validate checks the field values on EndComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EndComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EndComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EndComponentMultiError, or
// nil if none found.
func (m *EndComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *EndComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EndComponentMultiError(errors)
	}

	return nil
}

// EndComponentMultiError is an error wrapping multiple validation errors
// returned by EndComponent.ValidateAll() if the designated constraints aren't met.
type EndComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EndComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EndComponentMultiError) AllErrors() []error { return m }

// EndComponentValidationError is the validation error returned by
// EndComponent.Validate if the designated constraints aren't met.
type EndComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EndComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EndComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EndComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EndComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EndComponentValidationError) ErrorName() string { return "EndComponentValidationError" }

// Error satisfies the builtin error interface
func (e EndComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEndComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EndComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EndComponentValidationError{}

// Validate checks the field values on SetupComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SetupComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetupComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SetupComponentMultiError,
// or nil if none found.
func (m *SetupComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *SetupComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetImports()) > 0 {

		for idx, item := range m.GetImports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, SetupComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, SetupComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return SetupComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, SetupComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, SetupComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return SetupComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return SetupComponentMultiError(errors)
	}

	return nil
}

// SetupComponentMultiError is an error wrapping multiple validation errors
// returned by SetupComponent.ValidateAll() if the designated constraints
// aren't met.
type SetupComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetupComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetupComponentMultiError) AllErrors() []error { return m }

// SetupComponentValidationError is the validation error returned by
// SetupComponent.Validate if the designated constraints aren't met.
type SetupComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetupComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetupComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetupComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetupComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetupComponentValidationError) ErrorName() string { return "SetupComponentValidationError" }

// Error satisfies the builtin error interface
func (e SetupComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetupComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetupComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetupComponentValidationError{}

// Validate checks the field values on TeardownComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TeardownComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TeardownComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TeardownComponentMultiError, or nil if none found.
func (m *TeardownComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *TeardownComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetImports()) > 0 {

		for idx, item := range m.GetImports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, TeardownComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, TeardownComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return TeardownComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return TeardownComponentMultiError(errors)
	}

	return nil
}

// TeardownComponentMultiError is an error wrapping multiple validation errors
// returned by TeardownComponent.ValidateAll() if the designated constraints
// aren't met.
type TeardownComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TeardownComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TeardownComponentMultiError) AllErrors() []error { return m }

// TeardownComponentValidationError is the validation error returned by
// TeardownComponent.Validate if the designated constraints aren't met.
type TeardownComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TeardownComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TeardownComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TeardownComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TeardownComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TeardownComponentValidationError) ErrorName() string {
	return "TeardownComponentValidationError"
}

// Error satisfies the builtin error interface
func (e TeardownComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTeardownComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TeardownComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TeardownComponentValidationError{}

// Validate checks the field values on BusinessSingleComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BusinessSingleComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BusinessSingleComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BusinessSingleComponentMultiError, or nil if none found.
func (m *BusinessSingleComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *BusinessSingleComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetImports()) > 0 {

		for idx, item := range m.GetImports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BusinessSingleComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BusinessSingleComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BusinessSingleComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BusinessSingleComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BusinessSingleComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BusinessSingleComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return BusinessSingleComponentMultiError(errors)
	}

	return nil
}

// BusinessSingleComponentMultiError is an error wrapping multiple validation
// errors returned by BusinessSingleComponent.ValidateAll() if the designated
// constraints aren't met.
type BusinessSingleComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BusinessSingleComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BusinessSingleComponentMultiError) AllErrors() []error { return m }

// BusinessSingleComponentValidationError is the validation error returned by
// BusinessSingleComponent.Validate if the designated constraints aren't met.
type BusinessSingleComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BusinessSingleComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BusinessSingleComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BusinessSingleComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BusinessSingleComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BusinessSingleComponentValidationError) ErrorName() string {
	return "BusinessSingleComponentValidationError"
}

// Error satisfies the builtin error interface
func (e BusinessSingleComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBusinessSingleComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BusinessSingleComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BusinessSingleComponentValidationError{}

// Validate checks the field values on BusinessGroupComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BusinessGroupComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BusinessGroupComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BusinessGroupComponentMultiError, or nil if none found.
func (m *BusinessGroupComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *BusinessGroupComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetImports()) > 0 {

		for idx, item := range m.GetImports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BusinessGroupComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BusinessGroupComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BusinessGroupComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BusinessGroupComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BusinessGroupComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BusinessGroupComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return BusinessGroupComponentMultiError(errors)
	}

	return nil
}

// BusinessGroupComponentMultiError is an error wrapping multiple validation
// errors returned by BusinessGroupComponent.ValidateAll() if the designated
// constraints aren't met.
type BusinessGroupComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BusinessGroupComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BusinessGroupComponentMultiError) AllErrors() []error { return m }

// BusinessGroupComponentValidationError is the validation error returned by
// BusinessGroupComponent.Validate if the designated constraints aren't met.
type BusinessGroupComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BusinessGroupComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BusinessGroupComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BusinessGroupComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BusinessGroupComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BusinessGroupComponentValidationError) ErrorName() string {
	return "BusinessGroupComponentValidationError"
}

// Error satisfies the builtin error interface
func (e BusinessGroupComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBusinessGroupComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BusinessGroupComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BusinessGroupComponentValidationError{}

// Validate checks the field values on LoopComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoopComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoopComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoopComponentMultiError, or
// nil if none found.
func (m *LoopComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := LoopComponent_Type_name[int32(m.GetType())]; !ok {
		err := LoopComponentValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTimeout() < 0 {
		err := LoopComponentValidationError{
			field:  "Timeout",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFor() == nil {
		err := LoopComponentValidationError{
			field:  "For",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponentValidationError{
					field:  "For",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponentValidationError{
					field:  "For",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponentValidationError{
				field:  "For",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetForEach() == nil {
		err := LoopComponentValidationError{
			field:  "ForEach",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetForEach()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponentValidationError{
					field:  "ForEach",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponentValidationError{
					field:  "ForEach",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForEach()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponentValidationError{
				field:  "ForEach",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetWhile() == nil {
		err := LoopComponentValidationError{
			field:  "While",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetWhile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponentValidationError{
					field:  "While",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponentValidationError{
					field:  "While",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWhile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponentValidationError{
				field:  "While",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoopComponentMultiError(errors)
	}

	return nil
}

// LoopComponentMultiError is an error wrapping multiple validation errors
// returned by LoopComponent.ValidateAll() if the designated constraints
// aren't met.
type LoopComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponentMultiError) AllErrors() []error { return m }

// LoopComponentValidationError is the validation error returned by
// LoopComponent.Validate if the designated constraints aren't met.
type LoopComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponentValidationError) ErrorName() string { return "LoopComponentValidationError" }

// Error satisfies the builtin error interface
func (e LoopComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponentValidationError{}

// Validate checks the field values on HttpRequestComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HttpRequestComponentMultiError, or nil if none found.
func (m *HttpRequestComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUrl()) < 2 {
		err := HttpRequestComponentValidationError{
			field:  "Url",
			reason: "value length must be at least 2 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _HttpRequestComponent_Method_InLookup[m.GetMethod()]; !ok {
		err := HttpRequestComponentValidationError{
			field:  "Method",
			reason: "value must be in list [GET POST PUT DELETE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAuthorization() == nil {
		err := HttpRequestComponentValidationError{
			field:  "Authorization",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAuthorization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponentValidationError{
					field:  "Authorization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponentValidationError{
					field:  "Authorization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthorization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponentValidationError{
				field:  "Authorization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetHeaders()) > 0 {

		for idx, item := range m.GetHeaders() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Headers[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Headers[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HttpRequestComponentValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetQueryParams()) > 0 {

		for idx, item := range m.GetQueryParams() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("QueryParams[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("QueryParams[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HttpRequestComponentValidationError{
						field:  fmt.Sprintf("QueryParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if m.GetBody() == nil {
		err := HttpRequestComponentValidationError{
			field:  "Body",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponentValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponentValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponentValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetTimeout() == nil {
		err := HttpRequestComponentValidationError{
			field:  "Timeout",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponentValidationError{
					field:  "Timeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponentValidationError{
					field:  "Timeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponentValidationError{
				field:  "Timeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetAssertions()) > 0 {

		for idx, item := range m.GetAssertions() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Assertions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Assertions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HttpRequestComponentValidationError{
						field:  fmt.Sprintf("Assertions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetImports()) > 0 {

		for idx, item := range m.GetImports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HttpRequestComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HttpRequestComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HttpRequestComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return HttpRequestComponentMultiError(errors)
	}

	return nil
}

// HttpRequestComponentMultiError is an error wrapping multiple validation
// errors returned by HttpRequestComponent.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponentMultiError) AllErrors() []error { return m }

// HttpRequestComponentValidationError is the validation error returned by
// HttpRequestComponent.Validate if the designated constraints aren't met.
type HttpRequestComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponentValidationError) ErrorName() string {
	return "HttpRequestComponentValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponentValidationError{}

var _HttpRequestComponent_Method_InLookup = map[string]struct{}{
	"GET":    {},
	"POST":   {},
	"PUT":    {},
	"DELETE": {},
}

// Validate checks the field values on ReferenceComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReferenceComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReferenceComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReferenceComponentMultiError, or nil if none found.
func (m *ReferenceComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ReferenceComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetReferenceId()) < 1 {
		err := ReferenceComponentValidationError{
			field:  "ReferenceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Version

	if len(m.GetImports()) > 0 {

		for idx, item := range m.GetImports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ReferenceComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ReferenceComponentValidationError{
							field:  fmt.Sprintf("Imports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ReferenceComponentValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ReferenceComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ReferenceComponentValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ReferenceComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	// no validation rules for ReferenceComponentId

	if len(errors) > 0 {
		return ReferenceComponentMultiError(errors)
	}

	return nil
}

// ReferenceComponentMultiError is an error wrapping multiple validation errors
// returned by ReferenceComponent.ValidateAll() if the designated constraints
// aren't met.
type ReferenceComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReferenceComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReferenceComponentMultiError) AllErrors() []error { return m }

// ReferenceComponentValidationError is the validation error returned by
// ReferenceComponent.Validate if the designated constraints aren't met.
type ReferenceComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReferenceComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReferenceComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReferenceComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReferenceComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReferenceComponentValidationError) ErrorName() string {
	return "ReferenceComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ReferenceComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReferenceComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReferenceComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReferenceComponentValidationError{}

// Validate checks the field values on ConditionComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConditionComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConditionComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConditionComponentMultiError, or nil if none found.
func (m *ConditionComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ConditionType_name[int32(m.GetType())]; !ok {
		err := ConditionComponentValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSingle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponentValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponentValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSingle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponentValidationError{
				field:  "Single",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponentValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponentValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponentValidationError{
				field:  "Group",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConditionComponentMultiError(errors)
	}

	return nil
}

// ConditionComponentMultiError is an error wrapping multiple validation errors
// returned by ConditionComponent.ValidateAll() if the designated constraints
// aren't met.
type ConditionComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionComponentMultiError) AllErrors() []error { return m }

// ConditionComponentValidationError is the validation error returned by
// ConditionComponent.Validate if the designated constraints aren't met.
type ConditionComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionComponentValidationError) ErrorName() string {
	return "ConditionComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionComponentValidationError{}

// Validate checks the field values on WaitComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WaitComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WaitComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WaitComponentMultiError, or
// nil if none found.
func (m *WaitComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *WaitComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := WaitComponent_Type_name[int32(m.GetType())]; !ok {
		err := WaitComponentValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSleep() == nil {
		err := WaitComponentValidationError{
			field:  "Sleep",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSleep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaitComponentValidationError{
					field:  "Sleep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaitComponentValidationError{
					field:  "Sleep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSleep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaitComponentValidationError{
				field:  "Sleep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// skipping validation for manual

	if len(errors) > 0 {
		return WaitComponentMultiError(errors)
	}

	return nil
}

// WaitComponentMultiError is an error wrapping multiple validation errors
// returned by WaitComponent.ValidateAll() if the designated constraints
// aren't met.
type WaitComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WaitComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WaitComponentMultiError) AllErrors() []error { return m }

// WaitComponentValidationError is the validation error returned by
// WaitComponent.Validate if the designated constraints aren't met.
type WaitComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WaitComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WaitComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WaitComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WaitComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WaitComponentValidationError) ErrorName() string { return "WaitComponentValidationError" }

// Error satisfies the builtin error interface
func (e WaitComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWaitComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WaitComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WaitComponentValidationError{}

// Validate checks the field values on AssertComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AssertComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssertComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssertComponentMultiError, or nil if none found.
func (m *AssertComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *AssertComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetAssertions()) > 0 {

		for idx, item := range m.GetAssertions() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, AssertComponentValidationError{
							field:  fmt.Sprintf("Assertions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, AssertComponentValidationError{
							field:  fmt.Sprintf("Assertions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return AssertComponentValidationError{
						field:  fmt.Sprintf("Assertions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return AssertComponentMultiError(errors)
	}

	return nil
}

// AssertComponentMultiError is an error wrapping multiple validation errors
// returned by AssertComponent.ValidateAll() if the designated constraints
// aren't met.
type AssertComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssertComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssertComponentMultiError) AllErrors() []error { return m }

// AssertComponentValidationError is the validation error returned by
// AssertComponent.Validate if the designated constraints aren't met.
type AssertComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssertComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssertComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssertComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssertComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssertComponentValidationError) ErrorName() string { return "AssertComponentValidationError" }

// Error satisfies the builtin error interface
func (e AssertComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssertComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssertComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssertComponentValidationError{}

// Validate checks the field values on PoolAccountComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PoolAccountComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PoolAccountComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PoolAccountComponentMultiError, or nil if none found.
func (m *PoolAccountComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetProductType() < 0 {
		err := PoolAccountComponentValidationError{
			field:  "ProductType",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCondition() == nil {
		err := PoolAccountComponentValidationError{
			field:  "Condition",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PoolAccountComponentValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PoolAccountComponentValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PoolAccountComponentValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetExports()) < 2 {
		err := PoolAccountComponentValidationError{
			field:  "Exports",
			reason: "value must contain at least 2 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetExports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PoolAccountComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PoolAccountComponentValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PoolAccountComponentValidationError{
					field:  fmt.Sprintf("Exports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PoolAccountComponentMultiError(errors)
	}

	return nil
}

// PoolAccountComponentMultiError is an error wrapping multiple validation
// errors returned by PoolAccountComponent.ValidateAll() if the designated
// constraints aren't met.
type PoolAccountComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponentMultiError) AllErrors() []error { return m }

// PoolAccountComponentValidationError is the validation error returned by
// PoolAccountComponent.Validate if the designated constraints aren't met.
type PoolAccountComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponentValidationError) ErrorName() string {
	return "PoolAccountComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponentValidationError{}

// Validate checks the field values on DataProcessingComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataProcessingComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataProcessingComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataProcessingComponentMultiError, or nil if none found.
func (m *DataProcessingComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *DataProcessingComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetProcesses()) > 0 {

		for idx, item := range m.GetProcesses() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, DataProcessingComponentValidationError{
							field:  fmt.Sprintf("Processes[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, DataProcessingComponentValidationError{
							field:  fmt.Sprintf("Processes[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return DataProcessingComponentValidationError{
						field:  fmt.Sprintf("Processes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return DataProcessingComponentMultiError(errors)
	}

	return nil
}

// DataProcessingComponentMultiError is an error wrapping multiple validation
// errors returned by DataProcessingComponent.ValidateAll() if the designated
// constraints aren't met.
type DataProcessingComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataProcessingComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataProcessingComponentMultiError) AllErrors() []error { return m }

// DataProcessingComponentValidationError is the validation error returned by
// DataProcessingComponent.Validate if the designated constraints aren't met.
type DataProcessingComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataProcessingComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataProcessingComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataProcessingComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataProcessingComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataProcessingComponentValidationError) ErrorName() string {
	return "DataProcessingComponentValidationError"
}

// Error satisfies the builtin error interface
func (e DataProcessingComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataProcessingComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataProcessingComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataProcessingComponentValidationError{}

// Validate checks the field values on DataDrivenComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataDrivenComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataDrivenComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataDrivenComponentMultiError, or nil if none found.
func (m *DataDrivenComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *DataDrivenComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DataDrivenComponentMultiError(errors)
	}

	return nil
}

// DataDrivenComponentMultiError is an error wrapping multiple validation
// errors returned by DataDrivenComponent.ValidateAll() if the designated
// constraints aren't met.
type DataDrivenComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataDrivenComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataDrivenComponentMultiError) AllErrors() []error { return m }

// DataDrivenComponentValidationError is the validation error returned by
// DataDrivenComponent.Validate if the designated constraints aren't met.
type DataDrivenComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataDrivenComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataDrivenComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataDrivenComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataDrivenComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataDrivenComponentValidationError) ErrorName() string {
	return "DataDrivenComponentValidationError"
}

// Error satisfies the builtin error interface
func (e DataDrivenComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataDrivenComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataDrivenComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataDrivenComponentValidationError{}

// Validate checks the field values on SqlExecutionComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SqlExecutionComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SqlExecutionComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SqlExecutionComponentMultiError, or nil if none found.
func (m *SqlExecutionComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *SqlExecutionComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetSqls()) > 0 {

		for idx, item := range m.GetSqls() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, SqlExecutionComponentValidationError{
							field:  fmt.Sprintf("Sqls[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, SqlExecutionComponentValidationError{
							field:  fmt.Sprintf("Sqls[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return SqlExecutionComponentValidationError{
						field:  fmt.Sprintf("Sqls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return SqlExecutionComponentMultiError(errors)
	}

	return nil
}

// SqlExecutionComponentMultiError is an error wrapping multiple validation
// errors returned by SqlExecutionComponent.ValidateAll() if the designated
// constraints aren't met.
type SqlExecutionComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SqlExecutionComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SqlExecutionComponentMultiError) AllErrors() []error { return m }

// SqlExecutionComponentValidationError is the validation error returned by
// SqlExecutionComponent.Validate if the designated constraints aren't met.
type SqlExecutionComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SqlExecutionComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SqlExecutionComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SqlExecutionComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SqlExecutionComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SqlExecutionComponentValidationError) ErrorName() string {
	return "SqlExecutionComponentValidationError"
}

// Error satisfies the builtin error interface
func (e SqlExecutionComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSqlExecutionComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SqlExecutionComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SqlExecutionComponentValidationError{}

// Validate checks the field values on VariableFunction_Parameter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VariableFunction_Parameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VariableFunction_Parameter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VariableFunction_ParameterMultiError, or nil if none found.
func (m *VariableFunction_Parameter) ValidateAll() error {
	return m.validate(true)
}

func (m *VariableFunction_Parameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := VariableFunction_ParameterValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := _VariableFunction_Parameter_Source_NotInLookup[m.GetSource()]; ok {
		err := VariableFunction_ParameterValidationError{
			field:  "Source",
			reason: "value must not be in list [FUNCTION]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := VariableFunction_ParameterValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VariableFunction_ParameterValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VariableFunction_ParameterValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VariableFunction_ParameterValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := VariableFunction_ParameterValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VariableFunction_ParameterValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VariableFunction_ParameterValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VariableFunction_ParameterValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := VariableFunction_ParameterValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VariableFunction_ParameterValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VariableFunction_ParameterValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VariableFunction_ParameterValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// skipping validation for function

	if len(errors) > 0 {
		return VariableFunction_ParameterMultiError(errors)
	}

	return nil
}

// VariableFunction_ParameterMultiError is an error wrapping multiple
// validation errors returned by VariableFunction_Parameter.ValidateAll() if
// the designated constraints aren't met.
type VariableFunction_ParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VariableFunction_ParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VariableFunction_ParameterMultiError) AllErrors() []error { return m }

// VariableFunction_ParameterValidationError is the validation error returned
// by VariableFunction_Parameter.Validate if the designated constraints aren't met.
type VariableFunction_ParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VariableFunction_ParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VariableFunction_ParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VariableFunction_ParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VariableFunction_ParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VariableFunction_ParameterValidationError) ErrorName() string {
	return "VariableFunction_ParameterValidationError"
}

// Error satisfies the builtin error interface
func (e VariableFunction_ParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVariableFunction_Parameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VariableFunction_ParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VariableFunction_ParameterValidationError{}

var _VariableFunction_Parameter_Source_NotInLookup = map[VariableSource]struct{}{
	3: {},
}

// Validate checks the field values on LoopComponent_For with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LoopComponent_For) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoopComponent_For with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoopComponent_ForMultiError, or nil if none found.
func (m *LoopComponent_For) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent_For) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTimes() <= 0 {
		err := LoopComponent_ForValidationError{
			field:  "Times",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return LoopComponent_ForMultiError(errors)
	}

	return nil
}

// LoopComponent_ForMultiError is an error wrapping multiple validation errors
// returned by LoopComponent_For.ValidateAll() if the designated constraints
// aren't met.
type LoopComponent_ForMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponent_ForMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponent_ForMultiError) AllErrors() []error { return m }

// LoopComponent_ForValidationError is the validation error returned by
// LoopComponent_For.Validate if the designated constraints aren't met.
type LoopComponent_ForValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponent_ForValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponent_ForValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponent_ForValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponent_ForValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponent_ForValidationError) ErrorName() string {
	return "LoopComponent_ForValidationError"
}

// Error satisfies the builtin error interface
func (e LoopComponent_ForValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent_For.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponent_ForValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponent_ForValidationError{}

// Validate checks the field values on LoopComponent_ForEach with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoopComponent_ForEach) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoopComponent_ForEach with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoopComponent_ForEachMultiError, or nil if none found.
func (m *LoopComponent_ForEach) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent_ForEach) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := LoopComponent_ForEachValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := LoopComponent_ForEachValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := LoopComponent_ForEachValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_ForEachValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := LoopComponent_ForEachValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_ForEachValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := LoopComponent_ForEachValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_ForEachValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_ForEachValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_ForEachValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoopComponent_ForEachMultiError(errors)
	}

	return nil
}

// LoopComponent_ForEachMultiError is an error wrapping multiple validation
// errors returned by LoopComponent_ForEach.ValidateAll() if the designated
// constraints aren't met.
type LoopComponent_ForEachMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponent_ForEachMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponent_ForEachMultiError) AllErrors() []error { return m }

// LoopComponent_ForEachValidationError is the validation error returned by
// LoopComponent_ForEach.Validate if the designated constraints aren't met.
type LoopComponent_ForEachValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponent_ForEachValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponent_ForEachValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponent_ForEachValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponent_ForEachValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponent_ForEachValidationError) ErrorName() string {
	return "LoopComponent_ForEachValidationError"
}

// Error satisfies the builtin error interface
func (e LoopComponent_ForEachValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent_ForEach.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponent_ForEachValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponent_ForEachValidationError{}

// Validate checks the field values on LoopComponent_While with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoopComponent_While) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoopComponent_While with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoopComponent_WhileMultiError, or nil if none found.
func (m *LoopComponent_While) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent_While) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ConditionType_name[int32(m.GetType())]; !ok {
		err := LoopComponent_WhileValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSingle() == nil {
		err := LoopComponent_WhileValidationError{
			field:  "Single",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSingle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_WhileValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_WhileValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSingle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_WhileValidationError{
				field:  "Single",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetGroup() == nil {
		err := LoopComponent_WhileValidationError{
			field:  "Group",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_WhileValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_WhileValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_WhileValidationError{
				field:  "Group",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoopComponent_WhileMultiError(errors)
	}

	return nil
}

// LoopComponent_WhileMultiError is an error wrapping multiple validation
// errors returned by LoopComponent_While.ValidateAll() if the designated
// constraints aren't met.
type LoopComponent_WhileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponent_WhileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponent_WhileMultiError) AllErrors() []error { return m }

// LoopComponent_WhileValidationError is the validation error returned by
// LoopComponent_While.Validate if the designated constraints aren't met.
type LoopComponent_WhileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponent_WhileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponent_WhileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponent_WhileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponent_WhileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponent_WhileValidationError) ErrorName() string {
	return "LoopComponent_WhileValidationError"
}

// Error satisfies the builtin error interface
func (e LoopComponent_WhileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent_While.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponent_WhileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponent_WhileValidationError{}

// Validate checks the field values on LoopComponent_While_SingleCondition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoopComponent_While_SingleCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoopComponent_While_SingleCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoopComponent_While_SingleConditionMultiError, or nil if none found.
func (m *LoopComponent_While_SingleCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent_While_SingleCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetLeft() == nil {
		err := LoopComponent_While_SingleConditionValidationError{
			field:  "Left",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetLeft()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_While_SingleConditionValidationError{
					field:  "Left",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_While_SingleConditionValidationError{
					field:  "Left",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeft()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_While_SingleConditionValidationError{
				field:  "Left",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _LoopComponent_While_SingleCondition_Compare_InLookup[m.GetCompare()]; !ok {
		err := LoopComponent_While_SingleConditionValidationError{
			field:  "Compare",
			reason: "value must be in list [EQ NE LT LE GT GE CONTAINS NOT_CONTAINS RE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRight() == nil {
		err := LoopComponent_While_SingleConditionValidationError{
			field:  "Right",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRight()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_While_SingleConditionValidationError{
					field:  "Right",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_While_SingleConditionValidationError{
					field:  "Right",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRight()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_While_SingleConditionValidationError{
				field:  "Right",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoopComponent_While_SingleConditionMultiError(errors)
	}

	return nil
}

// LoopComponent_While_SingleConditionMultiError is an error wrapping multiple
// validation errors returned by
// LoopComponent_While_SingleCondition.ValidateAll() if the designated
// constraints aren't met.
type LoopComponent_While_SingleConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponent_While_SingleConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponent_While_SingleConditionMultiError) AllErrors() []error { return m }

// LoopComponent_While_SingleConditionValidationError is the validation error
// returned by LoopComponent_While_SingleCondition.Validate if the designated
// constraints aren't met.
type LoopComponent_While_SingleConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponent_While_SingleConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponent_While_SingleConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponent_While_SingleConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponent_While_SingleConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponent_While_SingleConditionValidationError) ErrorName() string {
	return "LoopComponent_While_SingleConditionValidationError"
}

// Error satisfies the builtin error interface
func (e LoopComponent_While_SingleConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent_While_SingleCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponent_While_SingleConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponent_While_SingleConditionValidationError{}

var _LoopComponent_While_SingleCondition_Compare_InLookup = map[string]struct{}{
	"EQ":           {},
	"NE":           {},
	"LT":           {},
	"LE":           {},
	"GT":           {},
	"GE":           {},
	"CONTAINS":     {},
	"NOT_CONTAINS": {},
	"RE":           {},
}

// Validate checks the field values on LoopComponent_While_GroupCondition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoopComponent_While_GroupCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoopComponent_While_GroupCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoopComponent_While_GroupConditionMultiError, or nil if none found.
func (m *LoopComponent_While_GroupCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent_While_GroupCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := Relationship_name[int32(m.GetRelationship())]; !ok {
		err := LoopComponent_While_GroupConditionValidationError{
			field:  "Relationship",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetConditions()) > 0 {

		for idx, item := range m.GetConditions() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, LoopComponent_While_GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, LoopComponent_While_GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return LoopComponent_While_GroupConditionValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return LoopComponent_While_GroupConditionMultiError(errors)
	}

	return nil
}

// LoopComponent_While_GroupConditionMultiError is an error wrapping multiple
// validation errors returned by
// LoopComponent_While_GroupCondition.ValidateAll() if the designated
// constraints aren't met.
type LoopComponent_While_GroupConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponent_While_GroupConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponent_While_GroupConditionMultiError) AllErrors() []error { return m }

// LoopComponent_While_GroupConditionValidationError is the validation error
// returned by LoopComponent_While_GroupCondition.Validate if the designated
// constraints aren't met.
type LoopComponent_While_GroupConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponent_While_GroupConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponent_While_GroupConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponent_While_GroupConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponent_While_GroupConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponent_While_GroupConditionValidationError) ErrorName() string {
	return "LoopComponent_While_GroupConditionValidationError"
}

// Error satisfies the builtin error interface
func (e LoopComponent_While_GroupConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent_While_GroupCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponent_While_GroupConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponent_While_GroupConditionValidationError{}

// Validate checks the field values on
// LoopComponent_While_SingleCondition_Value with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoopComponent_While_SingleCondition_Value) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoopComponent_While_SingleCondition_Value with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LoopComponent_While_SingleCondition_ValueMultiError, or nil if none found.
func (m *LoopComponent_While_SingleCondition_Value) ValidateAll() error {
	return m.validate(true)
}

func (m *LoopComponent_While_SingleCondition_Value) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := LoopComponent_While_SingleCondition_ValueValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := LoopComponent_While_SingleCondition_ValueValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := LoopComponent_While_SingleCondition_ValueValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_While_SingleCondition_ValueValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := LoopComponent_While_SingleCondition_ValueValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_While_SingleCondition_ValueValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := LoopComponent_While_SingleCondition_ValueValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_While_SingleCondition_ValueValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoopComponent_While_SingleCondition_ValueValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoopComponent_While_SingleCondition_ValueValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoopComponent_While_SingleCondition_ValueMultiError(errors)
	}

	return nil
}

// LoopComponent_While_SingleCondition_ValueMultiError is an error wrapping
// multiple validation errors returned by
// LoopComponent_While_SingleCondition_Value.ValidateAll() if the designated
// constraints aren't met.
type LoopComponent_While_SingleCondition_ValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoopComponent_While_SingleCondition_ValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoopComponent_While_SingleCondition_ValueMultiError) AllErrors() []error { return m }

// LoopComponent_While_SingleCondition_ValueValidationError is the validation
// error returned by LoopComponent_While_SingleCondition_Value.Validate if the
// designated constraints aren't met.
type LoopComponent_While_SingleCondition_ValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoopComponent_While_SingleCondition_ValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoopComponent_While_SingleCondition_ValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoopComponent_While_SingleCondition_ValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoopComponent_While_SingleCondition_ValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoopComponent_While_SingleCondition_ValueValidationError) ErrorName() string {
	return "LoopComponent_While_SingleCondition_ValueValidationError"
}

// Error satisfies the builtin error interface
func (e LoopComponent_While_SingleCondition_ValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoopComponent_While_SingleCondition_Value.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoopComponent_While_SingleCondition_ValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoopComponent_While_SingleCondition_ValueValidationError{}

// Validate checks the field values on HttpRequestComponent_Authorization with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HttpRequestComponent_Authorization) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Authorization
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_AuthorizationMultiError, or nil if none found.
func (m *HttpRequestComponent_Authorization) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Authorization) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := HttpRequestComponent_Authorization_Type_name[int32(m.GetType())]; !ok {
		err := HttpRequestComponent_AuthorizationValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetApiKey() == nil {
		err := HttpRequestComponent_AuthorizationValidationError{
			field:  "ApiKey",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetApiKey()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_AuthorizationValidationError{
					field:  "ApiKey",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_AuthorizationValidationError{
					field:  "ApiKey",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApiKey()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_AuthorizationValidationError{
				field:  "ApiKey",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetBearerToken() == nil {
		err := HttpRequestComponent_AuthorizationValidationError{
			field:  "BearerToken",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBearerToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_AuthorizationValidationError{
					field:  "BearerToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_AuthorizationValidationError{
					field:  "BearerToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBearerToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_AuthorizationValidationError{
				field:  "BearerToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetBasicAuth() == nil {
		err := HttpRequestComponent_AuthorizationValidationError{
			field:  "BasicAuth",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBasicAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_AuthorizationValidationError{
					field:  "BasicAuth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_AuthorizationValidationError{
					field:  "BasicAuth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_AuthorizationValidationError{
				field:  "BasicAuth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HttpRequestComponent_AuthorizationMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_AuthorizationMultiError is an error wrapping multiple
// validation errors returned by
// HttpRequestComponent_Authorization.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_AuthorizationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_AuthorizationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_AuthorizationMultiError) AllErrors() []error { return m }

// HttpRequestComponent_AuthorizationValidationError is the validation error
// returned by HttpRequestComponent_Authorization.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_AuthorizationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_AuthorizationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_AuthorizationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_AuthorizationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_AuthorizationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_AuthorizationValidationError) ErrorName() string {
	return "HttpRequestComponent_AuthorizationValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_AuthorizationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Authorization.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_AuthorizationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_AuthorizationValidationError{}

// Validate checks the field values on HttpRequestComponent_KeyValueDesc with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HttpRequestComponent_KeyValueDesc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_KeyValueDesc
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_KeyValueDescMultiError, or nil if none found.
func (m *HttpRequestComponent_KeyValueDesc) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_KeyValueDesc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKey() != "" {

		if utf8.RuneCountInString(m.GetKey()) < 1 {
			err := HttpRequestComponent_KeyValueDescValidationError{
				field:  "Key",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := HttpRequestComponent_KeyValueDescValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	if len(errors) > 0 {
		return HttpRequestComponent_KeyValueDescMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_KeyValueDescMultiError is an error wrapping multiple
// validation errors returned by
// HttpRequestComponent_KeyValueDesc.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_KeyValueDescMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_KeyValueDescMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_KeyValueDescMultiError) AllErrors() []error { return m }

// HttpRequestComponent_KeyValueDescValidationError is the validation error
// returned by HttpRequestComponent_KeyValueDesc.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_KeyValueDescValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_KeyValueDescValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_KeyValueDescValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_KeyValueDescValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_KeyValueDescValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_KeyValueDescValidationError) ErrorName() string {
	return "HttpRequestComponent_KeyValueDescValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_KeyValueDescValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_KeyValueDesc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_KeyValueDescValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_KeyValueDescValidationError{}

// Validate checks the field values on HttpRequestComponent_Body with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Body) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Body with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_BodyMultiError, or nil if none found.
func (m *HttpRequestComponent_Body) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Body) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := HttpRequestComponent_Body_ContentType_name[int32(m.GetType())]; !ok {
		err := HttpRequestComponent_BodyValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFormData()) > 0 {

		for idx, item := range m.GetFormData() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HttpRequestComponent_BodyValidationError{
							field:  fmt.Sprintf("FormData[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HttpRequestComponent_BodyValidationError{
							field:  fmt.Sprintf("FormData[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HttpRequestComponent_BodyValidationError{
						field:  fmt.Sprintf("FormData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	// no validation rules for Raw

	if len(errors) > 0 {
		return HttpRequestComponent_BodyMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_BodyMultiError is an error wrapping multiple validation
// errors returned by HttpRequestComponent_Body.ValidateAll() if the
// designated constraints aren't met.
type HttpRequestComponent_BodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_BodyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_BodyMultiError) AllErrors() []error { return m }

// HttpRequestComponent_BodyValidationError is the validation error returned by
// HttpRequestComponent_Body.Validate if the designated constraints aren't met.
type HttpRequestComponent_BodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_BodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_BodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_BodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_BodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_BodyValidationError) ErrorName() string {
	return "HttpRequestComponent_BodyValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_BodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Body.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_BodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_BodyValidationError{}

// Validate checks the field values on HttpRequestComponent_Timeout with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Timeout) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Timeout with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_TimeoutMultiError, or nil if none found.
func (m *HttpRequestComponent_Timeout) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Timeout) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetConnectTimeout() < 0 {
		err := HttpRequestComponent_TimeoutValidationError{
			field:  "ConnectTimeout",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRequestTimeout() < 0 {
		err := HttpRequestComponent_TimeoutValidationError{
			field:  "RequestTimeout",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetResponseTimeout() < 0 {
		err := HttpRequestComponent_TimeoutValidationError{
			field:  "ResponseTimeout",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return HttpRequestComponent_TimeoutMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_TimeoutMultiError is an error wrapping multiple
// validation errors returned by HttpRequestComponent_Timeout.ValidateAll() if
// the designated constraints aren't met.
type HttpRequestComponent_TimeoutMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_TimeoutMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_TimeoutMultiError) AllErrors() []error { return m }

// HttpRequestComponent_TimeoutValidationError is the validation error returned
// by HttpRequestComponent_Timeout.Validate if the designated constraints
// aren't met.
type HttpRequestComponent_TimeoutValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_TimeoutValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_TimeoutValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_TimeoutValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_TimeoutValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_TimeoutValidationError) ErrorName() string {
	return "HttpRequestComponent_TimeoutValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_TimeoutValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Timeout.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_TimeoutValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_TimeoutValidationError{}

// Validate checks the field values on HttpRequestComponent_Assertion with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Assertion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Assertion with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_AssertionMultiError, or nil if none found.
func (m *HttpRequestComponent_Assertion) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Assertion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ResponseSource_name[int32(m.GetSource())]; !ok {
		err := HttpRequestComponent_AssertionValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHeaders() == nil {
		err := HttpRequestComponent_AssertionValidationError{
			field:  "Headers",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeaders()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_AssertionValidationError{
					field:  "Headers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_AssertionValidationError{
					field:  "Headers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaders()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_AssertionValidationError{
				field:  "Headers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetBody() == nil {
		err := HttpRequestComponent_AssertionValidationError{
			field:  "Body",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_AssertionValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_AssertionValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_AssertionValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetStatusCode() == nil {
		err := HttpRequestComponent_AssertionValidationError{
			field:  "StatusCode",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStatusCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_AssertionValidationError{
					field:  "StatusCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_AssertionValidationError{
					field:  "StatusCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_AssertionValidationError{
				field:  "StatusCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HttpRequestComponent_AssertionMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_AssertionMultiError is an error wrapping multiple
// validation errors returned by HttpRequestComponent_Assertion.ValidateAll()
// if the designated constraints aren't met.
type HttpRequestComponent_AssertionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_AssertionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_AssertionMultiError) AllErrors() []error { return m }

// HttpRequestComponent_AssertionValidationError is the validation error
// returned by HttpRequestComponent_Assertion.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_AssertionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_AssertionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_AssertionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_AssertionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_AssertionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_AssertionValidationError) ErrorName() string {
	return "HttpRequestComponent_AssertionValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_AssertionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Assertion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_AssertionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_AssertionValidationError{}

// Validate checks the field values on HttpRequestComponent_Export with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Export) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Export with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_ExportMultiError, or nil if none found.
func (m *HttpRequestComponent_Export) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Export) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := HttpRequestComponent_ExportValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := ResponseSource_name[int32(m.GetSource())]; !ok {
		err := HttpRequestComponent_ExportValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHeaders() == nil {
		err := HttpRequestComponent_ExportValidationError{
			field:  "Headers",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeaders()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_ExportValidationError{
					field:  "Headers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_ExportValidationError{
					field:  "Headers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaders()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_ExportValidationError{
				field:  "Headers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetBody() == nil {
		err := HttpRequestComponent_ExportValidationError{
			field:  "Body",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_ExportValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_ExportValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_ExportValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HttpRequestComponent_ExportMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_ExportMultiError is an error wrapping multiple
// validation errors returned by HttpRequestComponent_Export.ValidateAll() if
// the designated constraints aren't met.
type HttpRequestComponent_ExportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_ExportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_ExportMultiError) AllErrors() []error { return m }

// HttpRequestComponent_ExportValidationError is the validation error returned
// by HttpRequestComponent_Export.Validate if the designated constraints
// aren't met.
type HttpRequestComponent_ExportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_ExportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_ExportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_ExportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_ExportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_ExportValidationError) ErrorName() string {
	return "HttpRequestComponent_ExportValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_ExportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Export.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_ExportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_ExportValidationError{}

// Validate checks the field values on
// HttpRequestComponent_Authorization_ApiKey with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Authorization_ApiKey) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Authorization_ApiKey with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// HttpRequestComponent_Authorization_ApiKeyMultiError, or nil if none found.
func (m *HttpRequestComponent_Authorization_ApiKey) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Authorization_ApiKey) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKey() != "" {

		if utf8.RuneCountInString(m.GetKey()) < 1 {
			err := HttpRequestComponent_Authorization_ApiKeyValidationError{
				field:  "Key",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := HttpRequestComponent_Authorization_ApiKeyValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := HttpRequestComponent_Authorization_ApiKey_AddTo_name[int32(m.GetAddTo())]; !ok {
		err := HttpRequestComponent_Authorization_ApiKeyValidationError{
			field:  "AddTo",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return HttpRequestComponent_Authorization_ApiKeyMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Authorization_ApiKeyMultiError is an error wrapping
// multiple validation errors returned by
// HttpRequestComponent_Authorization_ApiKey.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Authorization_ApiKeyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Authorization_ApiKeyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Authorization_ApiKeyMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Authorization_ApiKeyValidationError is the validation
// error returned by HttpRequestComponent_Authorization_ApiKey.Validate if the
// designated constraints aren't met.
type HttpRequestComponent_Authorization_ApiKeyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Authorization_ApiKeyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Authorization_ApiKeyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Authorization_ApiKeyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Authorization_ApiKeyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Authorization_ApiKeyValidationError) ErrorName() string {
	return "HttpRequestComponent_Authorization_ApiKeyValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Authorization_ApiKeyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Authorization_ApiKey.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Authorization_ApiKeyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Authorization_ApiKeyValidationError{}

// Validate checks the field values on
// HttpRequestComponent_Authorization_BearerToken with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Authorization_BearerToken) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Authorization_BearerToken with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// HttpRequestComponent_Authorization_BearerTokenMultiError, or nil if none found.
func (m *HttpRequestComponent_Authorization_BearerToken) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Authorization_BearerToken) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetToken() != "" {

		if utf8.RuneCountInString(m.GetToken()) < 1 {
			err := HttpRequestComponent_Authorization_BearerTokenValidationError{
				field:  "Token",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Authorization_BearerTokenMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Authorization_BearerTokenMultiError is an error
// wrapping multiple validation errors returned by
// HttpRequestComponent_Authorization_BearerToken.ValidateAll() if the
// designated constraints aren't met.
type HttpRequestComponent_Authorization_BearerTokenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Authorization_BearerTokenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Authorization_BearerTokenMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Authorization_BearerTokenValidationError is the
// validation error returned by
// HttpRequestComponent_Authorization_BearerToken.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_Authorization_BearerTokenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Authorization_BearerTokenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Authorization_BearerTokenValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e HttpRequestComponent_Authorization_BearerTokenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Authorization_BearerTokenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Authorization_BearerTokenValidationError) ErrorName() string {
	return "HttpRequestComponent_Authorization_BearerTokenValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Authorization_BearerTokenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Authorization_BearerToken.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Authorization_BearerTokenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Authorization_BearerTokenValidationError{}

// Validate checks the field values on
// HttpRequestComponent_Authorization_BasicAuth with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Authorization_BasicAuth) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Authorization_BasicAuth with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// HttpRequestComponent_Authorization_BasicAuthMultiError, or nil if none found.
func (m *HttpRequestComponent_Authorization_BasicAuth) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Authorization_BasicAuth) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUsername() != "" {

		if utf8.RuneCountInString(m.GetUsername()) < 1 {
			err := HttpRequestComponent_Authorization_BasicAuthValidationError{
				field:  "Username",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetPassword() != "" {

		if utf8.RuneCountInString(m.GetPassword()) < 1 {
			err := HttpRequestComponent_Authorization_BasicAuthValidationError{
				field:  "Password",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Authorization_BasicAuthMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Authorization_BasicAuthMultiError is an error wrapping
// multiple validation errors returned by
// HttpRequestComponent_Authorization_BasicAuth.ValidateAll() if the
// designated constraints aren't met.
type HttpRequestComponent_Authorization_BasicAuthMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Authorization_BasicAuthMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Authorization_BasicAuthMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Authorization_BasicAuthValidationError is the
// validation error returned by
// HttpRequestComponent_Authorization_BasicAuth.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_Authorization_BasicAuthValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Authorization_BasicAuthValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Authorization_BasicAuthValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Authorization_BasicAuthValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Authorization_BasicAuthValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Authorization_BasicAuthValidationError) ErrorName() string {
	return "HttpRequestComponent_Authorization_BasicAuthValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Authorization_BasicAuthValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Authorization_BasicAuth.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Authorization_BasicAuthValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Authorization_BasicAuthValidationError{}

// Validate checks the field values on HttpRequestComponent_Assertion_Headers
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *HttpRequestComponent_Assertion_Headers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Assertion_Headers with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// HttpRequestComponent_Assertion_HeadersMultiError, or nil if none found.
func (m *HttpRequestComponent_Assertion_Headers) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Assertion_Headers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKey() != "" {

		if utf8.RuneCountInString(m.GetKey()) < 1 {
			err := HttpRequestComponent_Assertion_HeadersValidationError{
				field:  "Key",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCompare() != "" {

		if _, ok := _HttpRequestComponent_Assertion_Headers_Compare_InLookup[m.GetCompare()]; !ok {
			err := HttpRequestComponent_Assertion_HeadersValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LEN_EQ LEN_NE LEN_LT LEN_LE LEN_GT LEN_GE CONTAINS NOT_CONTAINS STARTS_WITH ENDS_WITH RE]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetExpression() != "" {

		if utf8.RuneCountInString(m.GetExpression()) < 1 {
			err := HttpRequestComponent_Assertion_HeadersValidationError{
				field:  "Expression",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Assertion_HeadersMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Assertion_HeadersMultiError is an error wrapping
// multiple validation errors returned by
// HttpRequestComponent_Assertion_Headers.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Assertion_HeadersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Assertion_HeadersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Assertion_HeadersMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Assertion_HeadersValidationError is the validation
// error returned by HttpRequestComponent_Assertion_Headers.Validate if the
// designated constraints aren't met.
type HttpRequestComponent_Assertion_HeadersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Assertion_HeadersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Assertion_HeadersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Assertion_HeadersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Assertion_HeadersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Assertion_HeadersValidationError) ErrorName() string {
	return "HttpRequestComponent_Assertion_HeadersValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Assertion_HeadersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Assertion_Headers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Assertion_HeadersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Assertion_HeadersValidationError{}

var _HttpRequestComponent_Assertion_Headers_Compare_InLookup = map[string]struct{}{
	"EQ":           {},
	"NE":           {},
	"LEN_EQ":       {},
	"LEN_NE":       {},
	"LEN_LT":       {},
	"LEN_LE":       {},
	"LEN_GT":       {},
	"LEN_GE":       {},
	"CONTAINS":     {},
	"NOT_CONTAINS": {},
	"STARTS_WITH":  {},
	"ENDS_WITH":    {},
	"RE":           {},
}

// Validate checks the field values on HttpRequestComponent_Assertion_Body with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HttpRequestComponent_Assertion_Body) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Assertion_Body
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_Assertion_BodyMultiError, or nil if none found.
func (m *HttpRequestComponent_Assertion_Body) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Assertion_Body) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := HttpRequestComponent_Assertion_Body_Type_name[int32(m.GetType())]; !ok {
		err := HttpRequestComponent_Assertion_BodyValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetJmespath() == nil {
		err := HttpRequestComponent_Assertion_BodyValidationError{
			field:  "Jmespath",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetJmespath()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_Assertion_BodyValidationError{
					field:  "Jmespath",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_Assertion_BodyValidationError{
					field:  "Jmespath",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJmespath()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_Assertion_BodyValidationError{
				field:  "Jmespath",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetRegex() == nil {
		err := HttpRequestComponent_Assertion_BodyValidationError{
			field:  "Regex",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRegex()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HttpRequestComponent_Assertion_BodyValidationError{
					field:  "Regex",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HttpRequestComponent_Assertion_BodyValidationError{
					field:  "Regex",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRegex()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HttpRequestComponent_Assertion_BodyValidationError{
				field:  "Regex",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HttpRequestComponent_Assertion_BodyMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Assertion_BodyMultiError is an error wrapping multiple
// validation errors returned by
// HttpRequestComponent_Assertion_Body.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Assertion_BodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Assertion_BodyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Assertion_BodyMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Assertion_BodyValidationError is the validation error
// returned by HttpRequestComponent_Assertion_Body.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_Assertion_BodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Assertion_BodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Assertion_BodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Assertion_BodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Assertion_BodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Assertion_BodyValidationError) ErrorName() string {
	return "HttpRequestComponent_Assertion_BodyValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Assertion_BodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Assertion_Body.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Assertion_BodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Assertion_BodyValidationError{}

// Validate checks the field values on
// HttpRequestComponent_Assertion_StatusCode with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Assertion_StatusCode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Assertion_StatusCode with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// HttpRequestComponent_Assertion_StatusCodeMultiError, or nil if none found.
func (m *HttpRequestComponent_Assertion_StatusCode) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Assertion_StatusCode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompare() != "" {

		if _, ok := _HttpRequestComponent_Assertion_StatusCode_Compare_InLookup[m.GetCompare()]; !ok {
			err := HttpRequestComponent_Assertion_StatusCodeValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LT LE GT GE RE]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetExpectation() != "" {

		if utf8.RuneCountInString(m.GetExpectation()) < 1 {
			err := HttpRequestComponent_Assertion_StatusCodeValidationError{
				field:  "Expectation",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Assertion_StatusCodeMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Assertion_StatusCodeMultiError is an error wrapping
// multiple validation errors returned by
// HttpRequestComponent_Assertion_StatusCode.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Assertion_StatusCodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Assertion_StatusCodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Assertion_StatusCodeMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Assertion_StatusCodeValidationError is the validation
// error returned by HttpRequestComponent_Assertion_StatusCode.Validate if the
// designated constraints aren't met.
type HttpRequestComponent_Assertion_StatusCodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Assertion_StatusCodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Assertion_StatusCodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Assertion_StatusCodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Assertion_StatusCodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Assertion_StatusCodeValidationError) ErrorName() string {
	return "HttpRequestComponent_Assertion_StatusCodeValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Assertion_StatusCodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Assertion_StatusCode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Assertion_StatusCodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Assertion_StatusCodeValidationError{}

var _HttpRequestComponent_Assertion_StatusCode_Compare_InLookup = map[string]struct{}{
	"EQ": {},
	"NE": {},
	"LT": {},
	"LE": {},
	"GT": {},
	"GE": {},
	"RE": {},
}

// Validate checks the field values on
// HttpRequestComponent_Assertion_Body_JMESPath with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Assertion_Body_JMESPath) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Assertion_Body_JMESPath with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// HttpRequestComponent_Assertion_Body_JMESPathMultiError, or nil if none found.
func (m *HttpRequestComponent_Assertion_Body_JMESPath) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Assertion_Body_JMESPath) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetExpression() != "" {

		if utf8.RuneCountInString(m.GetExpression()) < 1 {
			err := HttpRequestComponent_Assertion_Body_JMESPathValidationError{
				field:  "Expression",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCompare() != "" {

		if _, ok := _HttpRequestComponent_Assertion_Body_JMESPath_Compare_InLookup[m.GetCompare()]; !ok {
			err := HttpRequestComponent_Assertion_Body_JMESPathValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LT LE GT GE LEN_EQ LEN_NE LEN_LT LEN_LE LEN_GT LEN_GE CONTAINS NOT_CONTAINS RE]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetExpectation() != "" {

		if utf8.RuneCountInString(m.GetExpectation()) < 1 {
			err := HttpRequestComponent_Assertion_Body_JMESPathValidationError{
				field:  "Expectation",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Assertion_Body_JMESPathMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Assertion_Body_JMESPathMultiError is an error wrapping
// multiple validation errors returned by
// HttpRequestComponent_Assertion_Body_JMESPath.ValidateAll() if the
// designated constraints aren't met.
type HttpRequestComponent_Assertion_Body_JMESPathMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Assertion_Body_JMESPathMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Assertion_Body_JMESPathMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Assertion_Body_JMESPathValidationError is the
// validation error returned by
// HttpRequestComponent_Assertion_Body_JMESPath.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_Assertion_Body_JMESPathValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Assertion_Body_JMESPathValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Assertion_Body_JMESPathValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Assertion_Body_JMESPathValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Assertion_Body_JMESPathValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Assertion_Body_JMESPathValidationError) ErrorName() string {
	return "HttpRequestComponent_Assertion_Body_JMESPathValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Assertion_Body_JMESPathValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Assertion_Body_JMESPath.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Assertion_Body_JMESPathValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Assertion_Body_JMESPathValidationError{}

var _HttpRequestComponent_Assertion_Body_JMESPath_Compare_InLookup = map[string]struct{}{
	"EQ":           {},
	"NE":           {},
	"LT":           {},
	"LE":           {},
	"GT":           {},
	"GE":           {},
	"LEN_EQ":       {},
	"LEN_NE":       {},
	"LEN_LT":       {},
	"LEN_LE":       {},
	"LEN_GT":       {},
	"LEN_GE":       {},
	"CONTAINS":     {},
	"NOT_CONTAINS": {},
	"RE":           {},
}

// Validate checks the field values on
// HttpRequestComponent_Assertion_Body_Regex with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HttpRequestComponent_Assertion_Body_Regex) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HttpRequestComponent_Assertion_Body_Regex with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// HttpRequestComponent_Assertion_Body_RegexMultiError, or nil if none found.
func (m *HttpRequestComponent_Assertion_Body_Regex) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Assertion_Body_Regex) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetExpression() != "" {

		if utf8.RuneCountInString(m.GetExpression()) < 1 {
			err := HttpRequestComponent_Assertion_Body_RegexValidationError{
				field:  "Expression",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Assertion_Body_RegexMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Assertion_Body_RegexMultiError is an error wrapping
// multiple validation errors returned by
// HttpRequestComponent_Assertion_Body_Regex.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Assertion_Body_RegexMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Assertion_Body_RegexMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Assertion_Body_RegexMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Assertion_Body_RegexValidationError is the validation
// error returned by HttpRequestComponent_Assertion_Body_Regex.Validate if the
// designated constraints aren't met.
type HttpRequestComponent_Assertion_Body_RegexValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Assertion_Body_RegexValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Assertion_Body_RegexValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Assertion_Body_RegexValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Assertion_Body_RegexValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Assertion_Body_RegexValidationError) ErrorName() string {
	return "HttpRequestComponent_Assertion_Body_RegexValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Assertion_Body_RegexValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Assertion_Body_Regex.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Assertion_Body_RegexValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Assertion_Body_RegexValidationError{}

// Validate checks the field values on HttpRequestComponent_Export_Headers with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HttpRequestComponent_Export_Headers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Export_Headers
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_Export_HeadersMultiError, or nil if none found.
func (m *HttpRequestComponent_Export_Headers) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Export_Headers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKey() != "" {

		if utf8.RuneCountInString(m.GetKey()) < 1 {
			err := HttpRequestComponent_Export_HeadersValidationError{
				field:  "Key",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Export_HeadersMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Export_HeadersMultiError is an error wrapping multiple
// validation errors returned by
// HttpRequestComponent_Export_Headers.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Export_HeadersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Export_HeadersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Export_HeadersMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Export_HeadersValidationError is the validation error
// returned by HttpRequestComponent_Export_Headers.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_Export_HeadersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Export_HeadersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Export_HeadersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Export_HeadersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Export_HeadersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Export_HeadersValidationError) ErrorName() string {
	return "HttpRequestComponent_Export_HeadersValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Export_HeadersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Export_Headers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Export_HeadersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Export_HeadersValidationError{}

// Validate checks the field values on HttpRequestComponent_Export_Body with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HttpRequestComponent_Export_Body) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HttpRequestComponent_Export_Body with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HttpRequestComponent_Export_BodyMultiError, or nil if none found.
func (m *HttpRequestComponent_Export_Body) ValidateAll() error {
	return m.validate(true)
}

func (m *HttpRequestComponent_Export_Body) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := HttpRequestComponent_Export_Body_Type_name[int32(m.GetType())]; !ok {
		err := HttpRequestComponent_Export_BodyValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetExpression() != "" {

		if utf8.RuneCountInString(m.GetExpression()) < 1 {
			err := HttpRequestComponent_Export_BodyValidationError{
				field:  "Expression",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HttpRequestComponent_Export_BodyMultiError(errors)
	}

	return nil
}

// HttpRequestComponent_Export_BodyMultiError is an error wrapping multiple
// validation errors returned by
// HttpRequestComponent_Export_Body.ValidateAll() if the designated
// constraints aren't met.
type HttpRequestComponent_Export_BodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HttpRequestComponent_Export_BodyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HttpRequestComponent_Export_BodyMultiError) AllErrors() []error { return m }

// HttpRequestComponent_Export_BodyValidationError is the validation error
// returned by HttpRequestComponent_Export_Body.Validate if the designated
// constraints aren't met.
type HttpRequestComponent_Export_BodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HttpRequestComponent_Export_BodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HttpRequestComponent_Export_BodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HttpRequestComponent_Export_BodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HttpRequestComponent_Export_BodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HttpRequestComponent_Export_BodyValidationError) ErrorName() string {
	return "HttpRequestComponent_Export_BodyValidationError"
}

// Error satisfies the builtin error interface
func (e HttpRequestComponent_Export_BodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHttpRequestComponent_Export_Body.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HttpRequestComponent_Export_BodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HttpRequestComponent_Export_BodyValidationError{}

// Validate checks the field values on ConditionComponent_SingleCondition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConditionComponent_SingleCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConditionComponent_SingleCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConditionComponent_SingleConditionMultiError, or nil if none found.
func (m *ConditionComponent_SingleCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionComponent_SingleCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetLeft() == nil {
		err := ConditionComponent_SingleConditionValidationError{
			field:  "Left",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetLeft()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponent_SingleConditionValidationError{
					field:  "Left",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponent_SingleConditionValidationError{
					field:  "Left",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeft()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponent_SingleConditionValidationError{
				field:  "Left",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCompare() != "" {

		if _, ok := _ConditionComponent_SingleCondition_Compare_InLookup[m.GetCompare()]; !ok {
			err := ConditionComponent_SingleConditionValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LT LE GT GE LEN_EQ LEN_NE LEN_LT LEN_LE LEN_GT LEN_GE CONTAINS NOT_CONTAINS RE]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetRight() == nil {
		err := ConditionComponent_SingleConditionValidationError{
			field:  "Right",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRight()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponent_SingleConditionValidationError{
					field:  "Right",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponent_SingleConditionValidationError{
					field:  "Right",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRight()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponent_SingleConditionValidationError{
				field:  "Right",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConditionComponent_SingleConditionMultiError(errors)
	}

	return nil
}

// ConditionComponent_SingleConditionMultiError is an error wrapping multiple
// validation errors returned by
// ConditionComponent_SingleCondition.ValidateAll() if the designated
// constraints aren't met.
type ConditionComponent_SingleConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionComponent_SingleConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionComponent_SingleConditionMultiError) AllErrors() []error { return m }

// ConditionComponent_SingleConditionValidationError is the validation error
// returned by ConditionComponent_SingleCondition.Validate if the designated
// constraints aren't met.
type ConditionComponent_SingleConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionComponent_SingleConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionComponent_SingleConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionComponent_SingleConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionComponent_SingleConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionComponent_SingleConditionValidationError) ErrorName() string {
	return "ConditionComponent_SingleConditionValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionComponent_SingleConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionComponent_SingleCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionComponent_SingleConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionComponent_SingleConditionValidationError{}

var _ConditionComponent_SingleCondition_Compare_InLookup = map[string]struct{}{
	"EQ":           {},
	"NE":           {},
	"LT":           {},
	"LE":           {},
	"GT":           {},
	"GE":           {},
	"LEN_EQ":       {},
	"LEN_NE":       {},
	"LEN_LT":       {},
	"LEN_LE":       {},
	"LEN_GT":       {},
	"LEN_GE":       {},
	"CONTAINS":     {},
	"NOT_CONTAINS": {},
	"RE":           {},
}

// Validate checks the field values on ConditionComponent_GroupCondition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConditionComponent_GroupCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConditionComponent_GroupCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConditionComponent_GroupConditionMultiError, or nil if none found.
func (m *ConditionComponent_GroupCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionComponent_GroupCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := Relationship_name[int32(m.GetRelationship())]; !ok {
		err := ConditionComponent_GroupConditionValidationError{
			field:  "Relationship",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetConditions()) > 0 {

		for idx, item := range m.GetConditions() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ConditionComponent_GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ConditionComponent_GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ConditionComponent_GroupConditionValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return ConditionComponent_GroupConditionMultiError(errors)
	}

	return nil
}

// ConditionComponent_GroupConditionMultiError is an error wrapping multiple
// validation errors returned by
// ConditionComponent_GroupCondition.ValidateAll() if the designated
// constraints aren't met.
type ConditionComponent_GroupConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionComponent_GroupConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionComponent_GroupConditionMultiError) AllErrors() []error { return m }

// ConditionComponent_GroupConditionValidationError is the validation error
// returned by ConditionComponent_GroupCondition.Validate if the designated
// constraints aren't met.
type ConditionComponent_GroupConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionComponent_GroupConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionComponent_GroupConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionComponent_GroupConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionComponent_GroupConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionComponent_GroupConditionValidationError) ErrorName() string {
	return "ConditionComponent_GroupConditionValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionComponent_GroupConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionComponent_GroupCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionComponent_GroupConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionComponent_GroupConditionValidationError{}

// Validate checks the field values on ConditionComponent_SingleCondition_Value
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ConditionComponent_SingleCondition_Value) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConditionComponent_SingleCondition_Value with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConditionComponent_SingleCondition_ValueMultiError, or nil if none found.
func (m *ConditionComponent_SingleCondition_Value) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionComponent_SingleCondition_Value) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := ConditionComponent_SingleCondition_ValueValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := ConditionComponent_SingleCondition_ValueValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponent_SingleCondition_ValueValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := ConditionComponent_SingleCondition_ValueValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponent_SingleCondition_ValueValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := ConditionComponent_SingleCondition_ValueValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponent_SingleCondition_ValueValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionComponent_SingleCondition_ValueValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionComponent_SingleCondition_ValueValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConditionComponent_SingleCondition_ValueMultiError(errors)
	}

	return nil
}

// ConditionComponent_SingleCondition_ValueMultiError is an error wrapping
// multiple validation errors returned by
// ConditionComponent_SingleCondition_Value.ValidateAll() if the designated
// constraints aren't met.
type ConditionComponent_SingleCondition_ValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionComponent_SingleCondition_ValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionComponent_SingleCondition_ValueMultiError) AllErrors() []error { return m }

// ConditionComponent_SingleCondition_ValueValidationError is the validation
// error returned by ConditionComponent_SingleCondition_Value.Validate if the
// designated constraints aren't met.
type ConditionComponent_SingleCondition_ValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionComponent_SingleCondition_ValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionComponent_SingleCondition_ValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionComponent_SingleCondition_ValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionComponent_SingleCondition_ValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionComponent_SingleCondition_ValueValidationError) ErrorName() string {
	return "ConditionComponent_SingleCondition_ValueValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionComponent_SingleCondition_ValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionComponent_SingleCondition_Value.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionComponent_SingleCondition_ValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionComponent_SingleCondition_ValueValidationError{}

// Validate checks the field values on WaitComponent_Sleep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WaitComponent_Sleep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WaitComponent_Sleep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WaitComponent_SleepMultiError, or nil if none found.
func (m *WaitComponent_Sleep) ValidateAll() error {
	return m.validate(true)
}

func (m *WaitComponent_Sleep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTime() < 0 {
		err := WaitComponent_SleepValidationError{
			field:  "Time",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return WaitComponent_SleepMultiError(errors)
	}

	return nil
}

// WaitComponent_SleepMultiError is an error wrapping multiple validation
// errors returned by WaitComponent_Sleep.ValidateAll() if the designated
// constraints aren't met.
type WaitComponent_SleepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WaitComponent_SleepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WaitComponent_SleepMultiError) AllErrors() []error { return m }

// WaitComponent_SleepValidationError is the validation error returned by
// WaitComponent_Sleep.Validate if the designated constraints aren't met.
type WaitComponent_SleepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WaitComponent_SleepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WaitComponent_SleepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WaitComponent_SleepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WaitComponent_SleepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WaitComponent_SleepValidationError) ErrorName() string {
	return "WaitComponent_SleepValidationError"
}

// Error satisfies the builtin error interface
func (e WaitComponent_SleepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWaitComponent_Sleep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WaitComponent_SleepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WaitComponent_SleepValidationError{}

// Validate checks the field values on WaitComponent_Manual with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WaitComponent_Manual) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WaitComponent_Manual with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WaitComponent_ManualMultiError, or nil if none found.
func (m *WaitComponent_Manual) ValidateAll() error {
	return m.validate(true)
}

func (m *WaitComponent_Manual) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMaxWaitingTime() < 0 {
		err := WaitComponent_ManualValidationError{
			field:  "MaxWaitingTime",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := WaitComponent_Manual_Action_name[int32(m.GetDefaultAction())]; !ok {
		err := WaitComponent_ManualValidationError{
			field:  "DefaultAction",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return WaitComponent_ManualMultiError(errors)
	}

	return nil
}

// WaitComponent_ManualMultiError is an error wrapping multiple validation
// errors returned by WaitComponent_Manual.ValidateAll() if the designated
// constraints aren't met.
type WaitComponent_ManualMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WaitComponent_ManualMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WaitComponent_ManualMultiError) AllErrors() []error { return m }

// WaitComponent_ManualValidationError is the validation error returned by
// WaitComponent_Manual.Validate if the designated constraints aren't met.
type WaitComponent_ManualValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WaitComponent_ManualValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WaitComponent_ManualValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WaitComponent_ManualValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WaitComponent_ManualValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WaitComponent_ManualValidationError) ErrorName() string {
	return "WaitComponent_ManualValidationError"
}

// Error satisfies the builtin error interface
func (e WaitComponent_ManualValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWaitComponent_Manual.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WaitComponent_ManualValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WaitComponent_ManualValidationError{}

// Validate checks the field values on AssertComponent_Actual with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssertComponent_Actual) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssertComponent_Actual with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssertComponent_ActualMultiError, or nil if none found.
func (m *AssertComponent_Actual) ValidateAll() error {
	return m.validate(true)
}

func (m *AssertComponent_Actual) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetNodeId() != "" {

		if utf8.RuneCountInString(m.GetNodeId()) < 1 {
			err := AssertComponent_ActualValidationError{
				field:  "NodeId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := AssertComponent_ActualValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return AssertComponent_ActualMultiError(errors)
	}

	return nil
}

// AssertComponent_ActualMultiError is an error wrapping multiple validation
// errors returned by AssertComponent_Actual.ValidateAll() if the designated
// constraints aren't met.
type AssertComponent_ActualMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssertComponent_ActualMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssertComponent_ActualMultiError) AllErrors() []error { return m }

// AssertComponent_ActualValidationError is the validation error returned by
// AssertComponent_Actual.Validate if the designated constraints aren't met.
type AssertComponent_ActualValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssertComponent_ActualValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssertComponent_ActualValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssertComponent_ActualValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssertComponent_ActualValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssertComponent_ActualValidationError) ErrorName() string {
	return "AssertComponent_ActualValidationError"
}

// Error satisfies the builtin error interface
func (e AssertComponent_ActualValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssertComponent_Actual.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssertComponent_ActualValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssertComponent_ActualValidationError{}

// Validate checks the field values on AssertComponent_Expected with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssertComponent_Expected) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssertComponent_Expected with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssertComponent_ExpectedMultiError, or nil if none found.
func (m *AssertComponent_Expected) ValidateAll() error {
	return m.validate(true)
}

func (m *AssertComponent_Expected) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := AssertComponent_ExpectedValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := AssertComponent_ExpectedValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssertComponent_ExpectedValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := AssertComponent_ExpectedValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssertComponent_ExpectedValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := AssertComponent_ExpectedValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssertComponent_ExpectedValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssertComponent_ExpectedValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssertComponent_ExpectedValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AssertComponent_ExpectedMultiError(errors)
	}

	return nil
}

// AssertComponent_ExpectedMultiError is an error wrapping multiple validation
// errors returned by AssertComponent_Expected.ValidateAll() if the designated
// constraints aren't met.
type AssertComponent_ExpectedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssertComponent_ExpectedMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssertComponent_ExpectedMultiError) AllErrors() []error { return m }

// AssertComponent_ExpectedValidationError is the validation error returned by
// AssertComponent_Expected.Validate if the designated constraints aren't met.
type AssertComponent_ExpectedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssertComponent_ExpectedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssertComponent_ExpectedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssertComponent_ExpectedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssertComponent_ExpectedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssertComponent_ExpectedValidationError) ErrorName() string {
	return "AssertComponent_ExpectedValidationError"
}

// Error satisfies the builtin error interface
func (e AssertComponent_ExpectedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssertComponent_Expected.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssertComponent_ExpectedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssertComponent_ExpectedValidationError{}

// Validate checks the field values on AssertComponent_Assertion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssertComponent_Assertion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssertComponent_Assertion with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssertComponent_AssertionMultiError, or nil if none found.
func (m *AssertComponent_Assertion) ValidateAll() error {
	return m.validate(true)
}

func (m *AssertComponent_Assertion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetActual() == nil {
		err := AssertComponent_AssertionValidationError{
			field:  "Actual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetActual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssertComponent_AssertionValidationError{
					field:  "Actual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssertComponent_AssertionValidationError{
					field:  "Actual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssertComponent_AssertionValidationError{
				field:  "Actual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCompare() != "" {

		if _, ok := _AssertComponent_Assertion_Compare_InLookup[m.GetCompare()]; !ok {
			err := AssertComponent_AssertionValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LT LE GT GE LEN_EQ LEN_NE LEN_LT LEN_LE LEN_GT LEN_GE CONTAINS NOT_CONTAINS RE]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetExpected() == nil {
		err := AssertComponent_AssertionValidationError{
			field:  "Expected",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExpected()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssertComponent_AssertionValidationError{
					field:  "Expected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssertComponent_AssertionValidationError{
					field:  "Expected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpected()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssertComponent_AssertionValidationError{
				field:  "Expected",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AssertComponent_AssertionMultiError(errors)
	}

	return nil
}

// AssertComponent_AssertionMultiError is an error wrapping multiple validation
// errors returned by AssertComponent_Assertion.ValidateAll() if the
// designated constraints aren't met.
type AssertComponent_AssertionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssertComponent_AssertionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssertComponent_AssertionMultiError) AllErrors() []error { return m }

// AssertComponent_AssertionValidationError is the validation error returned by
// AssertComponent_Assertion.Validate if the designated constraints aren't met.
type AssertComponent_AssertionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssertComponent_AssertionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssertComponent_AssertionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssertComponent_AssertionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssertComponent_AssertionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssertComponent_AssertionValidationError) ErrorName() string {
	return "AssertComponent_AssertionValidationError"
}

// Error satisfies the builtin error interface
func (e AssertComponent_AssertionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssertComponent_Assertion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssertComponent_AssertionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssertComponent_AssertionValidationError{}

var _AssertComponent_Assertion_Compare_InLookup = map[string]struct{}{
	"EQ":           {},
	"NE":           {},
	"LT":           {},
	"LE":           {},
	"GT":           {},
	"GE":           {},
	"LEN_EQ":       {},
	"LEN_NE":       {},
	"LEN_LT":       {},
	"LEN_LE":       {},
	"LEN_GT":       {},
	"LEN_GE":       {},
	"CONTAINS":     {},
	"NOT_CONTAINS": {},
	"RE":           {},
}

// Validate checks the field values on PoolAccountComponent_SingleCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PoolAccountComponent_SingleCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PoolAccountComponent_SingleCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PoolAccountComponent_SingleConditionMultiError, or nil if none found.
func (m *PoolAccountComponent_SingleCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent_SingleCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetField() != "" {

		if utf8.RuneCountInString(m.GetField()) < 1 {
			err := PoolAccountComponent_SingleConditionValidationError{
				field:  "Field",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetCompare() != "" {

		if _, ok := _PoolAccountComponent_SingleCondition_Compare_InLookup[m.GetCompare()]; !ok {
			err := PoolAccountComponent_SingleConditionValidationError{
				field:  "Compare",
				reason: "value must be in list [EQ NE LT LE GT GE LIKE IN BETWEEN]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetIn()) > 0 {

	}

	if m.GetBetween() == nil {
		err := PoolAccountComponent_SingleConditionValidationError{
			field:  "Between",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBetween()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PoolAccountComponent_SingleConditionValidationError{
					field:  "Between",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PoolAccountComponent_SingleConditionValidationError{
					field:  "Between",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBetween()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PoolAccountComponent_SingleConditionValidationError{
				field:  "Between",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetOther() == nil {
		err := PoolAccountComponent_SingleConditionValidationError{
			field:  "Other",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOther()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PoolAccountComponent_SingleConditionValidationError{
					field:  "Other",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PoolAccountComponent_SingleConditionValidationError{
					field:  "Other",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOther()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PoolAccountComponent_SingleConditionValidationError{
				field:  "Other",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PoolAccountComponent_SingleConditionMultiError(errors)
	}

	return nil
}

// PoolAccountComponent_SingleConditionMultiError is an error wrapping multiple
// validation errors returned by
// PoolAccountComponent_SingleCondition.ValidateAll() if the designated
// constraints aren't met.
type PoolAccountComponent_SingleConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponent_SingleConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponent_SingleConditionMultiError) AllErrors() []error { return m }

// PoolAccountComponent_SingleConditionValidationError is the validation error
// returned by PoolAccountComponent_SingleCondition.Validate if the designated
// constraints aren't met.
type PoolAccountComponent_SingleConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponent_SingleConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponent_SingleConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponent_SingleConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponent_SingleConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponent_SingleConditionValidationError) ErrorName() string {
	return "PoolAccountComponent_SingleConditionValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponent_SingleConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent_SingleCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponent_SingleConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponent_SingleConditionValidationError{}

var _PoolAccountComponent_SingleCondition_Compare_InLookup = map[string]struct{}{
	"EQ":      {},
	"NE":      {},
	"LT":      {},
	"LE":      {},
	"GT":      {},
	"GE":      {},
	"LIKE":    {},
	"IN":      {},
	"BETWEEN": {},
}

// Validate checks the field values on PoolAccountComponent_GroupCondition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PoolAccountComponent_GroupCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PoolAccountComponent_GroupCondition
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PoolAccountComponent_GroupConditionMultiError, or nil if none found.
func (m *PoolAccountComponent_GroupCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent_GroupCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := Relationship_name[int32(m.GetRelationship())]; !ok {
		err := PoolAccountComponent_GroupConditionValidationError{
			field:  "Relationship",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetConditions()) > 0 {

		for idx, item := range m.GetConditions() {
			_, _ = idx, item

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PoolAccountComponent_GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PoolAccountComponent_GroupConditionValidationError{
							field:  fmt.Sprintf("Conditions[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PoolAccountComponent_GroupConditionValidationError{
						field:  fmt.Sprintf("Conditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return PoolAccountComponent_GroupConditionMultiError(errors)
	}

	return nil
}

// PoolAccountComponent_GroupConditionMultiError is an error wrapping multiple
// validation errors returned by
// PoolAccountComponent_GroupCondition.ValidateAll() if the designated
// constraints aren't met.
type PoolAccountComponent_GroupConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponent_GroupConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponent_GroupConditionMultiError) AllErrors() []error { return m }

// PoolAccountComponent_GroupConditionValidationError is the validation error
// returned by PoolAccountComponent_GroupCondition.Validate if the designated
// constraints aren't met.
type PoolAccountComponent_GroupConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponent_GroupConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponent_GroupConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponent_GroupConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponent_GroupConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponent_GroupConditionValidationError) ErrorName() string {
	return "PoolAccountComponent_GroupConditionValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponent_GroupConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent_GroupCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponent_GroupConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponent_GroupConditionValidationError{}

// Validate checks the field values on PoolAccountComponent_Condition with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PoolAccountComponent_Condition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PoolAccountComponent_Condition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PoolAccountComponent_ConditionMultiError, or nil if none found.
func (m *PoolAccountComponent_Condition) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent_Condition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ConditionType_name[int32(m.GetType())]; !ok {
		err := PoolAccountComponent_ConditionValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSingle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PoolAccountComponent_ConditionValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PoolAccountComponent_ConditionValidationError{
					field:  "Single",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSingle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PoolAccountComponent_ConditionValidationError{
				field:  "Single",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PoolAccountComponent_ConditionValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PoolAccountComponent_ConditionValidationError{
					field:  "Group",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PoolAccountComponent_ConditionValidationError{
				field:  "Group",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PoolAccountComponent_ConditionMultiError(errors)
	}

	return nil
}

// PoolAccountComponent_ConditionMultiError is an error wrapping multiple
// validation errors returned by PoolAccountComponent_Condition.ValidateAll()
// if the designated constraints aren't met.
type PoolAccountComponent_ConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponent_ConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponent_ConditionMultiError) AllErrors() []error { return m }

// PoolAccountComponent_ConditionValidationError is the validation error
// returned by PoolAccountComponent_Condition.Validate if the designated
// constraints aren't met.
type PoolAccountComponent_ConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponent_ConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponent_ConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponent_ConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponent_ConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponent_ConditionValidationError) ErrorName() string {
	return "PoolAccountComponent_ConditionValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponent_ConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent_Condition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponent_ConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponent_ConditionValidationError{}

// Validate checks the field values on PoolAccountComponent_KeyValPair with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PoolAccountComponent_KeyValPair) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PoolAccountComponent_KeyValPair with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PoolAccountComponent_KeyValPairMultiError, or nil if none found.
func (m *PoolAccountComponent_KeyValPair) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent_KeyValPair) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKey() != "" {

		if utf8.RuneCountInString(m.GetKey()) < 1 {
			err := PoolAccountComponent_KeyValPairValidationError{
				field:  "Key",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := PoolAccountComponent_KeyValPairValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return PoolAccountComponent_KeyValPairMultiError(errors)
	}

	return nil
}

// PoolAccountComponent_KeyValPairMultiError is an error wrapping multiple
// validation errors returned by PoolAccountComponent_KeyValPair.ValidateAll()
// if the designated constraints aren't met.
type PoolAccountComponent_KeyValPairMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponent_KeyValPairMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponent_KeyValPairMultiError) AllErrors() []error { return m }

// PoolAccountComponent_KeyValPairValidationError is the validation error
// returned by PoolAccountComponent_KeyValPair.Validate if the designated
// constraints aren't met.
type PoolAccountComponent_KeyValPairValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponent_KeyValPairValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponent_KeyValPairValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponent_KeyValPairValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponent_KeyValPairValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponent_KeyValPairValidationError) ErrorName() string {
	return "PoolAccountComponent_KeyValPairValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponent_KeyValPairValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent_KeyValPair.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponent_KeyValPairValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponent_KeyValPairValidationError{}

// Validate checks the field values on
// PoolAccountComponent_SingleCondition_Between with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PoolAccountComponent_SingleCondition_Between) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PoolAccountComponent_SingleCondition_Between with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PoolAccountComponent_SingleCondition_BetweenMultiError, or nil if none found.
func (m *PoolAccountComponent_SingleCondition_Between) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent_SingleCondition_Between) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetStart() != "" {

		if utf8.RuneCountInString(m.GetStart()) < 1 {
			err := PoolAccountComponent_SingleCondition_BetweenValidationError{
				field:  "Start",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetEnd() != "" {

		if utf8.RuneCountInString(m.GetEnd()) < 1 {
			err := PoolAccountComponent_SingleCondition_BetweenValidationError{
				field:  "End",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return PoolAccountComponent_SingleCondition_BetweenMultiError(errors)
	}

	return nil
}

// PoolAccountComponent_SingleCondition_BetweenMultiError is an error wrapping
// multiple validation errors returned by
// PoolAccountComponent_SingleCondition_Between.ValidateAll() if the
// designated constraints aren't met.
type PoolAccountComponent_SingleCondition_BetweenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponent_SingleCondition_BetweenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponent_SingleCondition_BetweenMultiError) AllErrors() []error { return m }

// PoolAccountComponent_SingleCondition_BetweenValidationError is the
// validation error returned by
// PoolAccountComponent_SingleCondition_Between.Validate if the designated
// constraints aren't met.
type PoolAccountComponent_SingleCondition_BetweenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponent_SingleCondition_BetweenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponent_SingleCondition_BetweenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponent_SingleCondition_BetweenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponent_SingleCondition_BetweenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponent_SingleCondition_BetweenValidationError) ErrorName() string {
	return "PoolAccountComponent_SingleCondition_BetweenValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponent_SingleCondition_BetweenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent_SingleCondition_Between.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponent_SingleCondition_BetweenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponent_SingleCondition_BetweenValidationError{}

// Validate checks the field values on
// PoolAccountComponent_SingleCondition_Other with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PoolAccountComponent_SingleCondition_Other) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PoolAccountComponent_SingleCondition_Other with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PoolAccountComponent_SingleCondition_OtherMultiError, or nil if none found.
func (m *PoolAccountComponent_SingleCondition_Other) ValidateAll() error {
	return m.validate(true)
}

func (m *PoolAccountComponent_SingleCondition_Other) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetValue() != "" {

		if utf8.RuneCountInString(m.GetValue()) < 1 {
			err := PoolAccountComponent_SingleCondition_OtherValidationError{
				field:  "Value",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return PoolAccountComponent_SingleCondition_OtherMultiError(errors)
	}

	return nil
}

// PoolAccountComponent_SingleCondition_OtherMultiError is an error wrapping
// multiple validation errors returned by
// PoolAccountComponent_SingleCondition_Other.ValidateAll() if the designated
// constraints aren't met.
type PoolAccountComponent_SingleCondition_OtherMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoolAccountComponent_SingleCondition_OtherMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoolAccountComponent_SingleCondition_OtherMultiError) AllErrors() []error { return m }

// PoolAccountComponent_SingleCondition_OtherValidationError is the validation
// error returned by PoolAccountComponent_SingleCondition_Other.Validate if
// the designated constraints aren't met.
type PoolAccountComponent_SingleCondition_OtherValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoolAccountComponent_SingleCondition_OtherValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoolAccountComponent_SingleCondition_OtherValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoolAccountComponent_SingleCondition_OtherValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoolAccountComponent_SingleCondition_OtherValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoolAccountComponent_SingleCondition_OtherValidationError) ErrorName() string {
	return "PoolAccountComponent_SingleCondition_OtherValidationError"
}

// Error satisfies the builtin error interface
func (e PoolAccountComponent_SingleCondition_OtherValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoolAccountComponent_SingleCondition_Other.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoolAccountComponent_SingleCondition_OtherValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoolAccountComponent_SingleCondition_OtherValidationError{}

// Validate checks the field values on DataProcessingComponent_Process with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataProcessingComponent_Process) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataProcessingComponent_Process with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DataProcessingComponent_ProcessMultiError, or nil if none found.
func (m *DataProcessingComponent_Process) ValidateAll() error {
	return m.validate(true)
}

func (m *DataProcessingComponent_Process) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := DataProcessingComponent_ProcessValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetFunction() == nil {
		err := DataProcessingComponent_ProcessValidationError{
			field:  "Function",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataProcessingComponent_ProcessValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataProcessingComponent_ProcessValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataProcessingComponent_ProcessValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DataProcessingComponent_ProcessMultiError(errors)
	}

	return nil
}

// DataProcessingComponent_ProcessMultiError is an error wrapping multiple
// validation errors returned by DataProcessingComponent_Process.ValidateAll()
// if the designated constraints aren't met.
type DataProcessingComponent_ProcessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataProcessingComponent_ProcessMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataProcessingComponent_ProcessMultiError) AllErrors() []error { return m }

// DataProcessingComponent_ProcessValidationError is the validation error
// returned by DataProcessingComponent_Process.Validate if the designated
// constraints aren't met.
type DataProcessingComponent_ProcessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataProcessingComponent_ProcessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataProcessingComponent_ProcessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataProcessingComponent_ProcessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataProcessingComponent_ProcessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataProcessingComponent_ProcessValidationError) ErrorName() string {
	return "DataProcessingComponent_ProcessValidationError"
}

// Error satisfies the builtin error interface
func (e DataProcessingComponent_ProcessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataProcessingComponent_Process.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataProcessingComponent_ProcessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataProcessingComponent_ProcessValidationError{}

// Validate checks the field values on SqlExecutionComponent_Value with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SqlExecutionComponent_Value) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SqlExecutionComponent_Value with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SqlExecutionComponent_ValueMultiError, or nil if none found.
func (m *SqlExecutionComponent_Value) ValidateAll() error {
	return m.validate(true)
}

func (m *SqlExecutionComponent_Value) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := SqlExecutionComponent_ValueValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := SqlExecutionComponent_ValueValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_ValueValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := SqlExecutionComponent_ValueValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_ValueValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := SqlExecutionComponent_ValueValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_ValueValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_ValueValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_ValueValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SqlExecutionComponent_ValueMultiError(errors)
	}

	return nil
}

// SqlExecutionComponent_ValueMultiError is an error wrapping multiple
// validation errors returned by SqlExecutionComponent_Value.ValidateAll() if
// the designated constraints aren't met.
type SqlExecutionComponent_ValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SqlExecutionComponent_ValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SqlExecutionComponent_ValueMultiError) AllErrors() []error { return m }

// SqlExecutionComponent_ValueValidationError is the validation error returned
// by SqlExecutionComponent_Value.Validate if the designated constraints
// aren't met.
type SqlExecutionComponent_ValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SqlExecutionComponent_ValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SqlExecutionComponent_ValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SqlExecutionComponent_ValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SqlExecutionComponent_ValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SqlExecutionComponent_ValueValidationError) ErrorName() string {
	return "SqlExecutionComponent_ValueValidationError"
}

// Error satisfies the builtin error interface
func (e SqlExecutionComponent_ValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSqlExecutionComponent_Value.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SqlExecutionComponent_ValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SqlExecutionComponent_ValueValidationError{}

// Validate checks the field values on SqlExecutionComponent_Sql with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SqlExecutionComponent_Sql) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SqlExecutionComponent_Sql with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SqlExecutionComponent_SqlMultiError, or nil if none found.
func (m *SqlExecutionComponent_Sql) ValidateAll() error {
	return m.validate(true)
}

func (m *SqlExecutionComponent_Sql) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := DataSourceType_name[int32(m.GetType())]; !ok {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHost() == nil {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Host",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHost()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Host",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Host",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHost()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_SqlValidationError{
				field:  "Host",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPort() == nil {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Port",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPort()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Port",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Port",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPort()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_SqlValidationError{
				field:  "Port",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetUser() == nil {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "User",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_SqlValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPassword() == nil {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Password",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPassword()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Password",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Password",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassword()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_SqlValidationError{
				field:  "Password",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetDatabase() == nil {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Database",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SqlExecutionComponent_SqlValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SqlExecutionComponent_SqlValidationError{
				field:  "Database",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetSql()) < 13 {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Sql",
			reason: "value length must be at least 13 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := SqlExecutionComponent_SqlValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetTimeout() <= 0 {
		err := SqlExecutionComponent_SqlValidationError{
			field:  "Timeout",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SqlExecutionComponent_SqlMultiError(errors)
	}

	return nil
}

// SqlExecutionComponent_SqlMultiError is an error wrapping multiple validation
// errors returned by SqlExecutionComponent_Sql.ValidateAll() if the
// designated constraints aren't met.
type SqlExecutionComponent_SqlMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SqlExecutionComponent_SqlMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SqlExecutionComponent_SqlMultiError) AllErrors() []error { return m }

// SqlExecutionComponent_SqlValidationError is the validation error returned by
// SqlExecutionComponent_Sql.Validate if the designated constraints aren't met.
type SqlExecutionComponent_SqlValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SqlExecutionComponent_SqlValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SqlExecutionComponent_SqlValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SqlExecutionComponent_SqlValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SqlExecutionComponent_SqlValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SqlExecutionComponent_SqlValidationError) ErrorName() string {
	return "SqlExecutionComponent_SqlValidationError"
}

// Error satisfies the builtin error interface
func (e SqlExecutionComponent_SqlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSqlExecutionComponent_Sql.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SqlExecutionComponent_SqlValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SqlExecutionComponent_SqlValidationError{}
