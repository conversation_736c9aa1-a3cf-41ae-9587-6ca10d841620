syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "common/config.proto";
import "common/enum.proto";
import "common/stability.proto";
import "common/ui_agent.proto";
import "manager/base.proto";
import "manager/component.proto";
import "manager/manager.proto";
import "dispatcher/base.proto";


enum WorkerType {
  WorkerType_UNKNOWN = 0;

  WorkerType_API_COMPONENT_GROUP = 1; // API组件组
  WorkerType_API_CASE = 2; // API测试用例
  WorkerType_INTERFACE_CASE = 3; // 接口用例
  WorkerType_UI_CASE = 4; // UI测试用例
  WorkerType_PERF_CASE = 5; // 压力测试用例
  WorkerType_STABILITY_CASE = 6; // 稳定性测试用例
  WorkerType_STABILITY_PLAN = 7; // 稳定性测试计划
  WorkerType_UI_AGENT_COMPONENT = 8; // `UI Agent`组件
  WorkerType_UI_AGENT_CASE = 9; // `UI Agent`用例
  WorkerType_UI_AGENT_PLAN = 10; // `UI Agent`计划
}

message WorkerReq {
  common.TriggerMode trigger_mode = 1; // 触发模式
  string trigger_rule = 2; // 触发规则
  string project_id = 3;  // 项目ID
  string task_id = 4; // 任务ID
  string execute_id = 5; // 执行ID
  manager.ApiExecutionDataType execute_type = 6; // 执行类型
  WorkerType worker_type = 7; // 工作类型
  common.GeneralConfig general_config = 8; // 通用配置
  repeated common.AccountConfig account_config = 9; // 池账号配置
  string user = 10;
  string user_id = 11;
  manager.ApiExecutionData node_data = 12;
  common.PurposeType purpose_type = 13; // 计划用途
	common.PriorityType  priority_type = 14; // 优先级

  oneof data {
    ComponentGroupWorkerInfo component_group = 31;
    CaseWorkerInfo case = 32;
    InterfaceCaseWorkerInfo interface_case = 33;
    UICaseWorkerInfo ui_case = 34;
    PerfCaseWorkerInfo perf_case = 35;
    StabilityCaseWorkerInfo stability_case = 36;
    StabilityPlanWorkerInfo stability_plan = 37;
    UIAgentComponentWorkerInfo ui_agent_component = 38;
  }

  bool debug = 99;
}

message ComponentGroupWorkerInfo {
  string component_group_id = 1;
  string component_group_execute_id = 2;
  string version = 3;
}

message CaseWorkerInfo {
  string case_id = 1;
  string case_execute_id = 2;
  string suite_id = 3; // 注：精准测试时，这里是服务ID，即虚拟的集合ID
  string suite_execute_id = 4;
  string version = 5;

  string suite_name = 11;
  string plan_id = 12;
  string plan_execute_id = 13;
  string plan_name = 14;
}

message InterfaceCaseWorkerInfo {
  string interface_case_id = 1;
  string interface_case_execute_id = 2;
  string interface_id = 3; // 注：精准测试时，这里是服务ID，即虚拟的集合ID
  string interface_execute_id = 4;
  string version = 5;
  string document_id = 6; // 接口文档ID

  string document_name = 11; // 接口文档名称
  string plan_id = 12;
  string plan_execute_id = 13;
  string plan_name = 14;
}

message PlanMonitorReq {
  string callback_url = 1;
  int64 timeout = 2;
  string project_id = 3;
  string task_id = 4;
  string plan_execute_id = 5;

  message TestInfo {
    repeated string no_case_services = 1;
  }

  TestInfo test_info = 6;
}

message UICaseWorkerInfo {
  string ui_case_id = 1;
  string ui_case_execute_id = 2;
  string ui_suite_id = 3;
  string ui_suite_execute_id = 4;
  string ui_plan_id = 5;
  string ui_plan_execute_id = 6;

  manager.UIPlanMetaData meta_data = 7;
}

message PerfCaseWorkerInfo {
  string perf_case_id = 1;
  string perf_case_execute_id = 2;
  string perf_suite_id = 3;
  string perf_suite_execute_id = 4;
  string perf_plan_id = 5;
  string perf_plan_execute_id = 6;

  manager.PerfPlanMetaData meta_data = 11; // 计划元数据
  PerfPlanInfo perf_plan_info = 12; // 计划执行信息
}

message StabilityCaseWorkerInfo {
  string stability_case_id = 1;
  string stability_case_execute_id = 2;
  string stability_plan_id = 5;
  string stability_plan_execute_id = 6;

  reserved 3, 4;
  reserved "stability_suite_id", "stability_suite_execute_id";

  common.AccountConfig account_config = 11; // 池账号配置

  common.DeviceType device_type = 21; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 22; // 平台类型（Android、iOS）
  string udid = 23; // 设备编号（非空：表示指定设备，空：表示随机选择设备）

  string package_name = 31; // 包名
  string app_download_link = 32; // App下载地址
  repeated string activities = 33; // 指定的Activity列表
  common.StabilityCustomScript custom_script = 34; // 自定义脚本

  uint32 duration = 41; // 执行时长
}

message StabilityPlanWorkerInfo {
  string stability_plan_id = 1;
  string stability_plan_execute_id = 2;

  manager.StabilityPlanMetaData meta_data = 11;
}

message UIAgentComponentWorkerInfo {
  string component_id = 1;
  string component_execute_id = 2;
  string parent_execute_id = 3;

  common.ExecuteType execute_type = 11; // 执行类型
  common.UIAgentDevice device = 12; // 设备信息
  bool reinstall = 13; // 是否重新安装
  bool restart = 14; // 是否重启应用
  string reference_id = 15; // 参考配置ID
  int32 times = 16; // 执行次数
}

message UIAgentComponentTimesInfo {
  WorkerReq req = 1; // 任务参数
  int32 total = 2; // 执行次数
  int32 sent = 3; // 已发送次数
  int32 success = 4; // 执行成功次数
}
