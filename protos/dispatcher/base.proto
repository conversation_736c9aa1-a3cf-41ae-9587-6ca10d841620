syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "validate/validate.proto";

import "protobuf/options.proto";
import "common/config.proto";
import "common/enum.proto";
import "common/ui_agent.proto";
import "manager/base.proto";


// 组件状态
enum ComponentState {
	Pending = 0;
	Init = 1;
	Started = 2;
	Success = 3;
	Waiting = 4;

	Warning = 11;
	Skip = 12;

	Failure = 21;
	Panic = 22;
	Stop = 23;
	Invalid = 99;
}

enum TaskPageType{
	PT_ALL = 0; // 缺省
	PT_ING = 1; // 处理中
	PT_ARCHIVE = 2; // 归档
}

enum StageType {
	ST_NULL = 0;
	ST_PREVIEW = 1 [(options.enum_value_alias) = "PREVIEW"];   // 预告
	ST_STARTED = 2 [(options.enum_value_alias) = "STARTED"];   // 开始
	ST_FINISHED = 3 [(options.enum_value_alias) = "FINISHED"]; // 结束
	ST_ADVANCED = 4 [(options.enum_value_alias) = "ADVANCED"]; // 提前
}

enum StopType {
	StopType_Unknown = 0;
	StopType_Manual = 1; // 手动
	StopType_Auto = 2;   // 自动
}

message ComponentKey {
	string component_id = 1;
	manager.ApiExecutionDataType component_type = 2;
	string version = 4;
}

message ComponentExecuteKey {
	ComponentKey key = 1;
	string component_execute_id = 2;
	ComponentState state = 3;
}

message TaskParams {
	common.TriggerMode trigger_mode = 1; // 触发模式
	string trigger_rule = 2; // 触发规则
	string project_id = 3;
	string task_id = 4;
	string execute_id = 5;
	manager.ApiExecutionDataType execute_type = 6;
	ComponentExecuteKey parent_component = 7;
	ComponentExecuteKey current_component = 8;
	common.GeneralConfig general_config = 9;
	repeated common.AccountConfig account_config = 10;
	string user = 11;
	string user_id = 12;
	ComponentState state = 13;
	repeated ComponentExecuteKey interface_case_ids = 14;
	manager.ExecutionMode suite_execution_mode = 15; // 集合执行方式
	manager.ExecutionMode case_execution_mode = 16; // 用例执行方式
	bool debug = 99;
}

message ApiPlanInfo {
	repeated string services = 1; // 服务列表
}

message UiPlanInfo {
	string app_download_url = 1; // App下载地址
	string app_version = 2;      // App版本信息
	repeated string devices = 3; // 设备列表
	bool together = 4;           // 是否一起执行
}

message PerfPlanInfo {
	common.PerfTaskType execute_type = 1; // 执行类型，如：执行、调试
	int64 estimated_time = 2;             // 指定开始时间
	bool send_preview_notification = 3;   // 是否发送预告通知
}

message StopDetailOfPerfStopRule {
	message MetricPoint {
		int64 timestamp = 1; // 时间戳
		double value = 2;    // 值
	}

	string metric_type = 1;          // 指标类型
	string service = 2;              // 服务名称
	string namespace = 3;            // 命名空间
	string method = 4;               // 接口名称
	double threshold = 5;            // 规则中的阀值
	int64 reached_at = 6;            // 达到规则中的阀值的时间
	int64 latest_at = 7;             // 达到规则中的持续时长的时间
	repeated MetricPoint points = 8; // 满足规则后的指标点
}

// UIAgentComponentInfo `UI Agent`组件信息
message UIAgentComponentInfo {
  common.ExecuteType execute_type = 1; // 执行类型
  common.UIAgentDevice device = 2; // 设备信息
  bool reinstall = 3; // 是否重新安装
  bool restart = 4; // 是否重启应用
  string reference_id = 5; // 参考配置ID
  int32 times = 6; // 执行次数
}

// UIAgentDeviceInfo `UI Agent`组件的设备信息
message UIAgentDeviceInfo {
	message ProjectDeviceInfo {
		string udid = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备ID
		string token = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 占用设备的令牌
	}
	message UserDeviceInfo {
		common.DeviceType device_type = 1 [(validate.rules).enum = {not_in: [0]}]; // 设备类型
		common.PlatformType platform_type = 2 [(validate.rules).enum = {not_in: [0]}]; // 平台类型
		string udid = 3 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备ID
		string remote_address = 4 [(validate.rules).string = {min_len: 1}]; // 设备远程连接地址
	}

	oneof device {
		ProjectDeviceInfo project_device = 1 [(validate.rules).message = {required: true}];
		UserDeviceInfo user_device = 2 [(validate.rules).message = {required: true}];
	}
}
