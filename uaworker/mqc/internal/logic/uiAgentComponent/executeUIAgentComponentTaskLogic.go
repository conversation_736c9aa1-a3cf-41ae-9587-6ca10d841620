package uiAgentComponent

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync/atomic"
	"time"

	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/pkg/errors"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zyedidia/generic/set"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commondevice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/errmsg"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	discoverypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
	dispatcherutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/svc"
)

const maxRetryTimesOfGetTaskStatus = 3

var (
	errorOfNullTaskInfo           = errors.New("the task info is null")
	errorOfNullAppConfig          = errors.New("the application configuration is null")
	errorOfNullDeviceInfo         = errors.New("the device info is null")
	errorOfExecuteTaskFailed      = errors.New("failed to execute the task")
	errorOfTerminateExecutingTask = errors.New("terminate executing task")
)

type ExecuteUIAgentComponentTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	taskInfo  *commonpb.UIAgentComponentTaskInfo
	state     *atomic.Int32
	startedAt time.Time

	key           string
	appPath       string // 待测试的App文件
	appName       string // 待测试的App名称（Android: package_name, iOS: bundle_id）
	deviceType    commonpb.DeviceType
	platformType  commonpb.PlatformType
	udid          string
	remoteAddress string
	device        commondevice.IDevice
	byDiscovery   bool

	stopCh chan lang.PlaceholderType
}

func NewExecuteUIAgentComponentTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, taskInfo *commonpb.UIAgentComponentTaskInfo,
) *ExecuteUIAgentComponentTaskLogic {
	l := &ExecuteUIAgentComponentTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		taskInfo:  taskInfo,
		state:     new(atomic.Int32),
		startedAt: time.Time{},

		stopCh: make(chan lang.PlaceholderType),
	}
	l.state.Store(int32(dispatcherpb.ComponentState_Pending))
	return l
}

func (l *ExecuteUIAgentComponentTaskLogic) Execute() (err error) {
	defer func() {
		if r := recover(); r != nil {
			l.changeState(dispatcherpb.ComponentState_Panic)
			l.Errorf("got a panic while executing the ui agent component task, error: %+v", r)
		} else if err != nil {
			l.changeState(dispatcherpb.ComponentState_Failure)
		} else {
			l.changeState(dispatcherpb.ComponentState_Success)
		}

		if l.taskInfo.GetParentExecuteId() == "" {
			_ = l.sendTaskStatusToReporter(err)
		} else {
			_ = l.sendCallbackDataToDispatcher(err)
		}

		if l.device != nil {
			_ = l.device.Close()
		}
	}()

	if err = l.validate(); err != nil {
		return err
	}
	if err = l.init(); err != nil {
		return err
	}

	l.changeState(dispatcherpb.ComponentState_Started)
	l.startedAt = time.Now()
	if err = l.sendTaskStatusToReporter(nil); err != nil {
		return err
	}

	threading.GoSafeCtx(l.ctx, l.watchStopSignal)

	if err = l.initApp(); err != nil {
		return err
	}

	if err = l.executeByClickPilot(); err != nil {
		return err
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) changeState(to dispatcherpb.ComponentState) {
	to32 := int32(to)
	for {
		from := l.state.Load()
		if from >= to32 {
			return
		}

		if flag := l.state.CompareAndSwap(from, to32); flag {
			return
		}
	}
}

func (l *ExecuteUIAgentComponentTaskLogic) validate() error {
	if l.taskInfo == nil {
		return errmsg.NewErrMsg(errorOfNullTaskInfo, errmsg.WithErrMsg(common.ErrZHMsgOfNullTaskInfo))
	}

	if err := l.taskInfo.ValidateAll(); err != nil {
		return errmsg.NewErrMsg(
			errors.Errorf(
				"failed to validate the ui agent component task info, info: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(l.taskInfo), err,
			), errmsg.WithErrMsg(common.ErrZHMsgOfValidateTaskInfoFailed),
		)
	}

	if l.taskInfo.GetApplicationConfig() == nil {
		return errmsg.NewErrMsg(errorOfNullAppConfig, errmsg.WithErrMsg(common.ErrZHMsgOfNullAppConfig))
	}

	if l.taskInfo.GetDevice() == nil {
		return errmsg.NewErrMsg(errorOfNullDeviceInfo, errmsg.WithErrMsg(common.ErrZHMsgOfNullDeviceInfo))
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) init() error {
	taskID := l.taskInfo.GetTaskId()
	l.key = taskID
	if ss := strings.Split(taskID, ":"); len(ss) >= 2 {
		l.key = ss[1]
	}

	suffix := ""
	platformType := l.taskInfo.GetApplicationConfig().GetPlatformType()
	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		suffix = commonconsts.ConstSuffixOfApk
	case commonpb.PlatformType_IOS:
		suffix = commonconsts.ConstSuffixOfIpa
	default:
		l.Warnf("invalid app platform type: %s", platformType.String())
	}

	// appPath = /app/data/reports/ui_agent_test/apps/${key}${suffix}
	l.appPath = filepath.Join(
		l.svcCtx.Config.LocalPath,
		commonconsts.ConstStoragePathOfReports,
		commonconsts.ConstStoragePathOfUIAgentTest,
		commonconsts.ConstStoragePathOfApps,
		fmt.Sprintf("%s%s", l.key, suffix),
	)

	switch v := l.taskInfo.GetDevice().GetDevice().(type) {
	case *commonpb.UIAgentDevice_ProjectDevice_:
		l.deviceType = v.ProjectDevice.GetDeviceType()
		l.platformType = v.ProjectDevice.GetPlatformType()
		l.udid = v.ProjectDevice.GetUdid()
		l.remoteAddress = v.ProjectDevice.GetRemoteAddress()
	case *commonpb.UIAgentDevice_UserDevice_:
		l.deviceType = v.UserDevice.GetDeviceType()
		l.platformType = v.UserDevice.GetPlatformType()
		l.udid = v.UserDevice.GetUdid()
		l.remoteAddress = v.UserDevice.GetRemoteAddress()
	default:
		return errmsg.NewErrMsg(
			errors.Errorf(
				"invalid device type, expected: %T or %T, but got %T",
				(*commonpb.UIAgentDevice_ProjectDevice_)(nil), (*commonpb.UIAgentDevice_UserDevice_)(nil), v,
			), errmsg.WithErrMsg(common.ErrZHMsgOfInvalidDeviceType),
		)
	}

	var err error
	switch l.platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		l.device, err = commondevice.NewAndroidDevice(l.ctx, l.deviceType, l.udid, l.remoteAddress)
	case commonpb.PlatformType_IOS:
		l.device, err = commondevice.NewIOSDevice(l.ctx, l.deviceType, l.udid, l.remoteAddress)
	default:
		return errmsg.NewErrMsg(
			errors.Errorf(
				"invalid platform type, expected: %s, %s or %s, but got: %s",
				protobuf.GetEnumStringOf(commonpb.PlatformType_ANDROID),
				protobuf.GetEnumStringOf(commonpb.PlatformType_IOS),
				protobuf.GetEnumStringOf(commonpb.PlatformType_HarmonyOS),
				l.platformType.String(),
			),
		)
	}
	if err != nil {
		return errmsg.NewErrMsg(err, errmsg.WithErrMsg(common.ErrZHMsgOfInitDeviceFailed))
	}

	passed := l.svcCtx.DiscoveryRPC.HealthCheck()
	byDiscovery := l.svcCtx.DiscoveryRPC.IsDiscovery()
	if passed == nil && byDiscovery && l.deviceType == commonpb.DeviceType_REAL_PHONE {
		l.byDiscovery = true
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) watchStopSignal() {
	ticker := timewheel.NewTicker(common.ConstIntervalOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(l.stopCh)
				return
			}
		}
	}
}

func (l *ExecuteUIAgentComponentTaskLogic) isStopped() (err error) {
	taskID := l.taskInfo.GetTaskId()

	defer func() {
		if r := recover(); r != nil {
			l.changeState(dispatcherpb.ComponentState_Panic)
			err = errors.Errorf("got a panic while checking the stop status, task_id: %s, error: %+v", taskID, r)
		}
	}()

	stop, err := dispatcherutils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.changeState(dispatcherpb.ComponentState_Panic)
		return errors.Errorf("failed to get the stop status of task, task_id: %s, error: %+v", taskID, err)
	} else if stop {
		l.changeState(dispatcherpb.ComponentState_Stop)
		return errors.Errorf("got a stop signal of task, task_id: %s", taskID)
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) sendTaskStatusToReporter(status error) error {
	ctx1, cancel1 := utils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfInvokeRPC)
	defer cancel1()

	var (
		taskID      = l.taskInfo.GetTaskId()
		executeID   = l.taskInfo.GetExecuteId()
		projectID   = l.taskInfo.GetProjectId()
		componentID = l.taskInfo.GetComponentId()
		state       = dispatcherpb.ComponentState(l.state.Load())

		req *reporterpb.ModifyUIAgentComponentRecordReq
	)

	req = &reporterpb.ModifyUIAgentComponentRecordReq{
		TaskId:      taskID,
		ExecuteId:   executeID,
		ProjectId:   projectID,
		ComponentId: componentID,
		Status:      state.String(),
	}
	switch state {
	case dispatcherpb.ComponentState_Success:
		req.Successes = 1
		req.EndedAt = timestamppb.Now()
	case dispatcherpb.ComponentState_Skip,
		dispatcherpb.ComponentState_Failure,
		dispatcherpb.ComponentState_Panic,
		dispatcherpb.ComponentState_Stop,
		dispatcherpb.ComponentState_Invalid:
		req.EndedAt = timestamppb.Now()
	}
	if status != nil {
		var em errmsg.ErrMsg
		if ok := errors.As(status, &em); ok {
			req.ErrMsg = &reporterpb.ErrorMessage{
				Code:      uint32(em.Code()),
				MessageEn: em.Error(),
				MessageZh: string(em.Message()),
			}
		}
	}

	if _, err := l.svcCtx.ReporterRPC.ModifyUIAgentComponentRecord(ctx1, req); err != nil {
		l.Errorf(
			"failed to modify the ui agent component record, task_id: %s, execute_id: %s, project_id: %s, component_id: %s, error: %+v",
			taskID, executeID, projectID, componentID, err,
		)
	} else {
		return nil
	}

	l.Infof(
		"try to modify the ui agent component record by mq, task_id: %s, execute_id: %s, project_id: %s, component_id: %s",
		taskID, executeID, projectID, componentID,
	)
	ctx2, cancel2 := utils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfSendTask)
	defer cancel2()

	if _, err := l.svcCtx.ReporterProducer.Send(
		ctx2, base.NewTask(
			commonconsts.MQTaskTypeReporterHandleUIAgentRecordTask,
			protobuf.MarshalJSONIgnoreError(
				&reporterpb.HandleUIAgentRecordTaskInfo{
					Payload: &reporterpb.HandleUIAgentRecordTaskInfo_ModifyComponent{
						ModifyComponent: req,
					},
				},
			),
		), base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to modify the ui agent component record by mq, task_id: %s, execute_id: %s, project_id: %s, component_id: %s, error: %+v",
			taskID, executeID, projectID, componentID, err,
		)
		return err
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) sendCallbackDataToDispatcher(status error) error {
	var (
		taskID          = l.taskInfo.GetTaskId()
		executeID       = l.taskInfo.GetExecuteId()
		parentExecuteID = l.taskInfo.GetParentExecuteId()
		projectID       = l.taskInfo.GetProjectId()
		componentID     = l.taskInfo.GetComponentId()
		executeType     = l.taskInfo.GetExecuteType()
		state           = dispatcherpb.ComponentState(l.state.Load())
	)

	debug := false
	if executeType == commonpb.ExecuteType_ET_DEBUG {
		debug = true
	}

	var errMsg *reporterpb.ErrorMessage
	if status != nil {
		var em errmsg.ErrMsg
		if ok := errors.As(status, &em); ok {
			errMsg = &reporterpb.ErrorMessage{
				Code:      uint32(em.Code()),
				MessageEn: em.Error(),
				MessageZh: string(em.Message()),
			}
		}
	}

	req := &dispatcherpb.CallbackReq{
		TriggerMode:  l.taskInfo.GetTriggerMode(),
		TriggerRule:  l.taskInfo.GetTriggerRule(),
		ProjectId:    projectID,
		TaskId:       taskID,
		ExecuteType:  managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
		CallbackType: dispatcherpb.CallbackType_CallbackType_UI_AGENT_COMPONENT,
		Data: &dispatcherpb.CallbackReq_UiAgentComponent{
			UiAgentComponent: &dispatcherpb.UIAgentComponentCallbackData{
				ComponentId:        componentID,
				ComponentExecuteId: executeID,
				ParentExecuteId:    parentExecuteID, // 父执行ID有可能为空
				ComponentState:     state,
				ErrMsg:             errMsg,
			},
		},
		Debug: debug,
	}

	ctx, cancel := utils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfSendTask)
	defer cancel()

	if _, err := l.svcCtx.DispatcherProducer.Send(
		ctx, base.NewTask(
			commonconsts.MQTaskTypeDispatcherCallback,
			protobuf.MarshalJSONIgnoreError(req),
			base.WithMaxRetryOptions(0),
		), base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send the task to mq, task_name: %s, payload: %s, error: %+v",
			commonconsts.MQTaskTypeDispatcherCallback, req, err,
		)
		return err
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) sendTaskStatusToManager(status clickPilot.TaskStatus) error {
	ctx, cancel := utils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfSendTask)
	defer cancel()

	var (
		projectID   = l.taskInfo.GetProjectId()
		componentID = l.taskInfo.GetComponentId()

		result commonpb.ExecutedResult
	)
	switch status {
	case clickPilot.TaskStatusOfNull, clickPilot.TaskStatusOfProcessing, clickPilot.TaskStatusOfTerminated:
		l.Warnf("no need to update the ui agent component result, status: %s", status)
		return nil
	case clickPilot.TaskStatusOfSucceed:
		result = commonpb.ExecutedResult_TER_SUCCESS
	case clickPilot.TaskStatusOfFailed:
		result = commonpb.ExecutedResult_TER_FAILURE
	default:
		l.Errorf("invalid task status: %s", status)
		return nil
	}

	if _, err := l.svcCtx.ManagerProducer.Send(
		ctx, base.NewTask(
			commonconsts.MQTaskTypeManagerHandlerUpdateUIAgentComponentResult,
			protobuf.MarshalJSONIgnoreError(
				&managerpb.UpdateUIAgentComponentResultTaskInfo{
					ProjectId:   projectID,
					ComponentId: componentID,
					ExecutedAt:  l.startedAt.UnixMilli(),
					Result:      result,
				},
			),
		), base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to update the ui agent component result by mq, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
		return err
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) initApp() error {
	apps, err := l.checkApp()
	if err != nil {
		return err
	} else if apps != nil && apps.Size() != 0 && !l.taskInfo.GetReinstall() {
		// if the device has installed the target app and the current execution does not need to reinstall,
		// then no need to install the app.
		return nil
	}

	if err = l.downloadApp(); err != nil {
		return err
	}

	return l.installApp()
}

func (l *ExecuteUIAgentComponentTaskLogic) checkApp() (*set.Set[string], error) {
	var (
		appConfig = l.taskInfo.GetApplicationConfig()

		apps *set.Set[string]
		err  error
	)

	switch d := l.device.(type) {
	case *commondevice.AndroidDevice:
		apps, err = d.ListPackages(commondevice.WithFilterOfPackageName(appConfig.GetAppId()))
	case *commondevice.IOSDevice:
		apps, err = d.ListApps(commondevice.WithAppsMatching(map[string]any{"CFBundleIdentifier": appConfig.GetAppId()}))
	default:
		return nil, errmsg.NewErrMsg(
			errors.Errorf("the device has not been initialized, udid: %s", l.udid),
			errmsg.WithErrMsg(common.ErrZHMsgOfDeviceUnInitialized),
		)
	}

	return apps, err
}

func (l *ExecuteUIAgentComponentTaskLogic) downloadApp() (err error) {
	var (
		appID        = l.taskInfo.GetApplicationConfig().GetAppId()
		downloadLink = l.taskInfo.GetApplicationConfig().GetAppDownloadLink()

		appInfo *commonpb.AppInfo
	)

	ctx, cancel := context.WithTimeout(l.ctx, common.ConstTimeoutOfDownloadApp)
	defer cancel()

	if l.byDiscovery {
		appInfo, err = l.downloadAppByDiscovery(ctx, appID, downloadLink)
	} else {
		appInfo, err = l.downloadAppLocally(ctx, appID, downloadLink)
	}
	if err != nil {
		return errmsg.NewErrMsg(err, errmsg.WithErrMsg(common.ErrZHMsgOfDownloadAppFailed))
	} else if appInfo == nil {
		return errmsg.NewErrMsg(
			errors.Errorf(
				"got a null app info after downloading the app package, app_id: %s, link: %s, by_discovery: %t",
				appID, downloadLink, l.byDiscovery,
			), errmsg.WithErrMsg(common.ErrZHMsgOfGetAppInfoFailed),
		)
	}

	l.appName = appInfo.GetName()
	l.Infof(
		"finish to download the app package, app_id: %s, link: %s, version: %s, size: %d, app_name: %s, app_path: %s, by_discovery: %t",
		appID, appInfo.GetDownloadLink(), appInfo.GetVersion(), appInfo.GetSize(), l.appName, l.appPath, l.byDiscovery,
	)
	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) downloadAppLocally(
	ctx context.Context, appID, downloadLink string,
) (*commonpb.AppInfo, error) {
	var version, name string

	stat, err := os.Stat(l.appPath)
	if err == nil && !stat.IsDir() && stat.Size() > 0 {
		l.Infof(
			"the app package has been downloaded, app_id: %s, link: %s, path: %s, size: %d",
			appID, downloadLink, l.appPath, stat.Size(),
		)
	} else {
		if downloadLink != "" {
			err = qetutils.DownloadFromUrl(ctx, downloadLink, l.appPath)
		} else {
			var puller pkgpuller.AppPkgPuller
			puller, err = pkgpuller.NewAppPkgPuller(ctx, pkgpuller.AppPkgNameType(appID), l.appPath)
			if err != nil {
				return nil, errors.Errorf(
					"failed to new app package puller, app_id: %s, path: %s, error: %+v",
					appID, l.appPath, err,
				)
			}

			downloadLink, err = puller.Pull()
		}
		if err != nil {
			return nil, errors.Errorf(
				"failed to download the app package, app_id: %s, link: %s, path: %s, error: %+v",
				appID, downloadLink, l.appPath, err,
			)
		}

		stat, err = os.Stat(l.appPath)
		if err != nil {
			return nil, errors.Errorf(
				"the app package was not found locally after download, app_id: %s, link: %s, path: %s, error: %+v",
				appID, downloadLink, l.appPath, err,
			)
		}
	}

	switch l.platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		info, err := apk.OpenFile(l.appPath)
		if err != nil {
			l.Errorf(
				"failed to open the apk file, app_id: %s, link: %s, path: %s, size: %d, error: %+v",
				appID, downloadLink, l.appPath, stat.Size(), err,
			)
		} else {
			name = info.PackageName()
			version = info.Manifest().VersionName
		}
	case commonpb.PlatformType_IOS:
		info, err := ipa.Info(l.appPath)
		if err != nil {
			l.Errorf(
				"failed to open the ipa file, app_id: %s, link: %s, path: %s, size: %d, error: %+v",
				appID, downloadLink, l.appPath, stat.Size(), err,
			)
		} else {
			name = info.CFBundleIdentifier
			version = info.CFBundleShortVersionString
			if len(info.CFBundleVersion) > 0 {
				version += "-" + info.CFBundleVersion
			}
		}
	}

	return &commonpb.AppInfo{
		DownloadLink: downloadLink,
		Version:      version,
		Name:         name,
		Size:         stat.Size(),
	}, nil
}

func (l *ExecuteUIAgentComponentTaskLogic) downloadAppByDiscovery(ctx context.Context, appID, downloadLink string) (
	*commonpb.AppInfo, error,
) {
	out, err := l.svcCtx.DiscoveryRPC.DownloadApp(
		ctx, &discoverypb.DownloadAppReq{
			PlatformType: l.platformType,
			AppName:      appID,
			Link:         downloadLink,
			Filename:     filepath.Base(l.appPath),
		},
	)
	if err != nil {
		return nil, err
	}

	return &commonpb.AppInfo{
		DownloadLink: out.GetLink(),
		Version:      out.GetVersion(),
		Name:         out.GetAppName(),
		Size:         out.GetSize(),
	}, nil
}

func (l *ExecuteUIAgentComponentTaskLogic) installApp() error {
	ctx, cancel := context.WithTimeout(l.ctx, common.ConstTimeoutOfInstallApp)
	defer cancel()

	resultCh := make(chan error, 1)
	// 注：这里不使用`threading.GoSafeCtx`方法，避免产生`panic`的时候没有把`err`通过`resultCh`返回，导致外层`select`阻塞
	go func() {
		var e error

		defer func() {
			if r := recover(); r != nil {
				l.Errorf(
					"failed to install app, udid: %s, path: %s, by_discovery: %t, error: %+v",
					l.udid, l.appPath, l.byDiscovery, r,
				)
				e = errors.Errorf("%v", r)
			} else if e != nil {
				l.Errorf(
					"failed to install app, udid: %s, path: %s, by_discovery: %t, error: %+v",
					l.udid, l.appPath, l.byDiscovery, e,
				)
			} else {
				l.Infof(
					"install app successfully, udid: %s, path: %s, by_discovery: %t", l.udid, l.appPath, l.byDiscovery,
				)
			}
			resultCh <- e
		}()

		if l.byDiscovery {
			_, e = l.svcCtx.DiscoveryRPC.InstallApp(
				ctx, &discoverypb.InstallAppReq{
					PlatformType:  l.platformType,
					Udid:          l.udid,
					RemoteAddress: l.remoteAddress,
					Filename:      filepath.Base(l.appPath),
				},
			)
		} else {
			e = l.device.AppInstall(l.appPath)
		}
	}()

	select {
	case <-ctx.Done():
		l.Infof("got a done signal while installing the app, udid: %s, path: %s", l.udid, l.appPath)
		return errmsg.NewErrMsg(ctx.Err(), errmsg.WithErrMsg(common.ErrZHMsgOfInstallAppTimeout))
	case <-l.stopCh:
		l.Infof("got a stop signal while installing the app, udid: %s, path: %s", l.udid, l.appPath)
		return nil
	case err := <-resultCh:
		if err != nil {
			return errmsg.NewErrMsg(err, errmsg.WithErrMsg(common.ErrZHMsgOfInstallAppFailed))
		}
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) executeByClickPilot() error {
	ctx, cancel := context.WithTimeout(l.ctx, common.ConstTimeoutOfExecuteTask)
	defer cancel()

	var (
		status clickPilot.TaskStatus
		err    error
	)

	if err = l.createClickPilotTask(); err != nil {
		return err
	}

	defer func() {
		if e := l.sendTaskStatusToManager(status); e != nil {
			l.Error(e)
		}
	}()
	status, err = l.checkClickPilotTask(ctx)
	return err
}

func (l *ExecuteUIAgentComponentTaskLogic) createClickPilotTask() error {
	task, err := l.generateClickPilotTask()
	if err != nil {
		return err
	}

	l.Infof("create task by ClickPilot, task: %s", jsonx.MarshalIgnoreError(task))
	if err = l.svcCtx.ClickPilotClient.CreateUITask(task); err != nil {
		var e *clickPilot.Error
		if errors.As(err, &e) {
			return errmsg.NewErrMsg(
				e.Unwrap(),
				errmsg.WithErrCode(errmsg.ErrCode(dispatcherpb.ComponentState_Failure)),
				errmsg.WithErrMsg(
					errmsg.ErrZHMsg(
						fmt.Sprintf("%s: %s", common.ErrZHMsgOfCreateUIAgentTaskFailed, e.Message()),
					),
				),
			)
		}
		return errmsg.NewErrMsg(err, errmsg.WithErrMsg(common.ErrZHMsgOfCreateUIAgentTaskFailed))
	}

	return nil
}

func (l *ExecuteUIAgentComponentTaskLogic) generateClickPilotTask() (*clickPilot.UITask, error) {
	appConfig := l.taskInfo.GetApplicationConfig()
	stepList := l.taskInfo.GetSteps()
	varList := l.taskInfo.GetVariables()

	executionMode := clickPilot.ExecutionModeOfAgent
	if l.taskInfo.GetMode() == commonpb.UIAgentMode_UIAgentMode_STEP {
		executionMode = clickPilot.ExecutionModeOfStep
	}
	agentType := clickPilot.AgentTypeOfAndroid
	if l.platformType == commonpb.PlatformType_IOS {
		agentType = clickPilot.AgentTypeOfIOS
	}
	restart := l.taskInfo.GetRestart()
	if l.taskInfo.GetReinstall() {
		restart = true
	}

	task := &clickPilot.UITask{
		TaskID:             l.taskInfo.GetExecuteId(), // 执行ID作为Agent的任务ID
		TaskName:           l.taskInfo.GetComponentName(),
		ExecutionMode:      executionMode,
		TaskStepByStep:     make([]*clickPilot.TaskStep, 0, len(stepList)),
		AgentType:          agentType,
		AppID:              appConfig.GetAppId(),
		AppName:            appConfig.GetName(),
		IsRestart:          restart,
		ReferenceTaskID:    l.taskInfo.GetReferenceId(),
		AppForegroundCheck: l.taskInfo.GetForegroundCheck(),
	}

	for _, prompt := range appConfig.GetPrompts() {
		switch prompt.GetCategory() {
		case commonpb.PromptCategory_PromptCategory_BACKGROUND:
			task.AppDescription = prompt.GetContent()
		case commonpb.PromptCategory_PromptCategory_UI_COMPONENT:
			task.UIComponentInstructions = prompt.GetContent()
		case commonpb.PromptCategory_PromptCategory_EXCEPTION_HANDLING:
			task.SpecialScenarios = prompt.GetContent()
		}
	}

	variables := make(map[string]string, len(varList))
	for _, v := range varList {
		if v.GetKey() == "" {
			continue
		}

		variables[v.GetKey()] = v.GetValue()
	}

	for _, step := range stepList {
		content := l.replaceVariables(step.GetContent(), variables)

		item := &clickPilot.TaskStep{
			Step:     content,
			WaitTime: step.GetWaitingTime(),
		}
		if exp := step.GetExpectation(); exp != nil &&
			(exp.GetText() != "" || exp.GetImage() != "") &&
			executionMode == clickPilot.ExecutionModeOfStep {
			item.ExpectResult = &clickPilot.TaskExpectResult{
				Text: l.replaceVariables(exp.GetText(), variables),
			}
			if exp.GetImage() != "" {
				path, err := l.checkImage(exp.GetImage())
				if err != nil {
					return nil, errmsg.NewErrMsg(err, errmsg.WithErrMsg(common.ErrZHMsgOfExpectationImageNotFound))
				}

				item.ExpectResult.Image = path
			}
		}

		task.TaskStepByStep = append(task.TaskStepByStep, item)
	}

	if exp := l.taskInfo.GetExpectation(); exp != nil && (exp.GetText() != "" || exp.GetImage() != "") {
		task.TaskExpectResult = &clickPilot.TaskExpectResult{
			Text: l.replaceVariables(exp.GetText(), variables),
		}
		if exp.GetImage() != "" {
			path, err := l.checkImage(exp.GetImage())
			if err != nil {
				return nil, errmsg.NewErrMsg(err, errmsg.WithErrMsg(common.ErrZHMsgOfExpectationImageNotFound))
			}

			task.TaskExpectResult.Image = path
		}
	}

	switch l.platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		task.Device = &clickPilot.Device{
			Type: clickPilot.DeviceTypeOfAndroid,
			Android: &clickPilot.AndroidDevice{
				URL: l.remoteAddress,
			},
		}
	case commonpb.PlatformType_IOS:
		task.Device = &clickPilot.Device{
			Type: clickPilot.DeviceTypeOfIOS,
			IOS: &clickPilot.IOSDevice{
				URL: l.remoteAddress,
			},
		}
	}

	return task, nil
}

func (l *ExecuteUIAgentComponentTaskLogic) checkClickPilotTask(ctx context.Context) (clickPilot.TaskStatus, error) {
	ticker := timewheel.NewTicker(common.ConstIntervalOfCheckTaskStatus)
	defer ticker.Stop()

	var (
		executeID = l.taskInfo.GetExecuteId() // 执行ID作为Agent的任务ID

		finished bool
		times    int
	)
	defer func() {
		if !finished {
			if e := l.svcCtx.ClickPilotClient.StopTask(executeID); e != nil {
				l.Errorf("failed to stop the task by ClickPilot, execute_id: %s, error: %+v", executeID, e)
			}
		}
	}()

	for {
		select {
		case <-l.ctx.Done():
			l.Infof("got a done signal while checking the task status, execute_id: %s", executeID)
			return clickPilot.TaskStatusOfNull, errmsg.NewErrMsg(
				l.ctx.Err(), errmsg.WithErrMsg(common.ErrZHMsgOfExecuteTaskTimeout),
			)
		case <-l.stopCh:
			l.Infof("got a stop signal while checking the task status, execute_id: %s", executeID)
			return clickPilot.TaskStatusOfTerminated, errmsg.NewErrMsg(
				errorOfTerminateExecutingTask, errmsg.WithErrMsg(common.ErrZHMsgOfTerminateExecutingTask),
			)
		case <-ctx.Done():
			l.Infof("got a done signal while checking the task status, execute_id: %s", executeID)
			return clickPilot.TaskStatusOfNull, errmsg.NewErrMsg(
				ctx.Err(), errmsg.WithErrMsg(common.ErrZHMsgOfExecuteUIAgentTaskTimeout),
			)
		case <-ticker.C:
			data, err := l.svcCtx.ClickPilotClient.GetTaskStatus(executeID)
			if err != nil {
				times += 1
				l.Errorf("failed to get the task status by ClickPilot, execute_id: %s, error: %+v", executeID, err)

				if times >= maxRetryTimesOfGetTaskStatus {
					var e *clickPilot.Error
					if errors.As(err, &e) {
						return clickPilot.TaskStatusOfNull, errmsg.NewErrMsg(
							e.Unwrap(),
							errmsg.WithErrCode(errmsg.ErrCode(dispatcherpb.ComponentState_Failure)),
							errmsg.WithErrMsg(
								errmsg.ErrZHMsg(
									fmt.Sprintf("%s: %s", common.ErrZHMsgOfCheckUIAgentTaskFailed, e.Message()),
								),
							),
						)
					}
					return clickPilot.TaskStatusOfNull, errmsg.NewErrMsg(
						err, errmsg.WithErrMsg(common.ErrZHMsgOfCheckUIAgentTaskFailed),
					)
				}

				continue
			} else if times > 0 {
				times = 0
			}

			switch data.Status {
			case clickPilot.TaskStatusOfProcessing:
				continue
			case clickPilot.TaskStatusOfSucceed:
				finished = true
				return data.Status, nil
			case clickPilot.TaskStatusOfFailed:
				finished = true
				opts := make([]errmsg.Option, 0, 2)
				if len(data.Message) != 0 {
					opts = append(
						opts,
						errmsg.WithErrCode(errmsg.ErrCode(dispatcherpb.ComponentState_Failure)),
						errmsg.WithErrMsg(
							errmsg.ErrZHMsg(
								fmt.Sprintf(
									"%s: %s", common.ErrZHMsgOfExecuteUIAgentTaskFailed, data.Message,
								),
							),
						),
					)
				} else {
					opts = append(opts, errmsg.WithErrMsg(common.ErrZHMsgOfExecuteUIAgentTaskFailed))
				}
				return data.Status, errmsg.NewErrMsg(errorOfExecuteTaskFailed, opts...)
			case clickPilot.TaskStatusOfTerminated:
				finished = true
				return data.Status, errmsg.NewErrMsg(
					errorOfTerminateExecutingTask, errmsg.WithErrMsg(common.ErrZHMsgOfTerminateExecutingTask),
				)
			}
		}
	}
}

func (l *ExecuteUIAgentComponentTaskLogic) checkImage(imageID string) (string, error) {
	imagePath := filepath.Join(l.svcCtx.Config.LocalPath, commonconsts.ConstStoragePathOfFiles, imageID)
	if !qetutils.Exists(imagePath) {
		return "", errors.Errorf("the image file doesn't exist, image_id: %s, path: %s", imageID, imagePath)
	}

	return imagePath, nil
}

func (l *ExecuteUIAgentComponentTaskLogic) replaceVariables(content string, vars map[string]string) string {
	bs, err := template.Execute("replaceVariables", content, vars)
	if err != nil {
		l.Errorf(
			"failed to replace content by variables, content: %s, variables: %s, error: %+v",
			content, jsonx.MarshalIgnoreError(vars), err,
		)
		return content
	}

	return string(bs)
}
