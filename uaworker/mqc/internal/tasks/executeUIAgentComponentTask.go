package tasks

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/logic/uiAgentComponent"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/svc"
)

var _ base.Handler = (*UIAgentComponentTaskProcessor)(nil)

type UIAgentComponentTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewUIAgentComponentTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UIAgentComponentTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UIAgentComponentTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var req dispatcherpb.WorkerReq
	if err := protobuf.UnmarshalJSON(task.Payload, &req); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of ui agent component task, payload: %s, error: %+v", task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	ctx = updateContext(
		ctx, req.GetTaskId(), req.GetExecuteId(), req.GetUiAgentComponent().GetParentExecuteId(), req.GetUser(),
	)
	taskInfo, err := convertToTaskInfo(&req)
	if err != nil {
		logger.Errorf("failed to convert ui agent component task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}
	logger.Infof("ui agent component task info: %s", protobuf.MarshalJSONIgnoreError(taskInfo))

	if err = uiAgentComponent.NewExecuteUIAgentComponentTaskLogic(ctx, p.svcCtx, taskInfo).Execute(); err != nil {
		logger.Errorf("failed to execute the ui agent component task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

func convertToTaskInfo(req *dispatcherpb.WorkerReq) (*commonpb.UIAgentComponentTaskInfo, error) {
	if req == nil {
		return nil, errors.New("the worker request is null")
	}

	componentData := req.GetNodeData().GetUiAgentComponent()
	if componentData == nil {
		return nil, errors.Errorf("the execution data is null, execute_type: %s", req.GetExecuteType())
	}

	componentWorkerInfo := req.GetUiAgentComponent()
	if componentWorkerInfo == nil {
		return nil, errors.Errorf("the ui agent component worker info is null, execute_type: %s", req.GetExecuteType())
	}

	var times int32 = 1
	if componentWorkerInfo.GetTimes() > 1 {
		times = componentWorkerInfo.GetTimes()
	}

	info := &commonpb.UIAgentComponentTaskInfo{
		TaskId:          req.GetTaskId(),
		ExecuteId:       req.GetExecuteId(),
		ParentExecuteId: componentWorkerInfo.GetParentExecuteId(), // 父执行ID有可能为空
		ProjectId:       req.GetProjectId(),
		TriggerMode:     req.GetTriggerMode(),
		TriggerRule:     req.GetTriggerRule(),

		ComponentId:       componentData.GetComponentId(),
		ComponentName:     componentData.GetName(),
		ApplicationConfig: componentData.GetApplicationConfig(),
		Mode:              componentData.GetMode(),
		Steps:             componentData.GetSteps(),
		Expectation:       componentData.GetExpectation(),
		Variables:         componentData.GetVariables(),

		ExecuteType: componentWorkerInfo.GetExecuteType(),
		Device:      componentWorkerInfo.GetDevice(),
		Reinstall:   componentWorkerInfo.GetReinstall(),
		Restart:     componentWorkerInfo.GetRestart(),
		ReferenceId: componentWorkerInfo.GetReferenceId(),
		Times:       times, // 注：正常情况下，`uaworker`不需要关心此字段

		ExecutedBy: req.GetUserId(),
	}
	if err := info.ValidateAll(); err != nil {
		return nil, errors.Errorf(
			"failed to validate the ui agent component task info, info: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(info), err,
		)
	}

	return info, nil
}
